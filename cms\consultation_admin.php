<?php
session_start();
include('../database/dbconnection.php');

// Check if admin is logged in (you may need to adjust this based on your admin authentication)
if (!isset($_SESSION['AdminId'])) {
    header('Location: login.php');
    exit;
}

$obj = new main();
$obj->connection();

// Handle actions
$action = $_GET['action'] ?? 'dashboard';
$message = '';

if ($_POST) {
    switch ($action) {
        case 'update_booking_status':
            $bookingId = intval($_POST['bookingId']);
            $status = $_POST['status'];
            $notes = $_POST['notes'] ?? '';
            
            $updateQuery = "UPDATE consultation_bookings SET BookingStatus = ?, Notes = ? WHERE BookingId = ?";
            $result = $obj->MysqliUpdate($updateQuery, "ssi", array($status, $notes, $bookingId));
            
            if ($result) {
                $message = "Booking status updated successfully!";
            } else {
                $message = "Error updating booking status.";
            }
            break;
            
        case 'add_doctor':
            $doctorName = $_POST['doctorName'];
            $qualification = $_POST['qualification'];
            $specialization = $_POST['specialization'];
            $experience = intval($_POST['experience']);
            $consultationFee = floatval($_POST['consultationFee']);
            $phoneNumber = $_POST['phoneNumber'];
            $email = $_POST['email'];
            $description = $_POST['description'];
            
            $insertQuery = "INSERT INTO doctors (DoctorName, Qualification, Specialization, Experience, ConsultationFee, PhoneNumber, Email, Description) VALUES (?, ?, ?, ?, ?, ?, ?, ?)";
            $result = $obj->MysqliInsert($insertQuery, "sssiisss", array($doctorName, $qualification, $specialization, $experience, $consultationFee, $phoneNumber, $email, $description));
            
            if ($result) {
                $message = "Doctor added successfully!";
            } else {
                $message = "Error adding doctor.";
            }
            break;
    }
}

// Get statistics
$totalBookings = $obj->MysqliSelect1("SELECT COUNT(*) as count FROM consultation_bookings", array("count"), "", array())[0]['count'];
$pendingBookings = $obj->MysqliSelect1("SELECT COUNT(*) as count FROM consultation_bookings WHERE BookingStatus = 'Pending'", array("count"), "", array())[0]['count'];
$confirmedBookings = $obj->MysqliSelect1("SELECT COUNT(*) as count FROM consultation_bookings WHERE BookingStatus = 'Confirmed'", array("count"), "", array())[0]['count'];
$totalDoctors = $obj->MysqliSelect1("SELECT COUNT(*) as count FROM doctors WHERE IsActive = 1", array("count"), "", array())[0]['count'];

// Get recent bookings
$recentBookings = $obj->MysqliSelect1("
    SELECT cb.*, d.DoctorName 
    FROM consultation_bookings cb 
    JOIN doctors d ON cb.DoctorId = d.DoctorId 
    ORDER BY cb.CreatedAt DESC 
    LIMIT 10", 
    array("BookingId", "BookingNumber", "DoctorName", "PatientName", "PatientPhone", "ConsultationDate", "ConsultationTime", "BookingStatus", "PaymentStatus", "CreatedAt"), "", array());

// Get all doctors
$doctors = $obj->MysqliSelect1("SELECT * FROM doctors WHERE IsActive = 1 ORDER BY DoctorName", 
    array("DoctorId", "DoctorName", "Qualification", "Specialization", "Experience", "ConsultationFee", "PhoneNumber", "Email"), "", array());
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Consultation Management - My Nutrify Admin</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        .sidebar {
            min-height: 100vh;
            background: #2c3e50;
        }
        .sidebar .nav-link {
            color: #ecf0f1;
            padding: 15px 20px;
        }
        .sidebar .nav-link:hover, .sidebar .nav-link.active {
            background: #34495e;
            color: #fff;
        }
        .main-content {
            background: #f8f9fa;
            min-height: 100vh;
        }
        .stat-card {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 10px;
            padding: 20px;
            margin-bottom: 20px;
        }
        .stat-card.success {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
        }
        .stat-card.warning {
            background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
        }
        .stat-card.info {
            background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
        }
        .booking-card {
            border-left: 4px solid #85B26F;
            margin-bottom: 15px;
        }
        .status-badge {
            font-size: 0.8rem;
            padding: 4px 8px;
        }
    </style>
</head>
<body>
    <div class="container-fluid">
        <div class="row">
            <!-- Sidebar -->
            <div class="col-md-2 sidebar">
                <div class="p-3">
                    <h5 class="text-white">Consultation Admin</h5>
                </div>
                <nav class="nav flex-column">
                    <a class="nav-link <?php echo $action == 'dashboard' ? 'active' : ''; ?>" href="?action=dashboard">
                        <i class="fas fa-tachometer-alt me-2"></i> Dashboard
                    </a>
                    <a class="nav-link <?php echo $action == 'bookings' ? 'active' : ''; ?>" href="?action=bookings">
                        <i class="fas fa-calendar-check me-2"></i> Bookings
                    </a>
                    <a class="nav-link <?php echo $action == 'doctors' ? 'active' : ''; ?>" href="?action=doctors">
                        <i class="fas fa-user-md me-2"></i> Doctors
                    </a>
                    <a class="nav-link <?php echo $action == 'settings' ? 'active' : ''; ?>" href="?action=settings">
                        <i class="fas fa-cog me-2"></i> Settings
                    </a>
                    <a class="nav-link" href="../index.php">
                        <i class="fas fa-home me-2"></i> Back to Site
                    </a>
                </nav>
            </div>

            <!-- Main Content -->
            <div class="col-md-10 main-content">
                <div class="p-4">
                    <?php if ($message): ?>
                        <div class="alert alert-info alert-dismissible fade show" role="alert">
                            <?php echo htmlspecialchars($message); ?>
                            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                        </div>
                    <?php endif; ?>

                    <?php if ($action == 'dashboard'): ?>
                        <h2>Consultation Dashboard</h2>
                        
                        <!-- Statistics Cards -->
                        <div class="row">
                            <div class="col-md-3">
                                <div class="stat-card">
                                    <div class="d-flex justify-content-between">
                                        <div>
                                            <h3><?php echo $totalBookings; ?></h3>
                                            <p class="mb-0">Total Bookings</p>
                                        </div>
                                        <div class="align-self-center">
                                            <i class="fas fa-calendar-alt fa-2x"></i>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="stat-card warning">
                                    <div class="d-flex justify-content-between">
                                        <div>
                                            <h3><?php echo $pendingBookings; ?></h3>
                                            <p class="mb-0">Pending Bookings</p>
                                        </div>
                                        <div class="align-self-center">
                                            <i class="fas fa-clock fa-2x"></i>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="stat-card success">
                                    <div class="d-flex justify-content-between">
                                        <div>
                                            <h3><?php echo $confirmedBookings; ?></h3>
                                            <p class="mb-0">Confirmed Bookings</p>
                                        </div>
                                        <div class="align-self-center">
                                            <i class="fas fa-check-circle fa-2x"></i>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="stat-card info">
                                    <div class="d-flex justify-content-between">
                                        <div>
                                            <h3><?php echo $totalDoctors; ?></h3>
                                            <p class="mb-0">Active Doctors</p>
                                        </div>
                                        <div class="align-self-center">
                                            <i class="fas fa-user-md fa-2x"></i>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Recent Bookings -->
                        <div class="card">
                            <div class="card-header">
                                <h5>Recent Bookings</h5>
                            </div>
                            <div class="card-body">
                                <?php foreach ($recentBookings as $booking): ?>
                                    <div class="card booking-card">
                                        <div class="card-body">
                                            <div class="row">
                                                <div class="col-md-8">
                                                    <h6><?php echo htmlspecialchars($booking['PatientName']); ?></h6>
                                                    <p class="mb-1">
                                                        <strong>Doctor:</strong> <?php echo htmlspecialchars($booking['DoctorName']); ?><br>
                                                        <strong>Date:</strong> <?php echo date('M d, Y', strtotime($booking['ConsultationDate'])); ?> 
                                                        at <?php echo date('g:i A', strtotime($booking['ConsultationTime'])); ?><br>
                                                        <strong>Phone:</strong> <?php echo htmlspecialchars($booking['PatientPhone']); ?>
                                                    </p>
                                                </div>
                                                <div class="col-md-4 text-end">
                                                    <span class="badge bg-<?php echo $booking['BookingStatus'] == 'Confirmed' ? 'success' : ($booking['BookingStatus'] == 'Pending' ? 'warning' : 'secondary'); ?> status-badge">
                                                        <?php echo $booking['BookingStatus']; ?>
                                                    </span><br>
                                                    <small class="text-muted"><?php echo $booking['BookingNumber']; ?></small>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                <?php endforeach; ?>
                            </div>
                        </div>

                    <?php elseif ($action == 'bookings'): ?>
                        <h2>All Bookings</h2>
                        <div class="card">
                            <div class="card-body">
                                <div class="table-responsive">
                                    <table class="table table-striped">
                                        <thead>
                                            <tr>
                                                <th>Booking #</th>
                                                <th>Patient</th>
                                                <th>Doctor</th>
                                                <th>Date & Time</th>
                                                <th>Status</th>
                                                <th>Payment</th>
                                                <th>Actions</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <?php 
                                            $allBookings = $obj->MysqliSelect1("
                                                SELECT cb.*, d.DoctorName 
                                                FROM consultation_bookings cb 
                                                JOIN doctors d ON cb.DoctorId = d.DoctorId 
                                                ORDER BY cb.CreatedAt DESC", 
                                                array("BookingId", "BookingNumber", "DoctorName", "PatientName", "PatientPhone", "ConsultationDate", "ConsultationTime", "BookingStatus", "PaymentStatus"), "", array());
                                            
                                            foreach ($allBookings as $booking): ?>
                                                <tr>
                                                    <td><?php echo $booking['BookingNumber']; ?></td>
                                                    <td>
                                                        <?php echo htmlspecialchars($booking['PatientName']); ?><br>
                                                        <small><?php echo $booking['PatientPhone']; ?></small>
                                                    </td>
                                                    <td><?php echo htmlspecialchars($booking['DoctorName']); ?></td>
                                                    <td>
                                                        <?php echo date('M d, Y', strtotime($booking['ConsultationDate'])); ?><br>
                                                        <?php echo date('g:i A', strtotime($booking['ConsultationTime'])); ?>
                                                    </td>
                                                    <td>
                                                        <span class="badge bg-<?php echo $booking['BookingStatus'] == 'Confirmed' ? 'success' : ($booking['BookingStatus'] == 'Pending' ? 'warning' : 'secondary'); ?>">
                                                            <?php echo $booking['BookingStatus']; ?>
                                                        </span>
                                                    </td>
                                                    <td>
                                                        <span class="badge bg-<?php echo $booking['PaymentStatus'] == 'Paid' ? 'success' : 'warning'; ?>">
                                                            <?php echo $booking['PaymentStatus']; ?>
                                                        </span>
                                                    </td>
                                                    <td>
                                                        <button class="btn btn-sm btn-primary" onclick="updateBookingStatus(<?php echo $booking['BookingId']; ?>, '<?php echo $booking['BookingStatus']; ?>')">
                                                            Update
                                                        </button>
                                                    </td>
                                                </tr>
                                            <?php endforeach; ?>
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>

                    <?php elseif ($action == 'doctors'): ?>
                        <div class="d-flex justify-content-between align-items-center mb-4">
                            <h2>Doctors Management</h2>
                            <button class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#addDoctorModal">
                                <i class="fas fa-plus"></i> Add Doctor
                            </button>
                        </div>
                        
                        <div class="row">
                            <?php foreach ($doctors as $doctor): ?>
                                <div class="col-md-6 mb-4">
                                    <div class="card">
                                        <div class="card-body">
                                            <h5><?php echo htmlspecialchars($doctor['DoctorName']); ?></h5>
                                            <p class="text-muted"><?php echo htmlspecialchars($doctor['Qualification']); ?></p>
                                            <p><strong>Specialization:</strong> <?php echo htmlspecialchars($doctor['Specialization']); ?></p>
                                            <p><strong>Experience:</strong> <?php echo $doctor['Experience']; ?> years</p>
                                            <p><strong>Fee:</strong> ₹<?php echo number_format($doctor['ConsultationFee'], 0); ?></p>
                                            <p><strong>Contact:</strong> <?php echo $doctor['PhoneNumber']; ?></p>
                                        </div>
                                    </div>
                                </div>
                            <?php endforeach; ?>
                        </div>

                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>

    <!-- Add Doctor Modal -->
    <div class="modal fade" id="addDoctorModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">Add New Doctor</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <form method="POST" action="?action=add_doctor">
                    <div class="modal-body">
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label class="form-label">Doctor Name *</label>
                                <input type="text" class="form-control" name="doctorName" required>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label class="form-label">Qualification *</label>
                                <input type="text" class="form-control" name="qualification" required>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label class="form-label">Specialization *</label>
                                <input type="text" class="form-control" name="specialization" required>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label class="form-label">Experience (Years) *</label>
                                <input type="number" class="form-control" name="experience" min="1" required>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label class="form-label">Consultation Fee *</label>
                                <input type="number" class="form-control" name="consultationFee" step="0.01" required>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label class="form-label">Phone Number *</label>
                                <input type="tel" class="form-control" name="phoneNumber" required>
                            </div>
                        </div>
                        <div class="mb-3">
                            <label class="form-label">Email</label>
                            <input type="email" class="form-control" name="email">
                        </div>
                        <div class="mb-3">
                            <label class="form-label">Description</label>
                            <textarea class="form-control" name="description" rows="3"></textarea>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                        <button type="submit" class="btn btn-primary">Add Doctor</button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- Update Booking Status Modal -->
    <div class="modal fade" id="updateBookingModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">Update Booking Status</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <form method="POST" action="?action=update_booking_status">
                    <div class="modal-body">
                        <input type="hidden" name="bookingId" id="updateBookingId">
                        <div class="mb-3">
                            <label class="form-label">Status</label>
                            <select class="form-control" name="status" id="updateBookingStatus">
                                <option value="Pending">Pending</option>
                                <option value="Confirmed">Confirmed</option>
                                <option value="Completed">Completed</option>
                                <option value="Cancelled">Cancelled</option>
                                <option value="Rescheduled">Rescheduled</option>
                            </select>
                        </div>
                        <div class="mb-3">
                            <label class="form-label">Notes</label>
                            <textarea class="form-control" name="notes" rows="3"></textarea>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                        <button type="submit" class="btn btn-primary">Update Status</button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        function updateBookingStatus(bookingId, currentStatus) {
            document.getElementById('updateBookingId').value = bookingId;
            document.getElementById('updateBookingStatus').value = currentStatus;
            new bootstrap.Modal(document.getElementById('updateBookingModal')).show();
        }
    </script>
</body>
</html>
