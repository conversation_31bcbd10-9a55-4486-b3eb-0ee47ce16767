<?php
header('Content-Type: application/json');
include('database/dbconnection.php');

$obj = new main();
$obj->connection();

// Get JSON input
$input = json_decode(file_get_contents('php://input'), true);

if (!isset($input['doctorId']) || !isset($input['date'])) {
    echo json_encode(['success' => false, 'message' => 'Missing required parameters']);
    exit;
}

$doctorId = intval($input['doctorId']);
$selectedDate = $input['date'];

// Validate date format and ensure it's not in the past
$dateObj = DateTime::createFromFormat('Y-m-d', $selectedDate);
$today = new DateTime();
$today->setTime(0, 0, 0);

if (!$dateObj || $dateObj < $today) {
    echo json_encode(['success' => false, 'message' => 'Invalid date selected']);
    exit;
}

// Get day of week
$dayOfWeek = $dateObj->format('l'); // Monday, Tuesday, etc.

try {
    // Get doctor availability for the selected day
    $availabilityQuery = "
        SELECT StartTime, EndTime, SlotDuration 
        FROM doctor_availability 
        WHERE DoctorId = ? AND DayOfWeek = ? AND IsActive = 1";
    
    $availability = $obj->MysqliSelect1($availabilityQuery, 
        array("StartTime", "EndTime", "SlotDuration"), "is", array($doctorId, $dayOfWeek));

    if (empty($availability)) {
        echo json_encode(['success' => false, 'message' => 'Doctor not available on this day']);
        exit;
    }

    $startTime = $availability[0]['StartTime'];
    $endTime = $availability[0]['EndTime'];
    $slotDuration = $availability[0]['SlotDuration'];

    // Get already booked slots for this doctor on this date
    $bookedSlotsQuery = "
        SELECT ConsultationTime 
        FROM consultation_bookings 
        WHERE DoctorId = ? AND ConsultationDate = ? 
        AND BookingStatus IN ('Confirmed', 'Pending')";
    
    $bookedSlots = $obj->MysqliSelect1($bookedSlotsQuery, 
        array("ConsultationTime"), "is", array($doctorId, $selectedDate));

    $bookedTimes = array();
    foreach ($bookedSlots as $slot) {
        $bookedTimes[] = $slot['ConsultationTime'];
    }

    // Generate available time slots
    $availableSlots = array();
    $currentTime = new DateTime($selectedDate . ' ' . $startTime);
    $endDateTime = new DateTime($selectedDate . ' ' . $endTime);
    
    // If the selected date is today, ensure we don't show past time slots
    $now = new DateTime();
    if ($dateObj->format('Y-m-d') === $now->format('Y-m-d')) {
        // Add 2 hours buffer for same-day bookings
        $now->add(new DateInterval('PT2H'));
        if ($currentTime < $now) {
            $currentTime = clone $now;
            // Round up to next slot time
            $minutes = $currentTime->format('i');
            $roundedMinutes = ceil($minutes / $slotDuration) * $slotDuration;
            if ($roundedMinutes >= 60) {
                $currentTime->add(new DateInterval('PT' . (60 - $minutes) . 'M'));
                $currentTime->add(new DateInterval('PT' . ($roundedMinutes - 60) . 'M'));
            } else {
                $currentTime->setTime($currentTime->format('H'), $roundedMinutes, 0);
            }
        }
    }

    while ($currentTime < $endDateTime) {
        $timeString = $currentTime->format('H:i:s');
        $displayTime = $currentTime->format('g:i A');
        
        // Check if this slot is not already booked
        if (!in_array($timeString, $bookedTimes)) {
            $availableSlots[] = array(
                'time' => $timeString,
                'display_time' => $displayTime
            );
        }
        
        // Move to next slot
        $currentTime->add(new DateInterval('PT' . $slotDuration . 'M'));
    }

    echo json_encode([
        'success' => true, 
        'slots' => $availableSlots,
        'date' => $selectedDate,
        'dayOfWeek' => $dayOfWeek
    ]);

} catch (Exception $e) {
    error_log("Error in get_available_slots.php: " . $e->getMessage());
    echo json_encode(['success' => false, 'message' => 'Error retrieving available slots']);
}
?>
