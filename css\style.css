@charset "utf-8";
/* ----------------------------------
Name: style.css
Version: 1.0
----------------------------------
Table of contents
Google Font
Body and Default Transitions
Typography
Icon
Background Color
Border Color
Margin
Padding
Custom
OWL Carousel
Button
Form Control
Portfolio and Photo Gallery
Video and Sound
Navbar
Header
Hero Section
Slider
Feature Box
Content
Team
Pricing Table
Contact
Blog
Subscribe
Counter
Clients
Timer
Testimonial
Footer
*/
/*====================================
Google Font
=====================================*/
@import url('https://fonts.googleapis.com/css2?family=Poppins:wght@400;500;600;700;800;900&amp;display=swap');
@import url('https://fonts.googleapis.com/css2?family=Outfit:wght@300;400;500;600;700;800;900&display=swap');
/*====================================
Typography
=====================================*/
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

html, body {
    width: 100%;
    height: 100%;
    margin: 0;
    padding: 0;
    overflow-x: hidden;
}

/* Ensure full screen coverage */
.container-fluid {
    width: 100% !important;
    max-width: 100% !important;
    padding-left: 0 !important;
    padding-right: 0 !important;
}

/* Override Bootstrap container for full width when needed */
.full-width {
    width: 100% !important;
    max-width: 100% !important;
    padding-left: 15px;
    padding-right: 15px;
}

body{
    color: #222;
    font-size: 14px;
    font-family: 'Poppins', sans-serif;
    line-height: normal;
    font-weight: 400;
    letter-spacing: 0.5px;
}
body, html{
    height: 100%;
}
/* heading */
h1,
h2,
h3,
h4,
h5,
h6{
    color: #222;
    margin: 0;
    padding: 0;
    font-weight: 600;
    line-height: normal;
}
:focus{
    outline: none;
}
a{
    color: #000;
    display: inline-block;
    text-decoration: none;
}
a:hover{
    color: #ec6504;
    text-decoration: none;
}
a,
a:hover{
    -webkit-transition: all 0.3s ease-in-out 0s;
    -o-transition: all 0.3s ease-in-out 0s;
    transition: all 0.3s ease-in-out 0s;
}
img {
    backface-visibility: hidden;
}
span {
    display: inline-block;
}
button{
    padding: 0;
    border: none;
    cursor: pointer;
}
button,
button:hover{
    -webkit-transition: all 0.3s ease-in-out 0s;
    -o-transition: all 0.3s ease-in-out 0s;
    transition: all 0.3s ease-in-out 0s;
}
button,
button:focus {
    outline: none;
    box-shadow: none;
}
.navbar-toggler:focus{
    box-shadow: none;
}
p{
    line-height: 25px;
    margin-bottom: 0px;
}
ul {
    list-style: none;
    margin: 0;
    padding: 0;
}
input,
select,
textarea{
    padding: 10px 15px;
    border: 1px solid #e2e2e2;
    border-radius: 5px;
}
input:focus,
select:focus,
textarea:focus {
    outline: none;
    border: 1px solid #e2e2e2;
    box-shadow: none;
}
label {
    font-weight: 600;
}
.btn:focus,
.btn:active {
    outline: none;
    box-shadow: none;
}
/* body hidden class */
body.hidden {
    overflow-y: hidden;
    padding-right: 17px;
}
/* btn style 1 css */
.btn-style1 {
    color: #fff;
    font-size: 14px;
    padding: 10px 30px;
    background-color: #305724;
    
    font-weight: 600;
    border: 2px solid #305724;
    border-radius: 25px;
}
.btn-style1:hover {
    color: #000;
    background-color: transparent;
}
/* btn style 2 css */
.btn-style2 {
    color: #fff;
    font-size: 14px;
    padding: 10px 30px;
    background-color: #222;
    
    font-weight: 600;
    border-radius: 25px;
}
.btn-style2:hover {
    color: #fff;
    background-color: #ec6504;
}
/* btn style 3 css */
.btn-style3 {
    color: #fff;
    font-size: 14px;
    padding: 10px 30px;
    background-color: #ec6504;
    
    font-weight: 600;
    border-radius: 0;
}
.btn-style3:hover {
    color: #222;
}
/* mm-fullscreen-bg css */
.mm-fullscreen-bg {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    z-index: 99;
    opacity: 0;
    visibility: hidden;
}
.mm-fullscreen-bg.active {
    opacity: 1;
    visibility: visible;
}
.mm-fullscreen-bg,
.mm-fullscreen-bg.active{
    -webkit-transition: all 0.3s ease-in-out 0s;
    -o-transition: all 0.3s ease-in-out 0s;
    transition: all 0.3s ease-in-out 0s;
}
/* section padding css */
.section-t-padding{
    padding-top: 80px;
}
.section-b-padding{
    padding-bottom: 80px;
}
.section-tb-padding{
    padding-top: 80px;
    padding-bottom: 80px;
}
.section-1t-padding{
    padding-top: 60px;
}
.section-1b-padding{
    padding-bottom: 60px;
}
.section-1tb-padding{
    padding-top: 60px;
    padding-bottom: 60px;
}
/* breadcrumb padding */
.breadcrumb-t-padding{
    padding-top: 30px;
}
.breadcrumb-b-padding{
    padding-top: 30px;
}
.breadcrumb-tb-padding{
    padding-top: 30px;
    padding-bottom: 30px;
}
@media (max-width: 767px){
    .breadcrumb-t-padding{
        padding-top: 20px;
    }
    .breadcrumb-b-padding{
        padding-bottom: 20px;
    }
    .breadcrumb-tb-padding{
        padding-top: 20px;
        padding-bottom: 20px;
    }
}
/* section title css */
.section-title h2{
    font-size: 30px;
    text-align: center;
    margin-bottom: 60px;  
    line-height: 1; 
}
/* home-1 container css */
.home-1 .container{
    max-width: 1200px;
}
/* top notification css */
/* currency css */
.top1{
    background-color: #222;
}
.top1 ul.top-home{
    display: flex;
    align-items: center;
    justify-content: space-between;
}
.top1 ul.top-home li.top-home-li .currency{
    display: flex;
    align-items: center;
}
.top1 ul.top-home li.top-home-li .currency span.currency-head{
    color: #fff;
    padding: 10px 0;
    
}
.top1 ul.top-home li.top-home-li .currency .currency-drop{
    position: relative;
}
.top1 ul.top-home li.top-home-li .currency .currency-drop .eur{
    padding: 14px 11px;
    display: flex;
    align-items: center;
    position: relative;
}
.top1 ul.top-home li.top-home-li .currency .currency-drop .eur::after{
    content: "\e604";
    font-family: "simple-line-icons";
    font-size: 9px;
    color: #fff;
    position: absolute;
    right: -3px;
    bottom: 50%;
    transform: translateY(50%);
}
.top1 ul.top-home li.top-home-li .currency .currency-drop .eur span.cur-name{
    color: #fff;
    font-size: 13px;
    margin-left: 8px;
    text-transform: uppercase;
    line-height: 1;
}
.top1 ul.top-home li.top-home-li .currency .currency-drop ul.all-currency{
    position: absolute;
    top: 100%;
    left: 0;
    width: 100%;
    background-color: #fff;
    z-index: 7;
    box-shadow: 0 0 10px 0 rgba(0, 0, 0, 0.15);
    opacity: 0;
    visibility: hidden;
}
.top1 ul.top-home li.top-home-li .currency .currency-drop:hover ul.all-currency{
    opacity: 1;
    visibility: visible;
    -webkit-transition: all 0.3s ease-in-out 0s;
    -o-transition: all 0.3s ease-in-out 0s;
    transition: all 0.3s ease-in-out 0s;
}
.top1 ul.top-home li.top-home-li .currency .currency-drop ul.all-currency li{
    padding: 8px 15px;
}
.top1 ul.top-home li.top-home-li .currency .currency-drop ul.all-currency li a{
    display: flex;
    align-items: center;
}
.top1 ul.top-home li.top-home-li .currency .currency-drop ul.all-currency li a span{
    font-size: 13px;
    margin-left: 5px;
    text-transform: uppercase;
    line-height: 1;
}
/* top search css */
.top1 ul.top-home li.top-home-li .r-search{
    position: relative;
    display: none;
}
.top1 ul.top-home li.top-home-li .r-search a.search-popuup{
    color: #fff;
    font-size: 18px;
    font-weight: 600;
    line-height: 0;
}
.top1 ul.top-home li.top-home-li .r-search .modal-dialog{
    margin: 0px;
    max-width: 100%;
}
.top1 ul.top-home li.top-home-li .r-search .modal-content{
    background-color: #ec6504;
    border: none;
    border-radius: 0px;
}
.top1 ul.top-home li.top-home-li .r-search .modal-body{
    display: flex;
    align-items: center;
    padding: 10px 15px;
}
.top1 ul.top-home li.top-home-li .r-search .m-drop-search{
    width: 100%;
    position: relative;
    border-radius: 25px;
    overflow: hidden;
}
.top1 ul.top-home li.top-home-li .r-search .m-drop-search input{
    height: 40px;
    border-radius: 25px;
    border: 1px solid #c7c7c7;
    width: 100%;
}
.top1 ul.top-home li.top-home-li .r-search .m-drop-search .search-btn{
    color: #fff;
    font-size: 16px;
    position: absolute;
    top: 0px;
    right: 0;
    height: 100%;
    width: 40px;
    background-color: #305724;
    border: none;
    border-radius: 100%;
    line-height: 0;
    display: flex;
    align-items: center;
    justify-content: center;
}
.top1 ul.top-home li.top-home-li .r-search button.close{
    display: block;
    color: #fff;
    font-size: 16px;
    margin-left: 10px;
    opacity: 1;
    line-height: 0;
    background-color: transparent;
}
/* top offer css */
.top1 ul.top-home li.top-home-li .top-content p{
    color: #fff;
    font-size: 14px;
    padding: 8px 0;
    
}
.top1 ul.top-home li.top-home-li .top-content p span.top-c{
    font-weight: 600;
    animation: blinker .7s infinite;
}
@keyframes blinker{
    50%{
        color: #ec6504;
    }
}
/* header top css */
.header-main-area{
    background-color: #fff;
}
.header-main-area.is-sticky{
    background-color: #ffffff;
}
@media (max-width: 1199px){
.header-main-area.is-sticky{
  background-color: #ffffff;
}
}
.header-main-area.is-sticky{
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    background-color: #fff;
    z-index: 8;
    width: 100%;
    box-shadow: 0 0 15px rgb(0 0 0 / 15%);
    -webkit-box-shadow: 0 0 15px rgb(0 0 0 / 15%);
    -moz-box-shadow: 0 0 15px rgba(0, 0, 0, 0.15);
    -ms-box-shadow: 0 0 15px rgba(0, 0, 0, 0.15);
    background-color: #fff;
    animation: smoothScroll 1s forwards;
}
@keyframes smoothScroll {
  0% {
    transform: translateY(-150px);
  }
  100% {
    transform: translateY(0px);
  }
}
.header-main-area .header-main{
    display: flex;
    align-items: center;
    
}
.header-main-area .header-main .header-element.logo {
    width: 33.33%;
}
.header-main-area .header-main .header-element.search-wrap{
    width: 33.33%;
}
.header-main-area .header-main .search-wrap {
    position: relative;
}
.header-main-area .header-main .search-wrap input{
    width: 100%;
    height: 40px;
    border: 1px solid #c7c7c7;
    border-radius: 25px;
}
.header-main-area .header-main .search-wrap a{
    color: #fff;
    font-size: 16px;
    position: absolute;
    top: 0;
    right: 0px;
    width: 40px;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: #000;
    border: none;
    border-radius: 50%;
    line-height: 0;
}
.header-main-area .header-main .search-wrap a:hover{
    background-color: #ec6504;
}
.header-main-area .header-main .header-element.right-block-box {
    width: 33.33%;
}
.header-main-area .header-main .header-element.right-block-box ul.shop-element li.nav-toggler{
    display: none;
    line-height: 0;
}
/* navbar-toggler css */
button.navbar-toggler{
    padding: 0;
    border: none;
}
button.navbar-toggler span.line{
    position: relative;
    margin-top: 8px;
    margin-bottom: 8px;
}
button.navbar-toggler span.line,
button.navbar-toggler span.line::before,
button.navbar-toggler span.line::after{
    width: 20px;
    height: 2px;
    display: block;
    background-color: #000;
    -webkit-transition: all 0.3s ease-in-out 0s;
    -o-transition: all 0.3s ease-in-out 0s;
    transition: all 0.3s ease-in-out 0s;
}
button.navbar-toggler span.line::before,
button.navbar-toggler span.line::after{
    content: '';
    position: absolute;
}
button.navbar-toggler span.line::before{
    top: 8px;
}
button.navbar-toggler span.line::after{
    top: -8px;
}
/* right block css */
.right-block-box {
    display: flex;
    justify-content: flex-end;
}
.right-block-box ul.shop-element {
    display: flex;
    align-items: center;
    justify-content: flex-end;
    width: 100%;
}
.right-block-box ul.shop-element li.side-wrap {
    position: relative;
    margin-left: 30px;
}
.right-block-box ul.shop-element li.side-wrap:first-child{
    margin-left: 0;
}
/* user account css */
.right-block-box ul.shop-element li.user-wrap .acc-desk{
    display: flex;
    align-items: center;
}
.right-block-box ul.shop-element li.user-wrap .acc-desk .user-icon a.user-icon-desk{
    display: block;
    font-size: 30px;
    line-height: 0;
}
.right-block-box ul.shop-element li.user-wrap .acc-desk .user-icon a.user-icon-desk,
.right-block-box ul.shop-element li.user-wrap .acc-desk:hover .user-icon a.user-icon-desk{
    -webkit-transition: all 0.3s ease-in-out 0s;
    -o-transition: all 0.3s ease-in-out 0s;
    transition: all 0.3s ease-in-out 0s;
}
.right-block-box ul.shop-element li.user-wrap .acc-desk .user-info{
    display: flex;
    flex-direction: column;
    margin-left: 15px;
}
.right-block-box ul.shop-element li.user-wrap .acc-desk .user-info span.acc-title{
    color: #ec6504;
    font-size: 13px;
    text-transform: uppercase;
    font-weight: 600;
    line-height: 1;
}
.right-block-box ul.shop-element li.user-wrap .acc-desk .user-info .account-login{
    display: flex;
    margin-top: 7px;
}
.right-block-box ul.shop-element li.user-wrap .acc-desk .user-info .account-login a{
    color: #000;
    font-size: 11px;
    padding-right: 5px;
    margin-right: 5px;
    border-right: 1px solid #c3c3c3;
    line-height: 1;
}
.right-block-box ul.shop-element li.user-wrap .acc-desk .user-info .account-login a:last-child{
    padding-right: 0px;
    margin-right: 0px;
    border-right: none;
}
.right-block-box ul.shop-element li.user-wrap .acc-desk .user-info .account-login a:hover{
    color: #ec6504;
}
.right-block-box ul.shop-element li.user-wrap .acc-mob{
    display: none;
}
/* wishlist css */
.wishlist-wrap a.header-wishlist{
    display: block;
    position: relative;
}
.wishlist-wrap a.header-wishlist span.wishlist-icon{
    display: block;
    font-size: 30px;
}
.wishlist-wrap a.header-wishlist span.wishlist-counter{
    color: #fff;
    font-size: 12px;
    position: absolute;
    left: 20px;
    top: 5px;
    width: 20px;
    height: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
    text-align: center;
    background-color: #ec6504;
    font-weight: 500;
    border-radius: 100%;
}
/* cart css */
.shopping-widget .shopping-cart a.cart-count{
    display: block;
}
.shopping-widget .shopping-cart a.cart-count span.cart-icon-wrap{
    display: block;
    position: relative;
}
.shopping-widget .shopping-cart a.cart-count span.cart-icon-wrap span.cart-icon{
    display: block;
    font-size: 30px;
}
.wishlist-wrap a.header-wishlist span.wishlist-icon i,
.shopping-widget .shopping-cart a.cart-count span.cart-icon-wrap span.cart-icon i{
    margin-right: 10px;
}
.shopping-widget .shopping-cart a.cart-count span.cart-icon-wrap span.bigcounter{
    color: #fff;
    font-size: 12px;
    position: absolute;
    left: 20px;
    top: 5px;
    width: 20px;
    height: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
    text-align: center;
    background-color: #ec6504;
    font-weight: 500;
    border-radius: 100%;
}
/* mini cart css */
.mini-cart{
    position: fixed;
    top: 0;
    right: -300px;
    width: 300px;
    height: 100%;
    background-color: #fff;
    box-shadow: 0 16px 26px -10px rgba(0, 0, 0, 0.56), 0 4px 25px 0px rgba(0, 0, 0, 0.12), 0 8px 10px -5px rgba(0, 0, 0, 0.2);
    z-index: 100;
    opacity: 0;
    visibility: hidden;
}
.mini-cart.show{
    right: 0px;
    opacity: 1;
    visibility: visible;
}
.mini-cart,
.mini-cart.show{
    -webkit-transition: all 0.3s ease-in-out 0s;
    -o-transition: all 0.3s ease-in-out 0s;
    transition: all 0.3s ease-in-out 0s;
}
.mini-cart a.shopping-cart-close{
    position: absolute;
    top: 15px;
    right: 10px;
    line-height: 0px;
}
.mini-cart .cart-item-title p{
    padding: 10px 15px;
    font-weight: 600;
}
.mini-cart .cart-item-title p span.cart-count-item{
    color: #ec6504;
}
.mini-cart ul.cart-item-loop,
.all-filter .categories-page-filter ul.all-option,
.all-filter .filter-tag ul.all-tag{
    max-height: calc(100% - 219px);
    overflow-y: auto;
    border-top: 1px solid #eee;
    scrollbar-width: thin;
    -webkit-scrollbar-width: thin;
}
.mini-cart ul.cart-item-loop::-webkit-scrollbar,
.all-filter .categories-page-filter ul.all-option::-webkit-scrollbar,
.all-filter .filter-tag ul.all-tag::-webkit-scrollbar{
    width: 4px;
}
.mini-cart ul.cart-item-loop::-webkit-scrollbar-track,
.all-filter .categories-page-filter ul.all-option::-webkit-scrollbar-track,
.all-filter .filter-tag ul.all-tag::-webkit-scrollbar-track{
    background-color: #eee;
}
.mini-cart ul.cart-item-loop ul.all-option::-webkit-scrollbar-thumb,
.all-filter .categories-page-filter ul.all-option::-webkit-scrollbar-thumb,
.all-filter .filter-tag ul.all-tag::-webkit-scrollbar-thumb{
    background-color: #c1c1c1;
}
.mini-cart ul.cart-item-loop::-webkit-scrollbar-thumb:hover,
.all-filter .categories-page-filter ul.all-option::-webkit-scrollbar-thumb:hover,
.all-filter .pro-size ul.all-size::-webkit-scrollbar-thumb:hover,
.all-filter .filter-tag ul.all-tag::-webkit-scrollbar-thumb:hover{
    background-color: #ec6504;
}
.mini-cart ul.cart-item-loop li.cart-item{
    display: flex;
    padding: 15px;
    border-top: 1px solid #eee;
}
.mini-cart ul.cart-item-loop li.cart-item:first-child{
    border-top: none;
}
.mini-cart ul.cart-item-loop li.cart-item .cart-img{
    width: 60px;
}
.mini-cart ul.cart-item-loop li.cart-item .cart-title{
    width: calc(100% - 75px);
    margin-left: 15px;
}
.mini-cart ul.cart-item-loop li.cart-item .cart-title h6{
    font-size: 14px;
    width: 100%;
    font-weight: 400;
}
.mini-cart ul.cart-item-loop li.cart-item .cart-title h6 a{
    display: block;
    width: 100%;
    white-space: nowrap;
    text-overflow: ellipsis;
    overflow: hidden;
}
.mini-cart ul.cart-item-loop li.cart-item .cart-title .cart-pro-info{
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-top: 9px;
    line-height: 1;
}
.mini-cart ul.cart-item-loop li.cart-item .cart-title .cart-pro-info .cart-qty-price span.price-box{
    font-size: 16px;
    font-weight: 600;
}
.mini-cart ul.subtotal-title-area{
    position: absolute;
    bottom: 0;
    width: 100%;
    background-color: #fff;
}
.mini-cart ul.subtotal-title-area li.subtotal-info{
    padding: 10px 15px;
    border-top: 1px solid #eee;
}
.mini-cart ul.subtotal-title-area li.subtotal-info .subtotal-titles{
    display: flex;
    align-items: center;
    justify-content: space-between;
    line-height: 1;
}
.mini-cart ul.subtotal-title-area li.subtotal-info .subtotal-titles h6{
    font-size: 14px;
    font-weight: 600;
}
.mini-cart ul.subtotal-title-area li.subtotal-info .subtotal-titles .subtotal-price{
    color: #ec6504;
    margin-top: 0;
    font-weight: 600;
}
.mini-cart ul.subtotal-title-area li.mini-cart-btns{
    padding: 15px;
    border-top: 1px solid #eee;
}
.mini-cart ul.subtotal-title-area li.mini-cart-btns .cart-btns{
    display: flex;
    flex-direction: column;
}
.mini-cart ul.subtotal-title-area li.mini-cart-btns .cart-btns a{
    width: 100%;
    margin-top: 15px;
}
.mini-cart ul.subtotal-title-area li.mini-cart-btns .cart-btns a:first-child{
    margin-top: 0;
}
/* header-bottom css */
.header-bottom-area {
    background-color: #fff;
}
.header-bottom-area.mobile {
    display: none;
}
.header-bottom-area .main-menu-area{
    position: relative;
    border-top: 1px solid #ddd;
}
.header-bottom-area .main-menu-area .main-navigation{
    display: flex;
    align-items: center;
    justify-content: space-between;
}
.header-bottom-area .main-menu-area .main-navigation .navbar-collapse{
    width: 75%;
}
.header-bottom-area .main-menu-area .main-navigation .img-hotline{
    width: 25%;
}
/* box-header css */
.main-menu-area .main-navigation .box-header{
    display: none;
    position: absolute;
    top: 15px;
    right: 15px;
    z-index: 1;
    line-height: 0;
}
.main-menu-area .main-navigation .box-header button{
    font-size: 18px;
    background-color: transparent;
    line-height: 0;
}
/* main-menu css */
.megamenu-content .mainwrap ul.main-menu {
    display: flex;
}
.megamenu-content .mainwrap ul.main-menu li.menu-link a.link-title{
    display: block;
    position: relative;
    padding: 15px 20px;
    display: flex;
    align-items: center;
    font-weight: 500;
    font-family: 'Outfit', sans-serif;
}
.megamenu-content .mainwrap ul.main-menu li.menu-link:first-child a.link-title {
    padding-left: 0px;
}
.megamenu-content .mainwrap ul.main-menu li.menu-link a.link-title i{
    font-size: 14px;
    margin-left: 4px;
}
.megamenu-content .mainwrap ul.main-menu li.menu-link a.link-title span.sp-link-title {
    display: flex;
    align-items: center;
    position: relative;
    font-family: 'Outfit', sans-serif;
    font-weight: 500;
}
/* Offers menu item styling - using more compatible approach */
.megamenu-content .mainwrap ul.main-menu li.menu-link.offers-menu a.link-title span.sp-link-title {
    color: #ff6b35;
}

.megamenu-content .mainwrap ul.main-menu li.menu-link a.link-title span.sp-link-title span.hot{
    color: #fff;
    font-size: 8px;
    padding: 2px 5px;
    position: absolute;
    right: 5px;
    top: -10px;
    background-color: #ff4444;
    transform: translateX(100%);
    text-transform: uppercase;
    font-weight: 600;
    line-height: 1;
    font-family: 'Outfit', sans-serif;
    border-radius: 2px;
}
.megamenu-content .mainwrap ul.main-menu li.menu-link a.link-title span.sp-link-title span.hot::before{
    content: "";
    position: absolute;
    bottom: -3px;
    left: 0;
    border-left: 3px solid #ff4444;
    border-top: 3px solid transparent;
    border-bottom: 3px solid transparent;
}
.megamenu-content .mainwrap ul.main-menu li.menu-link a.link-title.link-title-lg{
    display: none;
}
.megamenu-content .mainwrap ul.main-menu li.menu-link a.link-title::after{
    content: '';
    position: absolute;
    right: 0px;
    bottom: 50%;
    transform: translateY(50%);
    width: 1px;
    height: 25px;
    background-color: #505050;
    opacity: 0.1;
}
.megamenu-content .mainwrap ul.main-menu li.menu-link:last-child a.link-title::after{
    display: none;
}
.megamenu-content .mainwrap ul.main-menu li.menu-link ul.dropdown-submenu.sub-menu.collapse:not(.show){
    display: block;
}
.megamenu-content .mainwrap ul.main-menu li.menu-link ul.dropdown-submenu.mega-menu.collapse:not(.show),
.megamenu-content .mainwrap ul.main-menu li.menu-link ul.dropdown-submenu.banner-menu.collapse:not(.show){
    display: flex;
    flex-wrap: wrap
}
.megamenu-content .mainwrap ul.main-menu li.menu-link ul.dropdown-submenu{
    position: absolute;
    top: calc(100% + 20px);
    padding: 15px 0px;
    margin: 0 auto;
    background-color: #fff;
    z-index: 11;
    opacity: 0;
    visibility: hidden;
    box-shadow: 0 0 15px rgba(0, 0, 0, 0.15);
}
.megamenu-content .mainwrap ul.main-menu li.menu-link:hover ul.dropdown-submenu{
    top: calc(100% + 0px);
    opacity: 1;
    visibility: visible;
}
.megamenu-content .mainwrap ul.main-menu li.menu-link ul.dropdown-submenu,
.megamenu-content .mainwrap ul.main-menu li.menu-link:hover ul.dropdown-submenu{
    -webkit-transition: all 0.3s ease-in-out 0s;
    -o-transition: all 0.3s ease-in-out 0s;
    transition: all 0.3s ease-in-out 0s;
}
.megamenu-content .mainwrap ul.main-menu li.menu-link ul.dropdown-submenu.sub-menu{
    left: auto;
    min-width:  145px;
}
.megamenu-content .mainwrap ul.main-menu li.menu-link ul.dropdown-submenu.mega-menu{
    width: 100%;
    left: 0;
}
.megamenu-content .mainwrap ul.main-menu li.menu-link ul.dropdown-submenu.banner-menu{
    width: 100%;
    left: 0px;
}
.megamenu-content .mainwrap ul.main-menu li.menu-link ul.dropdown-submenu li.submenu-li{
    margin-top: 10px;
}
.megamenu-content .mainwrap ul.main-menu li.menu-link ul.dropdown-submenu li.submenu-li:first-child{
    margin-top: 0px;
}
.megamenu-content .mainwrap ul.main-menu li.menu-link ul.dropdown-submenu li.submenu-li a.submenu-link{
    width: 100%;
}
.megamenu-content .mainwrap ul.main-menu li.menu-link ul.dropdown-submenu.mega-menu li.megamenu-li{
    width: 25%;
    padding: 0px 15px;
}
.megamenu-content .mainwrap ul.main-menu li.menu-link ul.dropdown-submenu.mega-menu li.megamenu-li h2.sublink-title{
    font-size: 16px;
    margin-bottom: 10px;
}
.megamenu-content .mainwrap ul.main-menu li.menu-link ul.dropdown-submenu.mega-menu li.megamenu-li a.sublink-title.sublink-title-lg{
    display: none;
}
.megamenu-content .mainwrap ul.main-menu li.menu-link ul.dropdown-submenu.mega-menu li.megamenu-li ul.dropdown-supmenu.collapse:not(.show){
    display: block;
}
.megamenu-content .mainwrap ul.main-menu li.menu-link ul.dropdown-submenu.mega-menu li.megamenu-li ul.dropdown-supmenu li.supmenu-li{
    margin-top: 10px;
}
.megamenu-content .mainwrap ul.main-menu li.menu-link ul.dropdown-submenu.banner-menu li.menu-banner{
    width: 33.33%;
    padding: 0px 15px;
}
.megamenu-content .mainwrap ul.main-menu li.menu-link ul.dropdown-submenu.banner-menu li.menu-banner a{
    position: relative;
    overflow: hidden;
    display: block;
}
.megamenu-content .mainwrap ul.main-menu li.menu-link ul.dropdown-submenu.banner-menu li.menu-banner a.menu-banner-img:hover img{
    transform: scale(1.1);
}
.megamenu-content .mainwrap ul.main-menu li.menu-link ul.dropdown-submenu.banner-menu li.menu-banner a.menu-banner-img img,
.megamenu-content .mainwrap ul.main-menu li.menu-link ul.dropdown-submenu.banner-menu li.menu-banner a.menu-banner-img:hover img{
    -webkit-transition: all 0.3s ease-in-out 0s;
    -o-transition: all 0.3s ease-in-out 0s;
    transition: all 0.3s ease-in-out 0s;
}
.megamenu-content .mainwrap ul.main-menu li.menu-link ul.dropdown-submenu.banner-menu li.menu-banner a.menu-banner-title{
    margin-top: 10px;
    text-align: center;
}
/* blog menu */
.megamenu-content .mainwrap ul.main-menu li.menu-link ul.dropdown-submenu.sub-menu li.submenu-li{
    position: relative;
    padding: 0px 15px;
}
.megamenu-content .mainwrap ul.main-menu li.menu-link ul.dropdown-submenu.sub-menu li.submenu-li a.g-l-link{
    width: 100%;
}
.megamenu-content .mainwrap ul.main-menu li.menu-link ul.dropdown-submenu.sub-menu li.submenu-li a.g-l-link i{
    float: right;
    font-size: 17px;
}
.megamenu-content .mainwrap ul.main-menu li.menu-link ul.dropdown-submenu.sub-menu li.submenu-li a.sub-link{
    display: none;
}
.megamenu-content .mainwrap ul.main-menu li.menu-link ul.dropdown-submenu.sub-menu li.submenu-li ul.blog-style-1.collapse:not(.show){
    display: block;
}
.megamenu-content .mainwrap ul.main-menu li.menu-link ul.dropdown-submenu.sub-menu li.submenu-li ul.blog-style-1{
    position: absolute;
    top: 0px;
    right: 0px;
    transform: translateX(100%);
    min-width: 145px;
    padding: 10px 0px;
    background-color: #fff;
    box-shadow: 0 0 15px rgb(0 0 0 / 15%);
    opacity: 0;
    visibility: hidden;
    -webkit-transition: all 0.3s ease-in-out 0s;
    -o-transition: all 0.3s ease-in-out 0s;
    transition: all 0.3s ease-in-out 0s;
}
.megamenu-content .mainwrap ul.main-menu li.menu-link ul.dropdown-submenu.sub-menu li.submenu-li ul.blog-style-1.ex-width{
    min-width: 200px;
}
.megamenu-content .mainwrap ul.main-menu li.menu-link ul.dropdown-submenu.sub-menu li.submenu-li:hover ul.blog-style-1{
    opacity: 1;
    visibility: visible;
    -webkit-transition: all 0.3s ease-in-out 0s;
    -o-transition: all 0.3s ease-in-out 0s;
    transition: all 0.3s ease-in-out 0s;
}
.megamenu-content .mainwrap ul.main-menu li.menu-link ul.dropdown-submenu.sub-menu li.submenu-li ul.blog-style-1 li a{
    width: 100%;
    padding: 5px 15px;
}
.megamenu-content .mainwrap ul.main-menu li.menu-link ul.dropdown-submenu.sub-menu li.submenu-li ul.blog-style-1 li a i{
    font-size: 17px;
    float: right;
}
.megamenu-content .mainwrap ul.main-menu li.menu-link ul.dropdown-submenu.sub-menu li.submenu-li ul.blog-style-1 li a.blog-sub-style{
    display: none;
}
.megamenu-content .mainwrap ul.main-menu li.menu-link ul.dropdown-submenu.sub-menu li.submenu-li ul.blog-style-1 li ul.grid-style.collapse:not(.show){
    display: block;
}
.megamenu-content .mainwrap ul.main-menu li.menu-link ul.dropdown-submenu.sub-menu li.submenu-li ul.blog-style-1 li ul.grid-style{
    position: absolute;
    top: 0px;
    right: 0px;
    padding: 10px 0px;
    min-width: 155px;
    transform: translateX(100%);
    background-color: #fff;
    box-shadow: 0 0 15px rgb(0 0 0 / 15%);
    opacity: 0;
    visibility: hidden;
    -webkit-transition: all 0.3s ease-in-out 0s;
    -o-transition: all 0.3s ease-in-out 0s;
    transition: all 0.3s ease-in-out 0s;
}
.megamenu-content .mainwrap ul.main-menu li.menu-link ul.dropdown-submenu.sub-menu li.submenu-li ul.blog-style-1 li{
    position: relative;
}
.megamenu-content .mainwrap ul.main-menu li.menu-link ul.dropdown-submenu.sub-menu li.submenu-li ul.blog-style-1 li:hover ul.grid-style{
    opacity: 1;
    visibility: visible;
    -webkit-transition: all 0.3s ease-in-out 0s;
    -o-transition: all 0.3s ease-in-out 0s;
    transition: all 0.3s ease-in-out 0s;
}
/* hotline css */
.img-hotline{
    display: flex;
    align-items: center;
    justify-content: flex-end;
}
.img-hotline .image-content{
    margin-left: 15px;
}
.img-hotline .image-content span{
    color: #848484;
}
.img-hotline .image-content span.hot-l{
    display: block;
    color: #ec6504;
}
/* Enhanced Modern Slider CSS */
.slider {
    position: relative;
    overflow: hidden;
}

.slider .home-slider .img-back{
    height: 750px;
    background-position: center;
    background-repeat: no-repeat;
    background-size: cover;
    position: relative;
    display: flex;
    align-items: center;
    justify-content: flex-end;
}

/* Modern Overlay Effect */
.slider .home-slider .img-back::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(
        135deg,
        rgba(45, 80, 22, 0.3) 0%,
        rgba(0, 0, 0, 0.2) 50%,
        rgba(45, 80, 22, 0.4) 100%
    );
    z-index: 1;
}

/* Enhanced Content Container */
.slider .home-slider .img-back .h-s-content,
.slider .home-slider .img-back .home-s-content {
    position: relative;
    z-index: 2;
    max-width: 600px;
    color: var(--white);
    transform: translateY(0);
    opacity: 1;
    animation: slideInRight 1s cubic-bezier(0.23, 1, 0.32, 1) forwards;
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    padding: 40px;
    border-radius: var(--border-radius);
    border: 1px solid rgba(255, 255, 255, 0.2);


}

.slider .home-slider .img-back .h-s-content.slide-c-l,
.slider .home-slider .img-back .home-s-content.slide-c-l{
    margin-left: 8%;
    margin-right: 0;
    transform: translateX(-100px);
}

.slider .home-slider .img-back .h-s-content.slide-c-r,
.slider .home-slider .img-back .home-s-content.slide-c-r{
    margin-right: 8%;
    text-align: left;
    transform: translateX(100px);
}

.slider .home-slider .img-back .h-s-content.slide-c-c,
.slider .home-slider .img-back .home-s-content.slide-c-c{
    margin: 0 auto;
    text-align: center;
    transform: translateY(50px);
}
/* Enhanced Active Slide Animations */
.slider .home-slider .owl-item.active .img-back .h-s-content,
.slider .home-slider .owl-item.active .img-back .home-s-content {
    transform: translateX(0) !important;
    opacity: 1 !important;
    transition-delay: 0.3s;
}

.slider .home-slider .owl-item.active .img-back .h-s-content span,
.slider .home-slider .owl-item.active .img-back .h-s-content h1,
.slider .home-slider .owl-item.active .img-back .h-s-content a,
.slider .home-slider .owl-item.active .img-back .home-s-content span,
.slider .home-slider .owl-item.active .img-back .home-s-content h1,
.slider .home-slider .owl-item.active .img-back .home-s-content a{
    -webkit-animation-name: slideInFromRight;
    animation-name: slideInFromRight;
    -webkit-animation-fill-mode: both;
    animation-fill-mode: both;
    opacity: 0;
}

.slider .home-slider .owl-item.active .img-back .h-s-content span,
.slider .home-slider .owl-item.active .img-back .home-s-content span{
    -webkit-animation-delay: 0.8s;
    animation-delay: 0.8s;
    animation-duration: 0.8s;
}

.slider .home-slider .owl-item.active .img-back .h-s-content h1,
.slider .home-slider .owl-item.active .img-back .home-s-content h1{
    -webkit-animation-delay: 1.1s;
    animation-delay: 1.1s;
    animation-duration: 0.8s;
}

.slider .home-slider .owl-item.active .img-back .h-s-content a,
.slider .home-slider .owl-item.active .img-back .home-s-content a{
    -webkit-animation-delay: 1.4s;
    animation-delay: 1.4s;
    animation-duration: 0.8s;
}

/* Enhanced Typography */
.slider .home-slider .img-back .h-s-content span,
.slider .home-slider .img-back .home-s-content span{
    font-size: 16px;
    font-weight: 600;
    color: #2d5016;
    text-transform: uppercase;
    letter-spacing: 2px;
    display: block;
    margin-bottom: 15px;
    position: relative;
}

.slider .home-slider .img-back .h-s-content span::after,
.slider .home-slider .img-back .home-s-content span::after {
    content: '';
    position: absolute;
    bottom: -8px;
    left: 0;
    width: 60px;
    height: 3px;
    background: linear-gradient(90deg, #2d5016, #ff8c00);
    border-radius: 2px;
}

.slider .home-slider .img-back .h-s-content h1,
.slider .home-slider .img-back .home-s-content h1{
    font-size: 28px;
    font-weight: 700;
    line-height: 1.2;
    color: #fffafa;
    margin: 25px 0 20px 0;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.slider .home-slider .img-back .h-s-content a,
.slider .home-slider .img-back .home-s-content a{
    margin-top: 30px;
    display: inline-block;
    padding: 15px 35px;
    background: linear-gradient(135deg, #2d5016, #4a7c59);
    color: white;
    text-decoration: none;
    border-radius: 50px;
    font-weight: 600;
    font-size: 16px;
    text-transform: uppercase;
    letter-spacing: 1px;
    transition: all 0.3s ease;
    box-shadow: 0 8px 25px rgba(45, 80, 22, 0.3);
    border: 2px solid transparent;
}

.slider .home-slider .img-back .h-s-content a:hover,
.slider .home-slider .img-back .home-s-content a:hover{
    transform: translateY(-3px);
    box-shadow: 0 12px 35px rgba(45, 80, 22, 0.4);
    background: linear-gradient(135deg, #4a7c59, #2d5016);
    border-color: #ff8c00;
    color: white;
    text-decoration: none;
}
/* Custom Animation Keyframes */
@keyframes slideInFromRight {
    0% {
        opacity: 0;
        transform: translateX(50px);
    }
    100% {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes slideInFromLeft {
    0% {
        opacity: 0;
        transform: translateX(-50px);
    }
    100% {
        opacity: 1;
        transform: translateX(0);
    }
}

/* Enhanced Navigation */
.owl-theme .owl-nav {
    margin-top: 0px;
}

.slider .home-slider.owl-theme .owl-nav{
    margin-top: 0px;
}

.slider .home-slider .owl-nav button{
    background: rgba(255, 255, 255, 0.9);
    color: #2d5016;
    border: 2px solid rgba(255, 255, 255, 0.3);
    position: absolute;
    top: 50%;
    transform: translateY(-50%);
    font-size: 20px;
    width: 55px;
    height: 55px;
    margin: 0;
    border-radius: 50%;
    backdrop-filter: blur(10px);
    transition: all 0.3s ease;
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
    z-index: 3;
}

.slider .home-slider .owl-nav button.owl-prev{
    left: 40px;
}

.slider .home-slider .owl-nav button.owl-next{
    right: 40px;
}

.slider .home-slider .owl-nav button:hover{
    background: #2d5016;
    color: white;
    border-color: #ff8c00;
    transform: translateY(-50%) scale(1.1);
    box-shadow: 0 12px 35px rgba(45, 80, 22, 0.3);
}

.slider .home-slider .owl-nav button:focus {
    outline: none;
}
/* Enhanced Pagination Dots */
.slider .home-slider .owl-dots{
    position: absolute;
    bottom: 30px;
    right: 0px;
    left: 0px;
    line-height: 0px;
    text-align: center;
    z-index: 3;
}

.slider .home-slider .owl-dots button{
    margin: 0 8px;
    transition: all 0.3s ease;
}

.slider .home-slider .owl-dots button:last-child{
    margin-right: 8px;
}

.slider .home-slider .owl-dots button.owl-dot span{
    width: 12px;
    height: 12px;
    margin: 0px;
    background: rgba(255, 255, 255, 0.6);
    border-radius: 50%;
    border: 2px solid rgba(255, 255, 255, 0.8);
    transition: all 0.3s ease;
    display: block;
}

.slider .home-slider .owl-dots button.owl-dot.active span{
    background: #2d5016;
    border-color: #ff8c00;
    transform: scale(1.3);
    box-shadow: 0 4px 15px rgba(45, 80, 22, 0.4);
}

.slider .home-slider .owl-dots button.owl-dot:hover span{
    background: rgba(255, 140, 0, 0.8);
    border-color: white;
    transform: scale(1.2);
}

.slider .owl-theme .owl-nav .disabled{
    opacity: 0.6;
}

/* Mobile Responsive Design */
@media (max-width: 768px) {
    .slider .home-slider .img-back {
        height: 500px;
        align-items: center;
        justify-content: center;
    }

    .slider .home-slider .img-back .h-s-content,
    .slider .home-slider .img-back .home-s-content {
        width: 85%;
        max-width: none;
        margin: 0 auto;
        padding: 25px;
        text-align: center;
        transform: translateY(50px);
    }

    .slider .home-slider .img-back .h-s-content.slide-c-l,
    .slider .home-slider .img-back .home-s-content.slide-c-l,
    .slider .home-slider .img-back .h-s-content.slide-c-r,
    .slider .home-slider .img-back .home-s-content.slide-c-r {
        margin: 0 auto;
        text-align: center;
        transform: translateY(50px);
    }

    .slider .home-slider .img-back .h-s-content h1,
    .slider .home-slider .img-back .home-s-content h1 {
        font-size: 32px;
        margin: 15px 0;
    }

    .slider .home-slider .img-back .h-s-content span,
    .slider .home-slider .img-back .home-s-content span {
        font-size: 14px;
        letter-spacing: 1px;
    }

    .slider .home-slider .img-back .h-s-content a,
    .slider .home-slider .img-back .home-s-content a {
        padding: 12px 25px;
        font-size: 14px;
        margin-top: 20px;
    }

    .slider .home-slider .owl-nav button {
        width: 45px;
        height: 45px;
        font-size: 16px;
    }

    .slider .home-slider .owl-nav button.owl-prev {
        left: 20px;
    }

    .slider .home-slider .owl-nav button.owl-next {
        right: 20px;
    }
}

@media (max-width: 480px) {
    .slider .home-slider .img-back {
        height: 450px;
    }

    .slider .home-slider .img-back .h-s-content,
    .slider .home-slider .img-back .home-s-content {
        width: 90%;
        padding: 20px;
    }

    .slider .home-slider .img-back .h-s-content h1,
    .slider .home-slider .img-back .home-s-content h1 {
        font-size: 28px;
    }

    .slider .home-slider .owl-nav button {
        width: 40px;
        height: 40px;
        font-size: 14px;
    }

    .slider .home-slider .owl-nav button.owl-prev {
        left: 15px;
    }

    .slider .home-slider .owl-nav button.owl-next {
        right: 15px;
    }
}
/* Banner grid css */
.t-banner1 .home-offer-banner{
    display: flex;
    -ms-flex-wrap: wrap;
    flex-wrap: wrap;
    margin-left: -30px;
}
.t-banner1 .home-offer-banner .o-t-banner{
    width: calc(50% - 30px);
    margin-left: 30px;
    position: relative;
}
.t-banner1 .home-offer-banner .o-t-banner a.image-b{
    display: block;
    overflow: hidden;
    position: relative;
}
.t-banner1 .home-offer-banner .o-t-banner a.image-b::before,
.t-banner1 .home-offer-banner .o-t-banner a.image-b::after{
    content: "";
    position: absolute;
    top: 10px;
    bottom: 10px;
    right: 10px;
    left: 10px;
    opacity: 0;
    visibility: hidden;
    filter: alpha(opacity=0);
    z-index: 1;
}
.t-banner1 .home-offer-banner .o-t-banner a.image-b::before{
    border-top: 1px solid #fff;
    border-bottom: 1px solid #fff;
    -webkit-transform: scale(0, 1);
    -o-transform: scale(0, 1);
    transform: scale(0, 1);
}
.t-banner1 .home-offer-banner .o-t-banner a.image-b::after{
    border-right: 1px solid #fff;
    border-left: 1px solid #fff;
    -webkit-transform: scale(1, 0);
    -o-transform: scale(1, 0);
    transform: scale(1, 0);
}
.t-banner1 .home-offer-banner .o-t-banner a.image-b:hover::after,
.t-banner1 .home-offer-banner .o-t-banner a.image-b:hover::before{
    opacity: 1;
    visibility: visible;
    filter: alpha(opacity=100);
    -webkit-transform: scale(1);
    -o-transform: scale(1);
    transform: scale(1);
}
.t-banner1 .home-offer-banner .o-t-banner:hover a.image-b img{
    -webkit-transform: scale(1.1);
    -o-transform: scale(1.1);
    transform: scale(1.1);
}
.t-banner1 .home-offer-banner .o-t-banner a.image-b::before,
.t-banner1 .home-offer-banner .o-t-banner a.image-b::after,
.t-banner1 .home-offer-banner .o-t-banner a.image-b:hover::after,
.t-banner1 .home-offer-banner .o-t-banner a.image-b:hover::before,
.t-banner1 .home-offer-banner .o-t-banner a.image-b img,
.t-banner1 .home-offer-banner .o-t-banner:hover a.image-b img{
    -webkit-transition: all 0.3s ease-in-out;
    -o-transition: all 0.3s ease-in-out;
    transition: all 0.3s ease-in-out;
}
.t-banner1 .home-offer-banner .o-t-banner .o-t-content{
    width: 50%;
    position: absolute;
    bottom: 50%;
    transform: translateY(50%);
    left: 30px;
}
.t-banner1 .home-offer-banner .o-t-banner .o-t-content h6{
    font-size: 22px;
    color: white;
}
.t-banner1 .home-offer-banner .o-t-banner .o-t-content a{
    margin-top: 21px;
}
.t-banner1 .home-offer-banner .o-t-banner .banner-color h6{
    color: #ffffff;
}
/* Category image css */
.category-img1{
    background-color: #f7f7f7;
}
.category-img1 .home-category .h-cate{
    text-align: center;
}
.category-img1 .home-category .h-cate .c-img a{
    position: relative;
    border: 1px solid #e5e5e5;
    border-radius: 100%;
}
.category-img1 .home-category .h-cate .c-img a::before{
    content: "";
    position: absolute;
    top: 0px;
    left: 0px;
    width: 100%;
    height: 100%;
    background-color: rgb(0, 0, 0, 0.5);
    opacity: 0;
    visibility: hidden;
    border-radius: 100%;
    transform: scale(0);
}
.category-img1 .home-category .h-cate .c-img a::before,
.category-img1 .home-category .h-cate:hover .c-img a::before{
    -webkit-transition: all 0.3s ease-in-out 0s;
    -o-transition: all 0.3s ease-in-out 0s;
    transition: all 0.3s ease-in-out 0s;
}
.category-img1 .home-category .h-cate:hover .c-img a::before{
    opacity: 1;
    visibility: visible;
    transform: scale(1);
}
.category-img1 .home-category .h-cate .c-img a img{
    border-radius: 100%;
}
.category-img1 .home-category .h-cate .c-img a span{
    color: #fff;
    font-size: 14px;
    position: absolute;
    bottom: 50%;
    transform: translateY(50%);
    right: 0px;
    left: 0px;
    
    opacity: 0;
    visibility: hidden;
}
.category-img1 .home-category .h-cate:hover .c-img a span{
    opacity: 1;
    visibility: visible;
}
.category-img1 .home-category .h-cate .c-img a span,
.category-img1 .home-category .h-cate:hover .c-img a span{
    -webkit-transition: all 0.3s ease-in-out 0s;
    -o-transition: all 0.3s ease-in-out 0s;
    transition: all 0.3s ease-in-out 0s;
}
.category-img1 .home-category .h-cate span.cat-num{
    margin-top: 3px;
}
/* Carousel button css */
.home-category.owl-carousel .owl-nav{
    margin-top: 0;
}
.home-category.owl-carousel .owl-nav button,
.h-t-products1 .trending-products .owl-nav button,
.testimonial-bg1 .testi-m .owl-nav button{
    position: absolute;
    bottom: 50%;
    transform: translateY(50%);
    background-color: #ec6504;
    color: #fff;
    font-size: 20px;
    margin: 0;
    width: 30px;
    height: 30px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 100%;
    opacity: 0;
    visibility: hidden;
}
.category-img1 .home-category:hover .owl-nav button{
    opacity: 1;
    visibility: visible;
    box-shadow: 0px 0px 12px 0px rgba(0, 0, 0, 0.1);
}
.category-img1 .home-category:hover .owl-nav button.owl-prev{
    left: -10px;
}
.category-img1 .home-category:hover .owl-nav button.owl-next{
    right: -10px;
}
.home-category.owl-carousel .owl-nav button:hover,
.h-t-products1 .trending-products .owl-nav button:hover,
.testimonial-bg1 .testi-m .owl-nav button:hover{
    background-color: #fff;
    color: #ec6504;
}
.home-category.owl-carousel .owl-nav button.owl-prev,
.h-t-products1 .trending-products .owl-nav button.owl-prev,
.testimonial-bg1 .testi-m .owl-nav button.owl-prev{
    left: 10px;
}
.home-category.owl-carousel .owl-nav button.owl-next,
.h-t-products1 .trending-products .owl-nav button.owl-next,
.testimonial-bg1 .testi-m .owl-nav button.owl-next{
    right: 10px;
}
.h-t-products1 .trending-products .owl-nav button,
.testimonial-bg1 .testi-m .owl-nav button{
    opacity: 1;
    visibility: visible;
}
.h-t-products1 .trending-products .owl-nav button:hover,
.testimonial-bg1 .testi-m .owl-nav button:hover{
    background-color: #fff;
    color: #ec6504;
    box-shadow: 0px 0px 12px 0px rgba(0, 0, 0, 0.1);
}
.h-t-products1 .trending-products .owl-nav button.owl-prev,
.testimonial-bg1 .testi-m .owl-nav button.owl-prev{
    left: -10px;
}
.h-t-products1 .trending-products .owl-nav button.owl-next,
.testimonial-bg1 .testi-m .owl-nav button.owl-next{
    right: -10px;
}
/* Carousel Dots css */
.testimonial-bg1 .testi-m.owl-theme .owl-dots{
    margin-top: 30px;
    line-height: 0px;
}
.testimonial-bg1 .testi-m.owl-theme .owl-dots button{
    margin-right: 10px;
}
.testimonial-bg1 .testi-m.owl-theme .owl-dots button:last-child{
    margin-right: 0px;
}
.testimonial-bg1 .testi-m.owl-theme .owl-dots button.owl-dot span{
    width: 6px;
    height: 6px;
    margin: 0px;
    background-color: #ec6504;
    border-radius: 50%;
}
.testimonial-bg1 .testi-m.owl-theme .owl-dots .owl-dot.active span,
.testimonial-bg1 .testi-m.owl-theme .owl-dots .owl-dot:hover span{
    background-color: #000;
}
/* Swiper button css */
.pro-tab-slider {
    position: relative;
}
.pro-tab-slider .swiper-buttons{
    height: calc(100%);
    height: -o-calc(100%);
    height: -ms-calc(100%);
    height: -moz-calc(100%);
    height: -webkit-calc(100%);
    top: 0;
    left: 0;
    width: 100%;
    position: absolute;
}
.pro-tab-slider .swiper-buttons .content-buttons{
    height: 100%;
    position: relative;
}
.pro-tab-slider .swiper-buttons .content-buttons .swiper-button-next,
.pro-tab-slider .swiper-buttons .content-buttons .swiper-button-prev{
    background-color: #ec6504;
}
.pro-tab-slider .swiper-buttons .content-buttons .swiper-button-next,
.pro-tab-slider .swiper-buttons .content-buttons .swiper-button-prev{
    background-image: none;
    height: 30px;
    width: 30px;
    display: flex;
    border-radius: 50%;
    color: #fff;
    opacity: 1;
    z-index: 6;
    font-size: 20px;
    box-shadow: 0px 0px 12px 0px rgba(123, 190, 70, 0.1);
    align-items: center;
    justify-content: center;
    -webkit-transition: all 0.3s ease-in-out 0s;
    -o-transition: all 0.3s ease-in-out 0s;
    transition: all 0.3s ease-in-out 0s;
}
.pro-tab-slider .swiper-buttons .content-buttons .swiper-button-next:hover,
.pro-tab-slider .swiper-buttons .content-buttons .swiper-button-prev:hover,
.pro-tab-slider .swiper-buttons .content-buttons .swiper-button-next:focus{
    background-color: #fff;
    color: #ec6504;
    box-shadow: 0px 0px 12px 0px rgba(0, 0, 0, 0.1);
    outline: none;
}
.pro-tab-slider .swiper-buttons .content-buttons .swiper-button-next{
    right: -30px;
}
.pro-tab-slider .swiper-buttons .content-buttons .swiper-button-prev{
    left: -30px;
}
.pro-tab-slider .swiper-buttons .content-buttons .swiper-button-next:after{
    content: "\f105";
    font: normal normal normal 14px/1 FontAwesome;
    font-weight: 800;
}
.pro-tab-slider .swiper-buttons .content-buttons .swiper-button-prev:after{
    content: "\f104";
    font: normal normal normal 14px/1 FontAwesome;
    font-weight: 800;
}
/* Trending Products css */
.tred-pro{
    position: relative;
}
.tred-pro .Pro-lable span.p-text,
.tred-pro .Pro-lable span.p-discount{
    position: absolute;
    top: 5px;
    font-size: 13px;
    color: #fff;
    padding: 2px 10px 2px 15px;
    clip-path: polygon(0 0, 100% 0, 100% 100%, 0 100%, 20% 50%);
}
.tred-pro .Pro-lable span.p-text{
    left: 5px;
    background-color: #305724;
}
.tred-pro .Pro-lable span.p-discount{
    right: 5px;
    background-color: #e30514;
}
.tred-pro .pro-icn{
    position: absolute;
    bottom: 15px;
    left: 0px;
    right: 0px;
    text-align: center;
    margin: 0px;
}
.tred-pro .pro-icn a.w-c-q-icn:first-child{
    transform: translateX(40px);
}
.tred-pro .pro-icn a.w-c-q-icn:last-child{
    transform: translateX(-40px);
}
.tred-pro:hover .pro-icn a.w-c-q-icn:first-child{
    margin-right: 15px;
}
.tred-pro:hover .pro-icn a.w-c-q-icn:last-child{
    margin-left: 15px;
}
.tred-pro:hover .pro-icn a.w-c-q-icn:first-child,
.tred-pro:hover .pro-icn a.w-c-q-icn:last-child{
    transform: translateX(0);
    -webkit-transition: all 0.3s ease-in-out 0s;
    -o-transition: all 0.3s ease-in-out 0s;
    transition: all 0.3s ease-in-out 0s;
}
.tred-pro .pro-icn a.w-c-q-icn i{
    background-color: #fff;
    color: #000;
    width: 40px;
    height: 40px;
    display: flex;
    justify-content: center;
    align-items: center;
    line-height: 0px;
    font-size: 16px;
    border-radius: 100%;
    -webkit-transition: all 0.2s ease-in-out 0s;
    -o-transition: all 0.2s ease-in-out 0s;
    transition: all 0.2s ease-in-out 0s;
    opacity: 0;
    visibility: hidden;
}
.tred-pro .pro-icn a.w-c-q-icn:hover i{
    color: #ec6504;
}
.tred-pro:hover .pro-icn a.w-c-q-icn i{
    opacity: 1;
    visibility: visible;
}
.caption{
    padding-top: 15px;
}
.caption h3{
    font-size: 14px;
    font-weight: 400;
}
.caption h3 a{
    display: block;
    white-space: nowrap;
    width: 100%;
    overflow: hidden;
    text-overflow: ellipsis;
}
.caption .rating{
    display: flex;
    margin-top: 12px;
}
.caption .rating i{
    color: #ccc;
    font-size: 14px;
    margin-right: 5px;
}
.caption .rating i.b-star,
.caption .rating i.c-star,
.caption .rating i.d-star,
.caption .rating i.e-star{
    color: #ec6504;
}
.caption .rating i:last-child{
    margin-right: 0px;
}
.caption .pro-price{
    margin-top: 13px;
}
.caption .pro-price span.new-price{
    font-size: 16px;
    font-weight: 600;
    margin-right: 5px;
    line-height: 1;
}
.caption .pro-price span.old-price{
    color: #999;
    font-size: 14px;
    line-height: 1;
}
/* additional image css */
.tred-pro .tr-pro-img a img{
    backface-visibility: hidden;
}
.tred-pro .tr-pro-img a img.additional-image{
    position: absolute;
    top: 0px;
    right: 0px;
    left: 0px;
    opacity: 0;
    visibility: hidden;
}
.tred-pro:hover .tr-pro-img a img.additional-image{
    opacity: 1;
    visibility: visible;
}
.tred-pro .tr-pro-img a img.additional-image,
.tred-pro:hover .tr-pro-img a img.additional-image{
    -webkit-transition: all 0.3s ease-in-out 0s;
    -o-transition: all 0.3s ease-in-out 0s;
    transition: all 0.3s ease-in-out 0s;
}
/* quick view css */
.quick-view .modal.fade.show {
    display: flex !important;
    align-items: center;
    justify-items: center;
}
.quick-view .modal .modal-dialog{
    max-width: 650px;
    background-color: #fff;
    position: absolute;
    bottom: 50%;
    transform: translateY(50%);
    left: 0;
    right: 0;
}
.quick-view .modal .modal-dialog .modal-content{
    border: none;
}
.quick-view .modal .modal-dialog .modal-content .modal-header{
    width: 100%;
    padding: 15px;
    float: right;
    display: flex;
    align-items: center;
}
.quick-view .modal .modal-dialog .modal-content .modal-header h5{
    font-size: 16px;
    line-height: 1;
}
.quick-view .modal .modal-dialog .modal-content .quick-veiw-area{
    padding: 15px;
    display: flex;
    align-items: flex-start;
}
.quick-view .modal .modal-dialog .modal-content .quick-veiw-area .quick-image{
    width: 50%;
}
.quick-view .modal .modal-dialog .modal-content .quick-veiw-area .quick-image ul.quick-slider{
    margin-top: 15px;
    border-bottom: none;
}
.quick-view .modal .modal-dialog .modal-content .quick-veiw-area .quick-image ul.quick-slider li a{
    padding: 0px;
    border: none;
}
.quick-view .modal .modal-dialog .modal-content .quick-veiw-area .quick-caption{
    width: calc(50% - 15px);
    margin-left: 15px;
}
.quick-view .modal .modal-dialog .modal-content .quick-veiw-area .quick-caption h4{
    font-size: 18px;
}
.quick-view .modal .modal-dialog .modal-content .quick-veiw-area .quick-caption .quick-price{
    margin-top: 10px;
}
.quick-view .modal .modal-dialog .modal-content .quick-veiw-area .quick-caption .quick-price span.new-price{
    margin-right: 5px;
    font-size: 15px;
    font-weight: 700;
}
.quick-view .modal .modal-dialog .modal-content .quick-veiw-area .quick-caption .quick-price span.old-price{
    color: #999;
    font-size: 14px;
    font-weight: 500;
}
.quick-view .modal .modal-dialog .modal-content .quick-veiw-area .quick-caption .quick-rating{
    margin-top: 10px;
}
.quick-view .modal .modal-dialog .modal-content .quick-veiw-area .quick-caption .quick-rating i{
    font-size: 16px;
    color: #999;
}
.quick-view .modal .modal-dialog .modal-content .quick-veiw-area .quick-caption .quick-rating i.c-star{
    color: #ec6504;
}
.quick-view .modal .modal-dialog .modal-content .quick-veiw-area .quick-caption .pro-description p{
    color: #999;
    font-size: 13px;
}
.quick-view .modal .modal-dialog .modal-content .quick-veiw-area .quick-caption .pro-size{
    margin-top: 10px;
}
.quick-view .modal .modal-dialog .modal-content .quick-veiw-area .quick-caption .pro-size label{
    font-weight: 600;
}
.quick-view .modal .modal-dialog .modal-content .quick-veiw-area .quick-caption .pro-size select{
    margin-left: 10px;
    padding: 8px 12px;
    border: 1px solid #eee;
    border-radius: 25px;
    text-transform: uppercase;
}
.quick-view .modal .modal-dialog .modal-content .quick-veiw-area .quick-caption .plus-minus{
    margin-top: 15px;
    display: flex;
    align-items: center;
}
.quick-view .modal .modal-dialog .modal-content .quick-veiw-area .quick-caption .plus-minus span{
    display: flex;
    border: 1px solid #eee;
}
.quick-view .modal .modal-dialog .modal-content .quick-veiw-area .quick-caption .plus-minus span a{
    width: 30px;
    height: 30px;
    display: flex;
    align-items: center;
    justify-content: center;
}
.quick-view .modal .modal-dialog .modal-content .quick-veiw-area .quick-caption .plus-minus span input{
    width: 50px;
    height: 30px;
    padding: 0px;
    text-align: center;
    border-top: none;
    border-bottom: none;
}
.quick-view .modal .modal-dialog .modal-content .quick-veiw-area .quick-caption .plus-minus a.quick-cart,
.quick-view .modal .modal-dialog .modal-content .quick-veiw-area .quick-caption .plus-minus a.quick-wishlist{
    width: 40px;
    height: 40px;
    background-color: #ec6504;
    color: #fff;
    font-size: 16px;
    margin-left: 10px;
    display: flex;
    align-items: center;
    justify-content: center;
    border: 2px solid #ec6504;
    border-radius: 100%;
}
.quick-view .modal .modal-dialog .modal-content .quick-veiw-area .quick-caption .plus-minus a.quick-cart:hover,
.quick-view .modal .modal-dialog .modal-content .quick-veiw-area .quick-caption .plus-minus a.quick-wishlist:hover {
    color: #ec6504;
    background-color: transparent;
}
/* deal of the day css */
.home-countdown1 .back-img{
    background-attachment: fixed;
    background-size: cover;
    background-position: center;
    background-repeat: no-repeat;
    align-items: center;
    display: flex;
    height: 450px;
}
.home-countdown1 .back-img .deal-content{
    max-width: 427px;
}
.home-countdown1 .back-img .deal-content h2{
    color: #fff;
    line-height: 1;
}
.home-countdown1 .back-img .deal-content span.deal-c{
    color: #fff;
    font-size: 16px;
    margin-top: 19px;
    font-weight: 500;
}
/* timer */
.home-countdown1 .back-img .deal-content ul.contdown_row{
    display: flex;
    align-items: center;
    justify-content: flex-start;
    margin-top: 23px;
}
.home-countdown1 .back-img .deal-content ul.contdown_row li.countdown_section{
    background-color: #ec6504;
    position: relative;
    width: 70px;
    height: 70px;
    margin-right: 20px;
    border-radius: 5px;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-direction: column;
}
.home-countdown1 .back-img .deal-content ul.contdown_row li.countdown_section:after{
    content: ":";
    position: absolute;
    right: -13px;
    bottom: 50%;
    transform: translateY(50%);
    color: #fff;
    font-size: 22px;
    font-weight: 600;
}
.home-countdown1 .back-img .deal-content ul.contdown_row li.countdown_section:last-child:after{
    display: none;
}
.home-countdown1 .back-img .deal-content ul.contdown_row li.countdown_section span.countdown_timer{
    color: #fff;
    font-size: 22px;
    font-weight: 600;
}
.home-countdown1 .back-img .deal-content ul.contdown_row li.countdown_section span.countdown_title{
    color: #fff;
    text-align: center;
    font-size: 12px;
    font-weight: 400;
    text-transform: uppercase;
    display: inline-block;
}
.home-countdown1 .back-img .deal-content a{
    margin-top: 30px;
}
.home-countdown1 .back-img .deal-content a:hover {
    color: #fff;
}
/* Our Products tab css */
.our-products-tab ul.nav.nav-tabs{
    border-bottom: none;
    justify-content: center;
    margin-bottom: 60px;
}
.our-products-tab ul.nav.nav-tabs li.nav-item{
    margin-right: 60px;
}
.our-products-tab ul.nav.nav-tabs li.nav-item:last-child{
    margin-right: 0px;
}
.our-products-tab ul.nav.nav-tabs li.nav-item a.nav-link{
    color: #949494;
    font-size: 14px;
    font-weight: 600;
    padding: 12px 30px;
    border-radius: 50px;
    margin-bottom: 0px;
}
.our-products-tab ul.nav.nav-tabs li.nav-item a.nav-link.active,
.our-products-tab ul.nav.nav-tabs li.nav-item a.nav-link:hover{
    color: #000;
    border: 1px solid #000;
}
/* Testimonial css */
.testimonial-bg1{
    background-color: #f9f9f9;
}
.testimonial-bg1 .testi-m .testimonial-area{
    padding: 30px;
    display: flex;
    flex-direction: column;
    align-items: center;
    text-align: center;
    border: 1px solid #eee;
}
.testimonial-bg1 .testi-m .testimonial-area:before{
    content: "\f10d";
    font: normal normal normal 14px/1 FontAwesome;
    font-size: 20px;
    font-weight: 900;
    background-color: #ffb503;
    color: #fff;
    width: 60px;
    height: 60px;
    align-items: center;
    display: flex;
    justify-content: center;
    align-items: center;
    margin: 7px auto;
    border-radius: 50px;
}
.testimonial-bg1 .testi-m .testimonial-area:after{
    content: "";
    position: absolute;
    left: 50%;
    top: 30px;
    transform: translateX(-50%);
    width: 75px;
    height: 75px;
    border: 1px solid #ffb503;
    border-radius: 50px;
}
.testimonial-bg1 .testi-m .testimonial-area span.tsti-title{
    font-size: 18px;
    font-weight: 500;
    margin-top: 13px;
}
.testimonial-bg1 .testi-m .testimonial-area p{
    margin-top: 16px;
}
.testimonial-bg1 .testi-m .testimonial-area .testi-name{
    display: flex;
    flex-direction: column;
    align-items: center;
    margin-top: 20px;
}
.testimonial-bg1 .testi-m .testimonial-area .testi-name h6{
    font-size: 16px;
    font-weight: 500;
    line-height: 1;
}
.testimonial-bg1 .testi-m .testimonial-area .testi-name span{
    color: #ffb503;
    display: flex;
    margin-top: 17px;
}
.testimonial-bg1 .testi-m .testimonial-area .testi-name span i{
    margin-right: 5px;
}
.testimonial-bg1 .testi-m .testimonial-area .testi-name span i:last-child{
    margin-right: 0px;
}
/* Blog css */
.blog1 .home-blog .owl-stage{
    counter-reset: my-sec-counter;
    display: flex;
}
.blog1 .home-blog .owl-item{
    padding: 0 2px;
}
.blog1 .home-blog .owl-item:before,
.blog1 .home-blog .owl-item:after{
    background-color: #ec6504;
}
.blog1 .home-blog .owl-item:before{
    counter-increment: my-sec-counter;
    content: counter(my-sec-counter);
    position: absolute;
    top: 0px;
    left: 15px;
    width: 50px;
    height: 50px;
    font-size: 24px;
    font-weight: 600;
    color: #fff;
    border-radius: 0px 5% 25% 25%;
    align-items: center;
    justify-content: center;
    display: flex;
}
.blog1 .home-blog .owl-item:after{
    content: "";
    width: 20px;
    height: 15px;
    position: absolute;
    top: 0px;
    left: 2px;
    opacity: 0.8;
    border-radius: 20px 5px 0px 0px;
}
.blog1 .home-blog .blog-start{
    border: 1px solid #eee;
    border-radius: 5px;
    margin-top: 15px;
}
.blog1 .home-blog .blog-start .blog-image{
    display: flex;
}
.blog1 .home-blog .blog-start .blog-image a img{
    backface-visibility: hidden;
}
.blog1 .home-blog .blog-start .blog-content{
    padding: 30px;
}
.blog1 .home-blog .blog-start .blog-content .blog-title h6{
    font-size: 16px;
}
.blog1 .home-blog .blog-start .blog-content .blog-title h6 a{
    display: block;
    width: 100%;
    white-space: nowrap;
    text-overflow: ellipsis;
    overflow: hidden;
}
.blog1 .home-blog .blog-start .blog-content .blog-title span.blog-admin{
    display: block;
    margin-top: 7px;
    line-height: 1;
}
.blog1 .home-blog .blog-start .blog-content .blog-title span.blog-admin span.blog-editor{
    color: #000;
    font-weight: 600;
}
.blog1 .home-blog .blog-start .blog-content p.blog-description{
    color: #999;
    margin-top: 21px;
}
.blog1 .home-blog .blog-start .blog-content a.read-link{
    display: flex;
    align-items: center;
    margin-top: 20px;
    font-weight: 600;
    line-height: 1;
    -webkit-transition: all 0s ease-in-out 0s;
    -o-transition: all 0s ease-in-out 0s;
    transition: all 0s ease-in-out 0s;
}
.blog1 .home-blog .blog-start .blog-content a.read-link span{
    color: #222;
}
.blog1 .home-blog .blog-start .blog-content a.read-link:hover span{
    color: #ec6504;
}
.blog1 .home-blog .blog-start .blog-content a.read-link i{
    padding-left: 5px;
    font-size: 12px;
}
.blog1 .home-blog .blog-start .blog-content a.read-link:hover i{
    padding-left: 10px;
}
.blog1 .home-blog .blog-start .blog-content a.read-link span,
.blog1 .home-blog .blog-start .blog-content a.read-link:hover span,
.blog1 .home-blog .blog-start .blog-content a.read-link i,
.blog1 .home-blog .blog-start .blog-content a.read-link:hover i{
    -webkit-transition: all 0.3s ease-in-out 0s;
    -o-transition: all 0.3s ease-in-out 0s;
    transition: all 0.3s ease-in-out 0s;
}
.blog1 .home-blog .blog-start .blog-content .blog-date-comment{
    display: flex;
    justify-content: space-between;
    margin-top: 12px;
    line-height: 1
}
.blog1 .home-blog .blog-start .blog-content .blog-date-comment a{
    font-weight: 600;
}
.blog1 .all-blog{
    text-align: center;
    margin-top: 30px;
}
/* news letter css */
.news-letter1 .home-news{
    width: 50%;
    margin-left: auto;
}
.news-letter1 .home-news h2{
    color: #fff;
    font-size: 30px;
    line-height: 1;
}
.news-letter1 .home-news p{
    color: #fff;
    margin-top: 8px;
}
.news-letter1 .home-news form{
    width: 70%;
    position: relative;
    margin-top: 22px;
    border-radius: 25px;
    overflow: hidden;
}
.news-letter1 .home-news form input{
    width: 100%;
    padding: 10px 15px 10px 20px;
    border-radius: 25px;
}
.news-letter1 .home-news form button{
    position: absolute;
    right: 0px;
    height: 100%;
}
.news-letter1 .home-news form button.news-sub{
    display: none;
}
/* footer css */
.footer-one{
    background-color: #faf8ed;
}
/* service css */
.footer-service ul.service-ul{
    display: flex;
    -ms-flex-wrap: wrap;
    flex-wrap: wrap;
}
.footer-service ul.service-ul li.service-li{
    width: calc(25%);
    display: flex;
    align-items: center;
}
.footer-service ul.service-ul li.service-li a{
    position: relative;
    font-size: 30px;
    width: 80px;
    height: 80px;
    margin-right: 15px;
    display: flex;
    align-items: center;
    justify-content: center;
    border: 1px solid #305724;
    border-radius: 100%;
}
.footer-service ul.service-ul li.service-li a::before,
.footer-service ul.service-ul li.service-li a::after{
    content: "";
    position: absolute;
    background-color: #305724;
    -webkit-transition: all 0.3s ease-in-out 0s;
    -o-transition: all 0.3s ease-in-out 0s;
    transition: all 0.3s ease-in-out 0s;
}
.footer-service ul.service-ul li.service-li a::after{
    width: 65px;
    height: 65px;
    border-radius: 50px;
}
.footer-service ul.service-ul li.service-li a::before{
    right: 0px;
    width: 15px;
    height: 2px;
    opacity: 0;
    visibility: hidden;
}
.footer-service ul.service-ul li.service-li:hover a::after{
    transform: skew(5deg, 5deg);
}
.footer-service ul.service-ul li.service-li:hover a::before{
    right: -15px;
    opacity: 1;
    visibility: visible;
}
.footer-service ul.service-ul li.service-li a i{
    position: relative;
    color: #fff;
    z-index: 1;
}
.footer-service ul.service-ul li.service-li span{
    font-size: 15px;
}
/* company detail css */
.f-logo{
    padding-top: 50px;
    border-top: 1px solid #e8e8e8;
}
.f-logo ul.footer-ul{
    display: flex;
    -ms-flex-wrap: wrap;
    flex-wrap: wrap;
    align-items: center;
}
.f-logo ul.footer-ul li.footer-li{
    width: 25%;
    padding-right: 15px;
}
.f-logo ul.footer-ul li.footer-li:last-child {
    padding-right: 0px;
}
.f-logo ul.footer-ul li.footer-li ul.f-ul-li-ul{
    display: flex;
    align-items: center;
}
.f-logo ul.footer-ul li.footer-li ul.f-ul-li-ul li.footer-icon i{
    color: #ec6504;
    font-size: 40px;
}
.f-logo ul.footer-ul li.footer-li ul.f-ul-li-ul li.footer-info{
    width: calc(100% - 30px);
    margin-left: 30px;
}
.f-logo ul.footer-ul li.footer-li ul.f-ul-li-ul li h6{
    font-size: 16px;
    color: #ec6504;
    line-height: 1;
}
.f-logo ul.footer-ul li.footer-li ul.f-ul-li-ul li span,
.f-logo ul.footer-ul li.footer-li ul.f-ul-li-ul li a{
    display: block;
    font-size: 13px;
    margin-top: 5px;
}
.f-logo ul.footer-ul li.footer-li ul.f-ul-li-ul li span:first-of-type,
.f-logo ul.footer-ul li.footer-li ul.f-ul-li-ul li a:first-of-type{
    margin-top: 9px;
}
/* footer bottom css */
.footer-bottom{
    margin-top: 50px;
    border-top: 1px solid #e5e5e5;
}
.footer-link{
    display: flex;
    -ms-flex-wrap: wrap;
    flex-wrap: wrap;
    margin-left: -30px;
}
.footer-link .f-link{
    width: calc(25% - 30px);
    margin-left: 30px;
}
.footer-link .f-link .h-footer{
    color: #ec6504;
    font-size: 16px;
    font-weight: 600;
    line-height: 1;
}
.footer-link .f-link a.h-footer{
    display: none;
}
.footer-link .f-link .collapse:not(.show){
    display: block;
}
.footer-link .f-link ul.f-link-ul{
    padding-top: 20px;
}
.footer-link .f-link ul.f-link-ul li.f-link-ul-li{
    margin-top: 10px;
    padding-top: 10px;
    border-top: 1px solid #e5e5e5;
}
.footer-link .f-link ul.f-link-ul li.f-link-ul-li:first-child{
    margin-top: 0px;
    padding-top: 0px;
    border-top: none;
}
.footer-link .f-link ul.f-link-ul li.f-link-ul-li a{
    font-size: 13px;
}
/* footer copyright css */
.footer-copyright{
    padding: 15px 0;
    background-color: #305724;
}
.footer-copyright ul.f-bottom{
    display: flex;
    -ms-flex-wrap: wrap;
    flex-wrap: wrap;
    align-items: center;
    margin-left: -30px;
}
.footer-copyright ul.f-bottom li.f-c{
    width: calc(33.33% - 30px);
    margin-left: 30px;
}
.footer-copyright ul.f-bottom li.f-copyright p{
    color: #fff;
    font-size: 13px;
}
.footer-copyright ul.f-bottom li.f-copyright p{
    color: #fff;
    font-size: 13px;
}
.footer-copyright ul.f-bottom li.f-social{
    display: inline-flex;
    justify-content: center;
}
.footer-copyright ul.f-bottom li.f-social a.f-icn-link{
    color: #ec6504;
    width: 30px;
    height: 30px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 5px;
    border: 2px solid #fff;
    background-color: #fff;
    border-radius: 100%;
}
.footer-copyright ul.f-bottom li.f-social a.f-icn-link:last-child{
    margin-right: 0;
}
.footer-copyright ul.f-bottom li.f-social a.f-icn-link:hover{
    background-color: transparent;
    color: #fff;
}
.footer-copyright ul.f-bottom li.f-payment{
    text-align: right;
}
/* back to top css */
a.scroll {
    position: fixed;
    bottom: 80px;
    right: 30px;
    z-index: 2;
    opacity: 0;
    visibility: hidden;
    -webkit-transition: all 0s ease-in-out 0s;
    -o-transition: all 0s ease-in-out 0s;
    transition: all 0s ease-in-out 0s;
}
a.scroll.show{
    opacity: 1;
    visibility: visible;
    -webkit-transition: all 0.3s ease-in-out 0s;
    -o-transition: all 0.3s ease-in-out 0s;
    transition: all 0.3s ease-in-out 0s;
}
a.scroll span{
    color: #ec6504;
    font-size: 16px;
    height: 37px;
    width: 37px;
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: transparent;
    border: 2px solid #ec6504;
    border-radius: 100%;
}
a.scroll:hover span{
    color: #fff;
    background-color: #ec6504;
}
a.scroll span,
a.scroll:hover span{
    -webkit-transition: all 0.3s ease-in-out 0s;
    -o-transition: all 0.3s ease-in-out 0s;
    transition: all 0.3s ease-in-out 0s;
}
/* popup css start */
.vegist-popup .modal-dialog{
    position: absolute;
    bottom: 50%;
    transform: translateY(50%) !important;
    left: 0px;
    right: 0px;
    margin: 0px auto;
}
.vegist-popup.animated{
    animation-duration: 2s;
}
.vegist-popup .modal-dialog .modal-content .modal-body{
    padding: 0px;
}
.vegist-popup .modal-dialog .modal-content .modal-body .popup-content{
    position: relative;
}
.vegist-popup .modal-dialog .modal-content .modal-body .popup-content:before{
    background-color: #fff;
    content: "";
    position: absolute;
    top: 0px;
    left: 0px;
    right: 0px;
    bottom: 0px;
    width: 100%;
    height: 100%;
    opacity: 0.3;
}
.vegist-popup .modal-dialog .modal-content .modal-body .popup-content a.close-btn{
    position: absolute;
    top: 5px;
    right: 10px;
    font-size: 18px;
}
.vegist-popup .modal-dialog .modal-content .modal-body .popup-content .pop-up-newsletter{
    padding: 60px;
    background-repeat: no-repeat;
    background-size: cover;
    background-position: center;
    text-align: center;
}
.vegist-popup .modal-dialog .modal-content .modal-body .popup-content .pop-up-newsletter .logo-content{
    position: relative;
}
.vegist-popup .modal-dialog .modal-content .modal-body .popup-content .pop-up-newsletter .logo-content h4{
    font-size: 20px;
    margin-top: 12px;
    
}
.vegist-popup .modal-dialog .modal-content .modal-body .popup-content .pop-up-newsletter .logo-content span{
    font-size: 14px;
    font-weight: 400;
    margin-top: 7px;
}
.vegist-popup .modal-dialog .modal-content .modal-body .popup-content .pop-up-newsletter .subscribe-area{
    margin-top: 24px;
    position: relative;
}
.vegist-popup .modal-dialog .modal-content .modal-body .popup-content .pop-up-newsletter .subscribe-area input{
    width: 100%;
    padding: 8px 15px;
    background-color: #f7f7f7;
    border-color: #eee;
    border-radius: 4px;
}
.vegist-popup .modal-dialog .modal-content .modal-body .popup-content .pop-up-newsletter .subscribe-area a{
    margin-top: 20px;
}
/* breadcrumb css */
.about-breadcrumb .about-back{
    position: relative;
    background-repeat: no-repeat;
    background-size: cover;
    background-position: center;
    z-index: 5;
}
.about-breadcrumb .about-back .about-l ul.about-link{
    text-align: center;
}
.about-breadcrumb .about-back .about-l ul.about-link li.go-home a{
    font-size: 18px;
    color: #333;
    position: relative;
    font-weight: 500;
    padding-bottom: 10px;
}
.about-breadcrumb .about-back .about-l ul.about-link li.go-home a::after{
    background-color: #333;
    content: "";
    position: absolute;
    bottom: -15px;
    right: 0px;
    left: 0px;
    width: 3px;
    height: 15px;
    margin: 0 auto;
}
.about-breadcrumb .about-back .about-l ul.about-link li.about-p {
    padding-top: 26px;
}
.about-breadcrumb .about-back .about-l ul.about-link li.about-p span{
    font-size: 18px;
    color: #333;
    font-weight: 500;
}
/* about page css */
.about-content .about-title{
    width: 60%;
    margin: 0 auto;
    text-align: center;
}
.about-content .about-title h1{
    font-size: 30px;
    line-height: 1;
}
.about-content .about-title p{
    margin-top: 18px;
}
.about-content .about-details {
    text-align: center;
}
.about-content .about-details {
    margin-top: 23px;
}
.about-content .about-details p{
    margin-top: 15px;
}
.about-content .about-details p:first-child {
    margin-top: 0px;
}
.about-counter{
    background-color: #ec6504;
}
.about-counter .text-center{
    display: flex;
    flex-wrap: wrap;
}
.about-counter .text-center .counter{
    width: 25%;
    border-right: 1px solid #fff;
}
.about-counter .text-center .counter:last-child{
    border: none;
}
.about-counter .text-center .count-title {
    color: #fff;
    font-size: 55px;
    font-weight:700;
    margin-top: 10px;
    margin-bottom: 0;
    line-height: 1;
    text-align: center;
}
.about-counter .text-center .count-text {
    color: #fff;
    font-size: 14px;
    font-weight: normal;
    margin-top: 15px;
    margin-bottom: 0;
    text-align: center;
}
/* billing page css */
.billing-area .billing-title h4{
    font-size: 20px; 
}
.billing-area .billing-address-1 {
    padding-top: 15px;
    margin-top: 12px;
    border-top: 1px solid #eee;
}
.billing-area .billing-address-1 ul.add-name{
    margin-left: -30px;
    display: flex;
    flex-wrap: wrap;
}
.billing-area .billing-address-1 ul.add-name li.billing-name{
    width: calc(50% - 30px);
    margin-left: 30px;
    margin-bottom: 15px;
}
.billing-area .billing-address-1 ul.add-name li.billing-name input{
    width: 100%;
    font-size: 13px;
    margin-top: 10px;
    border: 1px solid #eee;
}
.billing-area .billing-address-1 ul.billing-locatio{
    margin-left: -30px;
    display: flex;
    flex-wrap: wrap;
}
.billing-area .billing-address-1 ul.billing-locatio li.billing-info{
    width: calc(33.33% - 30px);
    margin-left: 30px;
    margin-bottom: 15px;
}
.billing-area .billing-address-1 ul.billing-locatio li.billing-info input{
    width: 100%;
    margin-top: 10px;
    font-size: 13px;
    border: 1px solid #eee;
}
.billing-area .billing-address-1 ul.country-info{
    margin-left: -30px;
    display: flex;
    flex-wrap: wrap;
}
.billing-area .billing-address-1 ul.country-info li.billing-country{
    width: calc(50% - 30px);
    margin-left: 30px;
    margin-bottom: 22px;
}
.billing-area .billing-address-1 ul.country-info li.billing-country input{
    width: 100%;
    margin-top: 10px;
    font-size: 13px;
    border: 1px solid #eee;
}
.billing-area .billing-address-1 ul.country-info li.billing-country select{
    width: 100%;
    margin-top: 10px;
    font-size: 13px;
    border: 1px solid #eee;
}
.billing-area .next-button{
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-top: 8px;
}
.billing-area .next-button a{
    background-color: #ec6504;
    color: #fff;
    font-weight: 600;
    padding: 7px 15px;
    border: 2px solid #ec6504;
    border-radius: 3px;
    
    line-height: 1;
}
.billing-area .next-button a:hover{
    background-color: transparent;
    color: #000;
}
/* cancellation page css */
.cancellation-title h1{
    font-size: 30px;
    margin-bottom: 30px;
    text-align: center;
    
}
.cancellation-content ul.cancellation{
    list-style-type: unset;
}
.cancellation-content ul.cancellation li{
    margin-bottom: 15px;
}
/* cart page css */
.cart-area{
    padding-bottom: 20px;
    border-bottom: 1px solid #eee;
}
.cart-area:last-child{
    border-bottom: none;
    padding-bottom: 0px;
}
.cart-area .cart-details .cart-item{
    padding: 13px 0px;
    border-top: 1px solid #eee;
    border-bottom: 1px solid #eee;
    display: flex;
    align-items: center;
    justify-content: space-between;
}
.cart-area .cart-details .cart-item span.cart-head{
    font-size: 16px;
    font-weight: 700;
}
.cart-area .cart-details .cart-item span.c-items{
    border-bottom: 1px solid #000;
    margin-bottom: 3px;
}
.cart-area .cart-details .cart-all-pro{
    display: flex;
    flex-wrap: wrap;
    align-items: center;
    justify-content: space-between;
}
.cart-area .cart-details .cart-all-pro .cart-pro{
    width: 60%;
    margin-top: 20px;
    display: flex;
}
.cart-area .cart-details .cart-all-pro .cart-pro .cart-pro-image{
    margin-right: 15px;
}
.cart-area .cart-details .cart-all-pro .cart-pro .pro-details h4{
    font-size: 14px;
    font-weight: 400;
    line-height: 1;
}
.cart-area .cart-details .cart-all-pro .cart-pro .pro-details span.pro-size{
    margin-top: 10px;
    display: block;
    line-height: 1;
}
.cart-area .cart-details .cart-all-pro .cart-pro .pro-details span.pro-size span.size{
    font-weight: 600;
}
.cart-area .cart-details .cart-all-pro .cart-pro .pro-details span.pro-shop{
    margin-top: 10px;
    display: block;
    line-height: 1;
}
.cart-area .cart-details .cart-all-pro .cart-pro .pro-details span.cart-pro-price{
    margin-top: 10px;
    display: block;
    line-height: 1;
}
.cart-area .cart-details .cart-all-pro .qty-item{
    width: 20%;
    display: flex;
    align-items: center;
    justify-content: center;
}
.cart-area .cart-details .cart-all-pro .qty-item .plus-minus{
    display: flex;
    align-items: center;
}
.cart-area .cart-details .cart-all-pro .qty-item .plus-minus span{
    display: flex;
    border: 1px solid #eee;
}
.cart-area .cart-details .cart-all-pro .qty-item .plus-minus span a{
    width: 30px;
    height: 30px;
    display: flex;
    align-items: center;
    justify-content: center;
}
.cart-area .cart-details .cart-all-pro .qty-item .plus-minus span input{
    width: 50px;
    height: 30px;
    padding: 0px;
    text-align: center;
    border-top: none;
    border-bottom: none;
}
.cart-area .cart-details .cart-all-pro .qty-item .plus-minus a.quick-cart,
.cart-area .cart-details .cart-all-pro .qty-item .plus-minus a.quick-wishlist{
    width: 40px;
    height: 40px;
    background-color: #ec6504;
    color: #fff;
    font-size: 16px;
    margin-left: 10px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 100%;
}
.cart-area .cart-details .cart-all-pro .qty-item .center a.pro-remove{
    color: #ec6504;
    margin-top: 15px;
    text-decoration: underline;
    display: flex;
    align-items: center;
    justify-content: center;
}
.cart-area .cart-details .cart-all-pro .all-pro-price{
    width: 20%;
    text-align: right;
}
.cart-area .cart-details .cart-all-pro .all-pro-price span{
    font-weight: 600;
}
.cart-area .cart-details .other-link{
    margin-top: 21px;
    padding: 15px 0px;
    border-top: 1px solid #eee;
    border-bottom: 1px solid #eee;
}
.cart-area .cart-details .other-link ul.c-link{
    display: flex;
    align-items: center;
    justify-content: space-between;
}
.cart-total{
    background-color: #f7f7f7;
    position: sticky;
    top: 0px;
    padding: 15px;
}
.cart-total .cart-price{
    padding-top: 15px;
    padding-bottom: 14px;
    border-top: 1px solid #eee;
    border-bottom: 1px solid #eee;
    display: flex;
    align-items: center;
    justify-content: space-between;
}
.cart-total .cart-price span.total{
    font-weight: 600;
}
.cart-total .cart-info h4{
    font-size: 14px;
    margin-top: 25px;
}
.cart-total .cart-info form{
    margin-top: 8px;
}
.cart-total .cart-info form label{
    font-size: 13px;
    margin-top: 15px;
}
.cart-total .cart-info form label:first-child {
    margin-top: 0px;
}
.cart-total .cart-info form select{
    width: 100%;
    background-color: #fff;
    padding: 8px 10px;
    border: 1px solid #eee;
    border-radius: 3px;
    margin-top: 10px;
}
.cart-total .cart-info form input{
    width: 100%;
    background-color: #fff;
    padding: 8px 10px;
    font-size: 12px;
    border: 1px solid #eee;
    border-radius: 3px;
    margin-top: 10px;
}
.cart-total .cart-info a.cart-calculate{
    width: 100%;
    margin-top: 25px;
}
.cart-total .shop-total{
    margin-top: 26px;
    padding: 14px 0px;
    border-top: 1px solid #eee;
    border-bottom: 1px solid #eee;
    display: flex;
    align-items: center;
    justify-content: space-between;
}
.cart-total .shop-total span.total-amount{
    font-weight: 600;
}
.cart-total a.check-link{
    width: 100%;
    margin-top: 25px;
}
/* cart style-3 css */
.cart-style-3 {
    margin-top: 21px;
}
.cart-style-3 h2.cart-main-title{
    font-size: 24px; 
}
.cart-style-3 .c-total{
    margin-top: 21px;
}
.cart-style-3 .c-total ul li.c-all-price{
    padding-top: 15px;
    padding-bottom: 14px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    border-top: 1px solid #eee;
    border-bottom: 1px solid #eee;
}
.cart-style-3 .c-total ul li span{
    font-weight: 600;
}
.cart-style-3 .recive-details {
    margin-top: 15px;
}
.cart-style-3 .recive-details h4.recive-title{
    font-size: 14px;  
}
.cart-style-3 .recive-details form {
    margin-top: 9px;
}
.cart-style-3 .recive-details form .form-1 ul{
    display: flex;
    flex-wrap: wrap;
    align-items: flex-end;
    margin-left: -15px;
}
.cart-style-3 .recive-details form .form-1 ul li{
    width: calc(25% - 15px);
    margin-left: 15px;
}
.cart-style-3 .recive-details form .form-1 ul li label{
    display: block;
    font-size: 13px;
    font-weight: 500;
}
.cart-style-3 .recive-details form .form-1 ul li select{
    width: 100%;
    margin-top: 10px;
}
.cart-style-3 .recive-details form .form-1 ul li input{
    width: 100%;
    margin-top: 10px;
}
.cart-style-3 .recive-details form .form-1 ul li a{
    width: 100%;
    text-align: center;
    text-transform: uppercase;
}
.cart-style-3 .c-total .recive-details form .form-2{
    border-top: 1px solid #eee;
    margin-top: 30px;
    padding-top: 25px;
}
.cart-style-3 .c-total .recive-details form .form-2 ul.recive-comments-area{
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-left: -15px;
}
.cart-style-3 .c-total .recive-details form .form-2 ul.recive-comments-area li.recive-comments{
    width: calc(50% - 15px);
    margin-left: 15px;
}
.cart-style-3 .c-total .recive-details form .form-2 ul.recive-comments-area li.recive-comments label{
    display: block;
    font-size: 13px;
    font-weight: 500;
}
.cart-style-3 .c-total .recive-details form .form-2 ul.recive-comments-area li.recive-comments textarea{
    width: 100%;
    margin-top: 10px;
}
.cart-style-3 .c-total .recive-details form .form-2 ul.recive-comments-area li.recive-comments a.btn-style1{
    width: 50%;
    float: right;
    text-align: center;
}

/* empty cart css */
.empty-area .empty-start{
    text-align: center;
}
.empty-area .empty-start h2.empty-title{
    font-size: 26px;
    font-weight: 600;
}
.empty-area .empty-start h2.empty-title span.color-text{
    color: #ec6504;
}
.empty-area .empty-start span.empty-more{
    margin-top: 30px;
}
.empty-area .empty-start span.empty-more a{
    color: #ec6504;
    font-size: 16px;
    font-weight: 600;
}

/* cart login box css */
.register-area{
    max-width: 60%;
    margin: 0 auto;
    display: flex;
    align-items: center;
    justify-content: center;
}
.register-area .register-box{
    width: 50%;
    padding: 30px;
    border: 1px solid #eee;
}
.register-area .register-box h1{
    font-size: 30px;
    text-align: center;
}
.register-area .register-box p{
    margin-top: 10px;
    text-align: center;
}
.register-area .register-box form {
    margin-top: 22px;
}
.register-area .register-box form input{
    width: 100%;
    padding: 10px 15px;
    margin-top: 20px;
    font-size: 13px;
    border: 1px solid #eee;
    border-radius: 3px;
}
.register-area .register-box form input:first-child {
    margin-top: 0px;
}
.register-area .register-box a.btn-style1{
    width: 100%;
    margin-top: 30px;
    text-align: center;
}
.register-area .register-account{
    width: calc(50% - 30px);
    margin-left: 30px;
}
.register-area .register-account h4{
    font-size: 16px;
    text-align: center;
}
.register-area .register-account a.ceate-a{
    position: relative;
    color: #ec6504;
    width: 100%;
    padding: 13px 25px;
    margin-top: 30px;
    font-weight: 600;
    display: flex;
    align-items: center;
    justify-content: center;
    border: 2px solid #ec6504;
    border-radius: 3px;
    -webkit-transition: all 0.2s ease-in-out 0s;
    -o-transition: all 0.2s ease-in-out 0s;
    transition: all 0.2s ease-in-out 0s;
}
.register-area .register-account a.ceate-a::after{
    background-color: #ec6504;
    content: "";
    position: absolute;
    top: 0px;
    right: 0px;
    left: 0px;
    width: 100%;
    height: 100%;
    opacity: 0;
    visibility: hidden;
}
.register-area .register-account a.ceate-a:hover::after{
    opacity: 0.1;
    visibility: visible;
    -webkit-transition: all 0.2s ease-in-out 0s;
    -o-transition: all 0.2s ease-in-out 0s;
    transition: all 0.2s ease-in-out 0s;
}
.register-area .register-account .register-info a.terms-link span{
    color: #000;
    line-height: 1;
    font-size: 12px;
}
.register-area .register-account .register-info a.terms-link{
    color: #ec6504;
    margin-top: 30px;
    font-size: 12px;
    font-weight: 600;
}
.register-area .register-account .register-info p{
    font-size: 13px;
    margin-top: 10px;
}
.register-area .register-account .register-info p a{
    color: #ec6504;
    font-weight: 600;
}

/* faq page css */
.faq-title {
    margin-bottom: 37px;
}
.faq-collapse .faq-title {
    margin-bottom: 45px;
}
.faq-title h1{
    font-size: 30px;
    margin-bottom: 18px;
    text-align: center;
    line-height: 1;  
}
.faq-title p{
    color: #000;
    font-size: 16px;
    font-weight: 600;
    text-align: center;
}
.faq-box{
    display: flex;
    flex-wrap: wrap;
    margin-left: -30px;
}
.faq-box ul.faq-ul{
    width: calc(50% - 30px);
    margin-left: 30px;
    margin-top: -16px;
}
.faq-box ul.faq-ul li.faq-li{
    margin-top: 16px;
}
.faq-box ul.faq-ul li.faq-li h3{
    font-size: 16px;
    color: #333;
    font-weight: 600; 
}
.faq-box ul.faq-ul li.faq-li h3 span{
    color: #ec6504;
}
.faq-box ul.faq-ul li.faq-li span.faq-desc{
    padding-left: 20px;
    margin-top: 6px;
    line-height: 25px;
}
.faq-box a.btn-style1{
    margin: 0 auto;
    margin-top: 22px;
}
/* collapse css */
.faq-start{
    margin-bottom: 30px;
}
.faq-start span{
    background-color: #fff;
    display: block;
    width: 40px;
    height: 40px;
    font-size: 15px;
    font-weight: 600;
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: 0px 0px 12px 0px rgb(0 0 0 / 10%);
}
.faq-start a.collapse-title{
    background-color: #ec6504;
    color: #fff;
    padding: 10px 15px;
    font-size: 16px;
    display: block;
    font-weight: 600;
}
.faq-start .collapse-content {
    padding-top: 13px;
}
.faq-start .collapse-content p{
    margin-top: 14px;
}
.faq-start .collapse-content p:first-child{
    margin-top: 0px;
}
/* forgot password page css */
.forgat-password-area{
    width: 50%;
    margin: 0 auto;
}
.forgat-password-area h4.forgot-title{
    font-size: 20px;
    text-align: center;
}
.forgat-password-area .forgot-p{
    margin-top: 21px;
    border: 1px solid #eee;
    border-radius: 5px;
    overflow: hidden;
}
.forgat-password-area .forgot-p span.forgot{
    width: 100%;
    background-color: #ec6504;
    color: #fff;
    padding: 15px;
    text-align: center;
}
.forgat-password-area .forgot-p form{
    width: 100%;
    padding: 30px;
}
.forgat-password-area .forgot-p form input{
    width: 100%;
    font-size: 13px;
    border: 1px solid #eee;
    text-align: center;
}
.forgat-password-area .forgot-p a.forgot-link{
    width: 100%;
    background-color: #ec6504;
    color: #fff;
    padding: 15px;
    font-weight: 400;
    text-align: center;
}
.forgat-password-area .forgot-p a.forgot-link span{
    background-color: #000;
    border: 2px solid #000;
    border-radius: 5px;
    padding: 8px 10px;
    -webkit-transition: all 0.3s ease-in-out 0s;
    -o-transition: all 0.3s ease-in-out 0s;
    transition: all 0.3s ease-in-out 0s;
}
.forgat-password-area .forgot-p a.forgot-link span i{
    font-size: 18px;
    margin-left: 3px;
}
.forgat-password-area .forgot-p a.forgot-link span:hover{
    background-color: transparent;
    -webkit-transition: all 0.3s ease-in-out 0s;
    -o-transition: all 0.3s ease-in-out 0s;
    transition: all 0.3s ease-in-out 0s;
}
/* order complete page css */
.order-area .order-price ul.total-order{
    display: flex;
    align-items: center;
    justify-content: space-between;
}
.order-area .order-price ul.total-order li:last-child{
    text-align: right;
}
.order-area .order-price ul.total-order li span.order-no{
    display: block;
    font-weight: 600;
}
.order-area .order-price ul.total-order li span.order-date{
    color: #bbbbbb;
    margin-top: 5px;
}
.order-area .order-price ul.total-order li span.total-price{
    display: block;
    text-align: right;
    font-weight: 600;
}
.order-area .order-price ul.total-order li span.amount{
    margin-top: 5px;
    text-align: right;
    font-weight: 600;
}
.order-area .order-details{
    margin-top: 24px;
    padding-top: 28px;
    border-top: 1px solid #eee;
    text-align: center;
}
.order-area .order-details span.order-i {
    font-size: 30px;
    line-height: 1;
}
.order-area .order-details span.order-s {
    display: block;
    margin-top: 8px;
}
.order-area .order-details a.tracking-link{
    margin-top: 24px;
}
.order-area .order-details a.tracking-link:hover{
    background-color: transparent;
    color: #000;
}
.order-area .order-delivery{
    margin-top: 30px;
    padding-top: 30px;
    border-top: 1px solid #eee;
}
.order-area .order-delivery ul.delivery-payment{
    display: flex;
    flex-wrap: wrap;
    margin: -30px 0px 0px -30px;
}
.order-area .order-delivery ul.delivery-payment li {
    width: calc(50% - 30px);
    margin: 30px 0px 0px 30px;
    border-right: 1px solid #eee; 
}
.order-area .order-delivery ul.delivery-payment li:last-child {
    border-right: none;
}
.order-area .order-delivery ul.delivery-payment li h5{
    font-size: 16px;
}
.order-area .order-delivery ul.delivery-payment li p{
    margin-top: 6px;
}
.order-area .order-delivery ul.delivery-payment li.delivery p{
    font-weight: 600;
}
.order-area .order-delivery ul.delivery-payment li.pay p{
    color: #bbb;
}
.order-area .order-delivery ul.delivery-payment li span.order-span{
    display: block;
    margin-top: 9px;
}
.order-area .order-delivery ul.delivery-payment li span.order-span:first-of-type{
    margin-top: 7px;
}
.order-area .order-delivery ul.delivery-payment li span.p-label {
    display: flex;
    align-items: center;
    justify-content: space-between;
}
.order-area .order-delivery ul.delivery-payment li span.p-label span.o-price {
    font-weight: 600;
}
/* track page css */
.track-area .track-price ul.track-order{
    display: flex;
    align-items: center;
    justify-content: space-between;
}
.track-area .track-price ul.track-order li h4{
    font-size: 16px;
}
.track-area .track-price ul.track-order li span.track-status{
    font-size: 16px;
    font-weight: 600;
}
.track-area .track-main {
    margin-top: 30px;
    padding-top: 8px;
    border-top: 1px solid #eee;
}
.track-area .track-main .track{
    position: relative;
    height: 7px;
    display: flex;
    margin-bottom: 40px;
    margin-top: 40px
}
.track-area .track-main .track:before {
    background-color: #ddd;
    content: '';
    position: absolute;
    bottom: 50%;
    transform: translateY(50%);
    width: 100%;
    height: 7px;
}
.track-area .track-main .track .step{
    width: 25%;
    margin-top: -18px;
    text-align: center;
    position: relative
}
.track-area .track-main .track .step.active:before{
    background-color: #ec6504;
}
.track-area .track-main .track .step::before {
    height: 7px;
    position: absolute;
    content: "";
    width: 100%;
    left: 0;
    top: 18px
}
.track-area .track-main .track .step.active .icon{
    background-color: #ec6504;
    color: #fff
}
.track-area .track-main .track .icon{
    display: inline-block;
    width: 40px;
    height: 40px;
    line-height: 40px;
    position: relative;
    border-radius: 100%;
    background: #ddd
}
.track-area .track-main .track .step.active .text{
    font-weight: 400;
    color: #000
}
.track-area .track-main .track .text{
    display: block;
    margin-top: 7px
}
/* contact page css */
.map-area .map-title h1{
    font-size: 30px;
    margin-bottom: 30px;
    text-align: center;
}
.map-area .map {
    display: flex;
}
.map-area .map iframe{
    width: 100%;
    height: 680px;
}
.map-area .map-details{
    display: flex;
    flex-wrap: wrap;
}
.map-area .map-details .contact-info{
    width: 50%;
}
.map-area .map-details .contact-info .contact-details h4{
    font-size: 18px;
}
.map-area .map-details .contact-info .contact-details form {
    margin-top: 18px;
}
.map-area .map-details .contact-info .contact-details form label{
    display: block;
    margin-top: 15px;
}
.map-area .map-details .contact-info .contact-details form label:first-child {
    margin-top: 0px;
}
.map-area .map-details .contact-info .contact-details form input{
    width: 100%;
    border-radius: 4px;
    padding: 7px 15px;
    margin-top: 10px;
    border: 1px solid #ddd;
    transition: border-color 0.3s ease;
}
.map-area .map-details .contact-info .contact-details form input:focus{
    outline: none;
    border-color: #ec6504;
    box-shadow: 0 0 5px rgba(236, 101, 4, 0.3);
}
.map-area .map-details .contact-info .contact-details form input.error{
    border-color: #dc3545;
    box-shadow: 0 0 5px rgba(220, 53, 69, 0.3);
}
.map-area .map-details .contact-info .contact-details form textarea{
    width: 100%;
    resize: none;
    margin-top: 10px;
    border-radius: 4px;
    border: 1px solid #ddd;
    padding: 7px 15px;
    transition: border-color 0.3s ease;
}
.map-area .map-details .contact-info .contact-details form textarea:focus{
    outline: none;
    border-color: #ec6504;
    box-shadow: 0 0 5px rgba(236, 101, 4, 0.3);
}
.map-area .map-details .contact-info .contact-details form textarea.error{
    border-color: #dc3545;
    box-shadow: 0 0 5px rgba(220, 53, 69, 0.3);
}
.map-area .map-details .contact-info .contact-details a,
.map-area .map-details .contact-info .contact-details button{
    margin-top: 24px;
    display: inline-flex;
    align-items: center;
    border: none;
    background: none;
    cursor: pointer;
    transition: all 0.3s ease;
}
.map-area .map-details .contact-info .contact-details a i,
.map-area .map-details .contact-info .contact-details button i {
    margin-left: 5px;
}
.map-area .map-details .contact-info .contact-details button:disabled{
    opacity: 0.7;
    cursor: not-allowed;
}
.map-area .map-details .contact-info .contact-details button .fa-spinner{
    animation: spin 1s linear infinite;
}
@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}
.map-area .map-details .contact-info .information{
    margin-left: 30px;
}
.map-area .map-details .contact-info .information h4{
    font-size: 18px; 
}
.map-area .map-details .contact-info .information p.info-description{
    margin-top: 16px;
}
.map-area .map-details .contact-info .information .contact-in {
    margin-top: 22px;
}
.map-area .map-details .contact-info .information .contact-in ul.info-details{
    display: flex;
    align-items: flex-start;
    justify-content: flex-start;
    margin-top: 10px;
}
.map-area .map-details .contact-info .information .contact-in ul.info-details:first-child {
    margin-top: 0px;
}
.map-area .map-details .contact-info .information .contact-in ul.info-details li i{
    font-size: 20px;
    margin-right: 20px;
}
.map-area .map-details .contact-info .information .contact-in ul.info-details li h4{
    font-size: 14px;
    line-height: 1;
}
.map-area .map-details .contact-info .information .contact-in ul.info-details li a {
    margin-top: 7px;
}
/* payment page css */
.payment-title h1{
    font-size: 30px;
    margin-bottom: 37px;
    text-align: center;
}
.payment{
    margin-bottom: 30px;
}
.payment span{
    background-color: #fff;
    display: block;
    width: 40px;
    height: 40px;
    font-size: 15px;
    font-weight: 600;
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: 0px 0px 12px 0px rgb(0 0 0 / 10%);
}
.payment h4.payment-title{
    background-color: #ec6504;
    color: #fff;
    padding: 10px 15px;
    font-size: 16px;
    display: block;
    font-weight: 600;
}
.payment .payment-content {
    margin-top: 13px;
}
.payment .payment-content p{
    margin-top: 5px;
}
.payment .payment-content p:first-child {
    margin-top: 0px;
}
/* privacy policy page css */
.privacy-title h1{
    font-size: 30px;
    margin-bottom: 30px;
    text-align: center;
}
.privacy-content ul{
    list-style-type: unset;
}
.privacy-content ul.privacy-policy li{
    margin-top: 5px;
}
.privacy-content ul.privacy-policy li:first-child {
    margin-top: 0px;
}
/* return page css */
.return-title h1{
    font-size: 30px;
    margin-bottom: 37px;
    text-align: center;
}
.return{
    margin-bottom: 30px;
}
.return span{
    background-color: #fff;
    display: block;
    width: 40px;
    height: 40px;
    font-size: 15px;
    font-weight: 600;
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: 0px 0px 12px 0px rgb(0 0 0 / 10%);
}
.return h4.return-title{
    background-color: #ec6504;
    color: #fff;
    padding: 10px 15px;
    font-size: 16px;
    display: block;
    font-weight: 600;
}
.return .return-content {
    margin-top: 13px;
}
.return .return-content p{
    margin-top: 5px;
}
.return .return-content p:first-child {
    margin-top: 0px;
}
/* terms conditions page css */
.terms-title h1{
    font-size: 30px;
    margin-bottom: 31px;
    text-align: center;
}
.terms-content ul.terms-policy li:first-child {
    margin-top: 0px;
}
.terms-content ul.terms-policy li {
    margin-top: 16px;
}
.terms-content ul.terms-policy li h2{
    font-size: 16px;
    color: #222;
}
.terms-content ul.terms-policy li h2 p{
    color: #ec6504;
}
.terms-content ul.terms-policy li p.trems-desc{
    padding-left: 20px;
    display: block;
    margin-top: 6px;
}
/* wishlist page css */
.wishlist-area{
    padding-bottom: 20px;
    border-bottom: 1px solid #eee;
}
.wishlist-area:last-child{
    border-bottom: none;
}
.wishlist-area .wishlist-details .wishlist-item{
    padding: 13px 0px;
    border-top: 1px solid #eee;
    border-bottom: 1px solid #eee;
    display: flex;
    align-items: center;
    justify-content: space-between;
}
.wishlist-area .wishlist-details .wishlist-item span.wishlist-head{
    font-size: 16px;
    font-weight: 700;
}
.wishlist-area .wishlist-details .wishlist-item span.c-items{
    border-bottom: 1px solid #000;
    margin-bottom: 3px;
}
.wishlist-area .wishlist-details .wishlist-all-pro{
    display: flex;
    flex-wrap: wrap;
    align-items: center;
    justify-content: space-between;
}
.wishlist-area .wishlist-details .wishlist-all-pro .wishlist-pro{
    width: 60%;
    margin-top: 20px;
    display: flex;
}
.wishlist-area .wishlist-details .wishlist-all-pro .wishlist-pro .wishlist-pro-image{
    margin-right: 15px;
}
.wishlist-area .wishlist-details .wishlist-all-pro .wishlist-pro .pro-details h4{
    font-size: 14px;
    font-weight: 400;
    line-height: 1;
}
.wishlist-area .wishlist-details .wishlist-all-pro .wishlist-pro .pro-details span.all-size{
    font-size: 14px;
    margin-top: 8px;
    font-weight: 600;
    display: block;
}
.wishlist-area .wishlist-details .wishlist-all-pro .wishlist-pro .pro-details span.all-size span.pro-size{
    font-weight: 400;   
}
.wishlist-area .wishlist-details .wishlist-all-pro .wishlist-pro .pro-details span.wishlist-text{
    margin-top: 8px;
    line-height: 1;
}
.wishlist-area .wishlist-details .wishlist-all-pro .qty-item{
    width: 20%;
}
.wishlist-area .wishlist-details .wishlist-all-pro .qty-item a.add-wishlist{
    color: #ec6504;
    margin-top: 7px;
    text-decoration: underline;
    display: block;
    text-align: center;
}
.wishlist-area .wishlist-details .wishlist-all-pro .qty-item a.add-wishlist:first-child {
    margin-top: 0px;
}
.wishlist-area .wishlist-details .wishlist-all-pro .all-pro-price{
    width: 20%;
    text-align: right;
}
.wishlist-area .wishlist-details .wishlist-all-pro .all-pro-price span.new-price{
    display: block;
    margin-bottom: 5px;
    font-weight: 700;
}
.wishlist-area .wishlist-details .wishlist-all-pro .all-pro-price span.old-price{
    color: #999;
}
.wishlist-area .wishlist-details .other-link{
    margin-top: 20px;
    padding: 15px 0px;
    border-top: 1px solid #eee;
    border-bottom: 1px solid #eee;
}
.wishlist-area .wishlist-details .other-link ul.c-link{
    display: flex;
    align-items: center;
    justify-content: space-between;
}
/* sitemap page css */
.sit-map-area{
    display: flex;
    flex-wrap: wrap;
}
.sit-map-area ul.site-map-main{
    width: 33.33%;
    margin-top: 21px;
}
.sit-map-area ul.site-map-main:nth-child(1),
.sit-map-area ul.site-map-main:nth-child(2),
.sit-map-area ul.site-map-main:nth-child(3){
    margin-top: 0px;
}
.sit-map-area ul.site-map-main li.site-main-title{
    position: relative;
    margin-left: 30px;
}
.sit-map-area ul.site-map-main li.site-main-title h4.title{
    line-height: 1;
}
.sit-map-area ul.site-map-main li.site-main-title h4.title a.site-title{
    position: relative;
    color: #ec6504;
    font-size: 18px;
    font-weight: 600;
}
.sit-map-area ul.site-map-main li.site-main-title h4.title a.site-title:before{
    background-color: #ec6504;
    content: "";
    position: absolute;
    top: 10px;
    left: -20px;
    width: 10px;
    height: 10px;
    border-radius: 100%;
}
.sit-map-area ul.site-map-main li.site-main-title ul.site-main-link{
    margin-top: 21px;
    margin-left: 30px;
}
.sit-map-area ul.site-map-main li.site-main-title ul.site-main-link li.site-link{
    position: relative;
    margin-top: 5px;
}
.sit-map-area ul.site-map-main li.site-main-title ul.site-main-link a.shop-main{
    position: relative;
    color: #ec6504;
    font-size: 16px;
    font-weight: 500;
}
.sit-map-area ul.site-map-main li.site-main-title ul.site-main-link a.shop-main:before{
    background-color: #ec6504;
    content: "";
    position: absolute;
    top: 8px;
    left: -20px;
    width: 8px;
    height: 8px;
    border-radius: 100%;
}
.sit-map-area ul.site-map-main li.site-main-title ul.site-main-link li.fruit-link{
    margin-left: 30px;
    margin-top: 12px;
    line-height: 1;
}
.sit-map-area ul.site-map-main li.site-main-title ul.site-main-link li.fruit-link:nth-child(2){
    margin-top: 11px;
}
.sit-map-area ul.site-map-main li.site-main-title ul.site-main-link li.fruit-link a{
    position: relative;
}
.sit-map-area ul.site-map-main li.site-main-title ul.site-main-link li.fruit-link a:before{
    background-color: #ec6504;
    content: "";
    position: absolute;
    top: 6px;
    left: -20px;
    width: 6px;
    height: 6px;
    border-radius: 100%;
}
.sit-map-area ul.site-map-main li.site-main-title ul.site-main-link li.site-link a:before{
    background-color: #ec6504;
    content: "";
    position: absolute;
    top: 8px;
    left: -20px;
    width: 6px;
    height: 6px;
    border-radius: 100%;
}
.sit-map-area ul.site-map-main li.site-main-title h4.title a.site-title span.hot {
    color: #fff;
    font-size: 9px;
    padding: 2px 4px;
    position: absolute;
    right: 8px;
    top: -15px;
    background-color: #ee433f;
    transform: translateX(100%);
    text-transform: uppercase;
    font-weight: 400;
    line-height: 1;
}
.sit-map-area ul.site-map-main li.site-main-title h4.title a.site-title span.hot::before {
    content: "";
    position: absolute;
    bottom: -4px;
    left: 0;
    border-left: 4px solid #ee433f;
    border-top: 4px solid transparent;
    border-bottom: 4px solid transparent;
} 
/* fnf page css */
.fnf-area{
    text-align: center;
}
.fnf-area h1.fnf-title{
    font-size: 180px;
    line-height: 1;
}
.fnf-area h1.fnf-title span.color-font{
    color: #ec6504;
}
.fnf-area p{
    margin-top: 16px;
    margin-bottom: 22px;
    font-weight: 500;
}
.fnf-area form{
    width: 30%;
    position: relative;
    margin: 0 auto;
}
.fnf-area form input{
    width: 100%;
    border: 1px solid #eee;
    border-radius: 25px;
}
.fnf-area a.submit{
    background-color: #ec6504;
    color: #fff;
    padding: 9px 25px;
    border-radius: 25px;
    border: 2px solid #ec6504;
    position: absolute;
    top: 0px;
    right: 0px;
    height: 100%;
}
.fnf-area a.submit:hover{
    background-color: transparent;
    color: #000;
}
.fnf-area a.back-home{
    margin-top: 30px;
    padding: 12px 25px;
    background-color: #ec6504;
    color: #fff;
    font-weight: 500;
    border: 2px solid #ec6504;
    border-radius: 25px;
}
.fnf-area a.back-home:hover{
    background-color: transparent;
    color: #000;
}
/* register page css */
.register-area{
    max-width: 60%;
    margin: 0 auto;
    display: flex;
    align-items: center;
    justify-content: center;
}
.register-area .register-box{
    width: 50%;
    padding: 30px;
    border: 1px solid #eee;
}
.register-area .register-box h1{
    font-size: 30px;
    text-align: center;
}
.register-area .register-box p{
    margin-top: 10px;
    text-align: center;
}
.register-area .register-box form input{
    width: 100%;
    padding: 10px 15px;
    margin-top: 20px;
    font-size: 13px;
    border: 1px solid #eee;
    border-radius: 3px;
}.register-area .register-box form input:first-child {
    margin-top: 0px;
}
.register-area .register-box a.btn-style1{
    width: 100%;
    margin-top: 30px;
    text-align: center;
}
.register-area .register-account{
    width: calc(50% - 30px);
    margin-left: 30px;
}
.register-area .register-account h4{
    font-size: 16px;
    text-align: center;
}
.register-area .register-account a.ceate-a{
    position: relative;
    color: #ec6504;
    width: 100%;
    padding: 13px 25px;
    margin-top: 30px;
    font-weight: 600;
    display: flex;
    align-items: center;
    justify-content: center;
    border: 2px solid #ec6504;
    border-radius: 3px;
    -webkit-transition: all 0.2s ease-in-out 0s;
    -o-transition: all 0.2s ease-in-out 0s;
    transition: all 0.2s ease-in-out 0s;
}
.register-area .register-account a.ceate-a::after{
    background-color: #ec6504;
    content: "";
    position: absolute;
    top: 0px;
    right: 0px;
    left: 0px;
    width: 100%;
    height: 100%;
    opacity: 0;
    visibility: hidden;
}
.register-area .register-account a.ceate-a:hover::after{
    opacity: 0.1;
    visibility: visible;
    -webkit-transition: all 0.2s ease-in-out 0s;
    -o-transition: all 0.2s ease-in-out 0s;
    transition: all 0.2s ease-in-out 0s;
}
.register-area .register-account .register-info a.terms-link span{
    color: #000;
    line-height: 1;
    font-size: 12px;
}
.register-area .register-account .register-info a.terms-link{
    color: #ec6504;
    margin-top: 30px;
    font-size: 12px;
    font-weight: 600;
}
.register-area .register-account .register-info p{
    font-size: 13px;
    margin-top: 10px;
}
.register-area .register-account .register-info p a{
    color: #ec6504;
    font-weight: 600;
}
/* login page css */
.login-area{
    max-width: 60%;
    margin: 0 auto;
    display: flex;
    align-items: center;
    justify-content: center;
}
.login-area .login-box{
    width: 50%;
    padding: 30px;
    border: 1px solid #eee;
}
.login-area .login-box h1{
    font-size: 30px;
    text-align: center;
}
.login-area .login-box p{
    margin-top: 10px;
    text-align: center;
}
.login-area .login-box form {
    margin-top: 17px;
}
.login-area .login-box form label{
    margin-top: 15px;
}
.login-area .login-box form label:first-child {
    margin-top: 0px;
}
.login-area .login-box form input{
    width: 100%;
    padding: 10px 15px;
    font-size: 13px;
    margin-top: 10px;
    border: 1px solid #eee;
    border-radius: 3px;
}
.login-area .login-box a.btn-style1{
    width: 100%;
    margin-top: 30px;
    text-align: center;
}
.login-area .login-box a.re-password{
    color: #ec6504;
    margin-top: 25px;
    font-weight: 600;
    display: flex;
    align-items: center;
    justify-content: center;
    text-align: center;
}
.login-area .login-account{
    width: calc(50% - 30px);
    margin-left: 30px;
}
.login-area .login-account h4{
    font-size: 16px;
    text-align: center;
}
.login-area .login-account a.ceate-a{
    position: relative;
    color: #ec6504;
    width: 100%;
    padding: 13px 25px;
    margin-top: 23px;
    font-weight: 600;
    display: flex;
    align-items: center;
    justify-content: center;
    border: 2px solid #ec6504;
    border-radius: 3px;
    -webkit-transition: all 0.2s ease-in-out 0s;
    -o-transition: all 0.2s ease-in-out 0s;
    transition: all 0.2s ease-in-out 0s;
}
.login-area .login-account a.ceate-a::after{
    background-color: #ec6504;
    content: "";
    position: absolute;
    top: 0px;
    right: 0px;
    left: 0px;
    width: 100%;
    height: 100%;
    opacity: 0;
    visibility: hidden;
}
.login-area .login-account a.ceate-a:hover::after{
    opacity: 0.1;
    visibility: visible;
    -webkit-transition: all 0.2s ease-in-out 0s;
    -o-transition: all 0.2s ease-in-out 0s;
    transition: all 0.2s ease-in-out 0s;
}
.login-area .login-account .login-info {
    margin-top: 24px;
}
.login-area .login-account .login-info a.terms-link span{
    color: #000;
    line-height: 1;
    font-size: 12px;
}
.login-area .login-account .login-info a.terms-link{
    color: #ec6504;
    font-size: 12px;
    font-weight: 600;
    
}
.login-area .login-account .login-info p{
    font-size: 13px;
    margin-top: 6px;
}
.login-area .login-account .login-info p a{
    color: #ec6504;
    font-weight: 600;
}
/* checkout style-1 css */
.checkout-area{
    display: flex;
    flex-wrap: wrap;
    align-items: flex-start;
    margin-left: -30px;
}
.checkout-area .billing-area{
    width: calc(60% - 30px);
    margin-left: 30px;
    border: 1px solid #eee;
}
.checkout-area .billing-area form h2{
    font-size: 24px;
}
.checkout-area .billing-area form{
    padding: 20px;
}
.checkout-area .billing-area form .billing-form {
    margin-top: 16px;
}
.checkout-area .billing-area form .billing-form ul.billing-ul{
    width: 100%;
    margin-top: 15px;
}
.checkout-area .billing-area form .billing-form ul.billing-ul.input-2{
    display: flex;
    flex-wrap: wrap;
}
.checkout-area .billing-area form .billing-form ul.billing-ul:first-child {
    margin-top: 0px;
}
.checkout-area .billing-area form .billing-form ul.billing-ul.input-2 li.billing-li{
    width: calc(50% - 15px);
    margin-left: 15px;
}
.checkout-area .billing-area form .billing-form ul.billing-ul.input-2 li.billing-li:first-child{
    margin-left: 0px;
    width: calc(50% - 0px);
    margin-left: 0px;
}
.checkout-area .billing-area form .billing-form ul.billing-ul li.billing-li input{
    width: 100%;
    margin-top: 10px;
}
.checkout-area .billing-area form .billing-form ul.billing-ul li.billing-li select{
    width: 100%;
    margin-top: 10px;
}
.checkout-area .billing-area .billing-details{
    border-top: 1px solid #eee;
}
.checkout-area .billing-area .billing-details form h2{
    font-size: 24px;  
}
.checkout-area .billing-area .billing-details ul.shipping-form {
    margin-top: 19px;
}
.checkout-area .billing-area .billing-details ul.shipping-form li.check-box{
    display: flex;
    align-items: center;
}
.checkout-area .billing-area .billing-details ul.shipping-form li.check-box input {
    margin-right: 5px;
}
.checkout-area .billing-area .billing-details ul.shipping-form li.comment-area{
    margin-top: 11px;
}
.checkout-area .billing-area .billing-details ul.shipping-form li label{
    margin-bottom: 0px;
}
.checkout-area .billing-area .billing-details ul.shipping-form li textarea{
    margin-top: 10px;
    width: 100%;
}
.checkout-area .order-area{
    width: calc(40% - 30px);
    margin-left: 30px;
    border: 1px solid #eee;
}
.checkout-area .order-area{
    position: sticky;
    top: 0px;
}
.checkout-area .order-area .check-pro h2{
    background-color: #f5f5f5;
    padding: 10px 20px;
    font-size: 18px;
}
.checkout-area .order-area .check-pro ul.check-ul li{
    padding: 20px;
    display: flex;
    align-items: flex-start;
    border-bottom: 1px solid #eee;
}
.checkout-area .order-area .check-pro ul.check-ul li .check-pro-img{
    width: 25%;
}
.checkout-area .order-area .check-pro ul.check-ul li .check-content{
    width: 75%;
    margin-left: 15px;
}
.checkout-area .order-area .check-pro ul.check-ul li .check-content a{
    display: block;
    font-weight: 400;
}
.checkout-area .order-area .check-pro ul.check-ul li .check-content span.check-code-blod{
    display: block;
    margin-top: 4px;
    font-weight: 600;
}
.checkout-area .order-area .check-pro ul.check-ul li .check-content span.check-code-blod span{
    font-weight: 500;
}
.checkout-area .order-area .check-pro ul.check-ul li .check-content span.check-price{
    font-size: 15px;
    font-weight: 600;
    margin-top: 5px;
}
.checkout-area .order-area h2{
    background-color: #f5f5f5;
    padding: 10px 20px;
    font-size: 18px;
}
.checkout-area .order-area ul.order-history{
    padding: 0px 20px;
}
.checkout-area .order-area ul.order-history li.order-details{
    margin-top: 14px;
    display: flex;
    align-items: center;
    justify-content: space-between;
}
.checkout-area .order-area ul.order-history li.order-details:first-child{
    margin-top: 0px;
    border: none;
}
.checkout-area .order-area ul.order-history li.order-details:last-child{
    padding-top: 15px;
    padding-bottom: 14px;
    margin-top: 14px;
    border-top: 1px solid #eee;
    border-bottom: 1px solid #eee;
}
.checkout-area .order-area ul.order-history li.order-details:last-child span{
    font-weight: 600;
}
.checkout-area .order-area form{
    padding: 0px 20px;
}
.checkout-area .order-area form ul.order-form {
    margin-top: 20px;
}
.checkout-area .order-area form ul.order-form li{
    margin-top: 19px;
    display: flex;
    align-items: center;
    line-height: 1;
}
.checkout-area .order-area form ul.order-form li:first-child{
    margin-top: 0px;
}
.checkout-area .order-area form ul.order-form li input{
    margin-right: 10px;
}
.checkout-area .order-area form ul.order-form label{
    margin-bottom: 0px;
}
.checkout-area .order-area form ul.order-form li.pay-icon a{
    font-size: 25px;
    margin-left: 10px;
}
.checkout-area .order-area form ul.order-form li.pay-icon a:first-child{
    margin-left: 0px;
}
.checkout-area .order-area .checkout-btn{
    padding: 28px 20px 20px 20px;
}
.checkout-area .order-area .checkout-btn a.btn-style1{
    display: block;
    text-align: center;
}
/* checkout style-2 tab css */
.checkout-tab ul.nav.nav-tabs{
    margin-bottom: 22px;
    border-bottom: none;
    display: flex;
    align-items: center;
    justify-content: center;
}
.checkout-tab ul.nav.nav-tabs li.nav-item{
    margin-left: 40px;
}
.checkout-tab ul.nav.nav-tabs li.nav-item:first-child{
    margin-left: 0px;
}
.checkout-tab ul.nav.nav-tabs li.nav-item a.nav-link{
    font-size: 18px;
    width: 40px;
    height: 40px;
    border-radius: 100%;
    background-color: #fff;
    color: #333;
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: 0 0 15px rgb(0 0 0 / 15%);
    font-weight: 600;
    border: none;
}
.checkout-tab ul.nav.nav-tabs li.nav-item a.nav-link.active{
    color: #ec6504;
}
.checkout-style-2 .billing-area form h2{
    font-size: 24px;
}
.checkout-style-2 .billing-area form .billing-form {
    margin-top: 16px;
}
.checkout-style-2 .billing-area form .billing-form ul.billing-ul{
    width: 100%;
    margin-top: 15px;
}
.checkout-style-2 .billing-area form .billing-form ul.billing-ul:first-child {
    margin-top: 0px;
}
.checkout-style-2 .billing-area form .billing-form ul.billing-ul.input-2{
    display: flex;
    flex-wrap: wrap;
}
.checkout-style-2 .billing-area form .billing-form ul.billing-ul.input-2 li.billing-li{
    width: calc(50% - 15px);
    margin-left: 15px;
}
.checkout-style-2 .billing-area form .billing-form ul.billing-ul.input-2 li.billing-li:first-child{
    margin-left: 0px;
    width: calc(50% - 0px);
    margin-left: 0px;
}
.checkout-style-2 .billing-area form .billing-form ul.billing-ul li.billing-li input{
    width: 100%;
    margin-top: 10px;
}
.checkout-style-2 .billing-area form .billing-form ul.billing-ul li.billing-li select{
    width: 100%;
    margin-top: 10px;
}
.checkout-style-2 .billing-area form .billing-form ul.billing-ul li.billing-li option{
    margin-top: 10px;
}
.checkout-style-2 .billing-area .billing-details form h2{
    font-size: 24px;
}
.checkout-style-2 .billing-area .billing-details ul.shipping-form {
    margin-top: 19px;
}
.checkout-style-2 .billing-area .billing-details ul.shipping-form li{
    margin-top: 9px;
}
.checkout-style-2 .billing-area .billing-details ul.shipping-form li.check-box {
    display: flex;
    align-items: center;
}
.checkout-style-2 .billing-area .billing-details ul.shipping-form li.check-box input {
    margin-right: 5px;
}
.checkout-style-2 .billing-area .billing-details ul.shipping-form li:first-child {
    margin-top: 0px;
}
.checkout-style-2 .billing-area .billing-details ul.shipping-form li label{
    margin-bottom: 0px;
}
.checkout-style-2 .billing-area .billing-details ul.shipping-form li textarea{
    margin-top: 10px;
    width: 100%;
}
.checkout-style-2 .order-area{
    margin-left: -20px;
}
.checkout-style-2 .order-area .check-pro{
    position: sticky;
    top: 0px;
    width: calc(50% - 30px);
    margin-left: 30px;
}
.checkout-style-2 .order-area .check-pro h2{
    font-size: 24px;
}
.checkout-style-2 .order-area .check-pro ul.check-ul{
    margin-top: 20px;
}
.checkout-style-2 .order-area .check-pro ul.check-ul li{
    display: flex;
    align-items: flex-start;
    margin-bottom: 20px;
    padding-bottom: 20px;
    border-bottom: 1px solid #eee;
}
.checkout-style-2 .order-area .check-pro ul.check-ul li:last-child{
    border: none;
    padding-bottom: 0px;
    margin-bottom: 0px;
}
.checkout-style-2 .order-area .check-pro ul.check-ul li .check-content{
    margin-left: 15px;
}
.checkout-style-2 .order-area .check-pro ul.check-ul li .check-content a{
    display: block;
    font-weight: 400;
}
.checkout-style-2 .order-area .check-pro ul.check-ul li .check-content span.check-code-blod{
    display: block;
    margin-top: 4px;
    font-weight: 600;
}
.checkout-style-2 .order-area .check-pro ul.check-ul li .check-content span.check-code-blod span{
    font-weight: 500;
}
.checkout-style-2 .order-area .check-pro ul.check-ul li .check-content span.check-price{
    font-size: 15px;
    font-weight: 600;
    margin-top: 5px;
}
.checkout-style-2 .order-area{
    display: flex;
    justify-content: space-between;
    margin-left: -30px;
}
.checkout-style-2 .order-area .order-history{
    width: calc(50% - 30px);
    margin-left: 30px;
}
.checkout-style-2 .order-area .order-history h2{
    font-size: 18px;    
}
.checkout-style-2 .order-area .order-history .order-inf {
    margin-top: 23px;
}
.checkout-style-2 .order-area .order-history .order-inf .order-details{
    margin-top: 13px;
    padding-top: 13px;
    display: flex;
    align-items: center;
    justify-content: space-between;   
}
.checkout-style-2 .order-area .order-history .order-inf .order-details:first-child {
    margin-top: 0px;
}
.checkout-style-2 .order-area .order-history .order-inf .order-details span{
    font-size: 16px;
    font-weight: 500;
}
.checkout-style-2 .order-area .order-history .order-inf .order-details.last{
    padding-top: 13px;
    padding-bottom: 13px;
    margin-top: 12px;
    border-top: 1px solid #eee;
    border-bottom: 1px solid #eee;
}
.checkout-style-2 .order-area .order-history .order-inf .order-details.last span{
    font-weight: 600;
}
.checkout-style-2 .order-area .order-history .order-inf form .order-form {
    margin-top: 19px;
}
.checkout-style-2 .order-area .order-history .order-inf form .order-form .order-checkbox {
    margin-top: 19px;
    display: flex;
    align-items: center;
    line-height: 1;
}
.checkout-style-2 .order-area .order-history .order-inf form .order-form .order-checkbox :first-child {
    margin-top: 0px;
}
.checkout-style-2 .order-area .order-history .order-inf form .order-form .order-checkbox input{
    margin-right: 10px;
}
.checkout-style-2 .order-area .order-history .order-inf form .order-form .order-checkbox label{
    margin-bottom: 0px;
}
.checkout-style-2 .order-area .order-history .order-inf form .order-form .pay-icon {
    margin-top: 19px;
}
.checkout-style-2 .order-area .order-history .order-inf form .order-form .pay-icon a{
    font-size: 25px;
    margin-left: 15px;
    line-height: 1;
}
.checkout-style-2 .order-area .order-history .order-inf form .order-form .pay-icon a:first-child{
    margin-left: 0px;
}
.checkout-style-2 .order-area .order-history .order-inf a.btn-style1{
    margin-top: 28px;
    display: block;
    text-align: center;
}
/* checkout style-3 css */
.check-3-start{
    margin-left: -30px;
    display: flex;
    align-items: flex-start;
}
.check-3 h2.style-3-title{
    font-size: 30px;
    margin-bottom: 30px;
    text-align: center;
}
.check-3-start .check-out-3{
    width: calc(33.33% - 30px);
    margin-left: 30px;
    border: 1px solid #eee;
}
.check-3-start .check-out-3 .check-pro h2{
    background-color: #f5f5f5;
    font-size: 18px;
    padding: 15px;
    line-height: 1;
}
.check-3-start .check-out-3 .check-pro ul.check-ul li{
    display: flex;
    align-items: flex-start;
    padding: 20px;
    border-bottom: 1px solid #eee;
}
.check-3-start .check-out-3 .check-pro ul.check-ul li .check-content{
    margin-left: 15px;
}
.check-3-start .check-out-3 .check-pro ul.check-ul li .check-content a{
    display: block;
    font-weight: 600;
}
.check-3-start .check-out-3 .check-pro ul.check-ul li .check-content span.check-code-blod{
    display: block;
    margin-top: 4px;
    font-weight: 600;
}
.check-3-start .check-out-3 .check-pro ul.check-ul li .check-content span.check-code-blod span{
    font-weight: 500;
}
.check-3-start .check-out-3 .check-pro ul.check-ul li .check-content span.check-price{
    font-size: 15px;
    font-weight: 600;
    margin-top: 5px;
}
.check-3-start .check-out-3 .check-pro form{
    padding: 20px;
}
.check-3-start .check-out-3 .check-pro form ul.style-3-check-pro li label{
    width: 100%;
    color: #333;
    font-size: 13px;
    margin-bottom: 0px;
    font-weight: 600;
}
.check-3-start .check-out-3 .check-pro form ul.style-3-check-pro li select{
    width: 100%;
    padding: 7px 10px;
    margin-top: 10px;
    border: 1px solid #eee;
    border-radius: 4px;
}
.check-3-start .check-out-3 .check-pro form ul.style-3-check-pro.selector{
    margin-top: 18px;
}
.check-3-start .check-out-3 .check-pro form ul.style-3-check-pro.selector li span{
    color: #333;
    display: block;
    font-size: 13px;
    font-weight: 600;
    margin-bottom: 2px;
    line-height: 1;
}
.check-3-start .check-out-3 .check-pro form ul.style-3-check-pro.selector li{
    margin-top: 9px;
    display: flex;
    align-items: center;
}
.check-3-start .check-out-3 .check-pro form ul.style-3-check-pro.selector li:first-child {
    margin-top: 0px;
}
.check-3-start .check-out-3 .check-pro form ul.style-3-check-pro.selector li input{
    margin-right: 5px;
}
.check-3-start .check-out-3 .check-pro .pay-op{
    padding: 20px;
    border-top: 1px solid #eee;
}
.check-3-start .check-out-3 .check-pro .pay-op span{
    display: block;
    font-size: 13px;
    color: #333;
    font-weight: 600;
}
.check-3-start .check-out-3 .check-pro .pay-op a.pay-link{
    width: 100%;
    height: 45px;
    color: #fff;
    text-align: center;
    border-radius: 4px;
    display: flex;
    align-items: center;
    justify-content: center;
}
.check-3-start .check-out-3 .check-pro .pay-op a.pay-link.pay-link-1{
    background-color: #ffc439;
    color: #000;
    margin-top: 14px;
}
.check-3-start .check-out-3 .check-pro .pay-op a.pay-link.pay-link-2{
    background-color: #000;
    margin-top: 20px;
}
.check-3-start .check-out-3 .check-pro .pay-op a.pay-link i{
    margin-left: 5px;
}
.check-3-start .check-out-3 .check-add h2{
    background-color: #f5f5f5;
    padding: 15px;
    font-size: 18px;
    line-height: 1;
}
.check-3-start .check-out-3 .check-add form{
    padding: 20px;
}
.check-3-start .check-out-3 .check-add form ul li{
    margin-top: 14px;
}
.check-3-start .check-out-3 .check-add form ul li:first-child {
    margin-top: 0px;
}
.check-3-start .check-out-3 .check-add form ul li label{
    margin-bottom: 0px;
    font-size: 13px;
    font-weight: 600;
}
.check-3-start .check-out-3 .check-add form ul li input{
    width: 100%;
    padding: 8px 13px;
    margin-top: 10px;
    border: 1px solid #eee;
    border-radius: 4px;
}
.check-3-start .check-out-3 .check-add form ul li select{
    width: 100%;
    margin-top: 10px;
    border: 1px solid #eee;
    padding: 8px 13px;
    border-radius: 4px;
}
.check-3-start .check-out-3 .pay-method h2{
    background-color: #f5f5f5;
    padding: 15px;
    font-size: 18px;
    
    line-height: 1;
}
.check-3-start .check-out-3 .pay-method .p-method ul li{
    border-bottom: 1px solid #eee;
}
.check-3-start .check-out-3 .pay-method .p-method ul li a{
    width: 100%;
    padding: 18px 24px;
}
.check-3-start .check-out-3 .pay-method .p-method ul li a span.p-labal{
    display: flex;
    align-items: center;
}
.check-3-start .check-out-3 .pay-method .p-method ul li a span.p-labal i{
    font-size: 22px;
    margin-right: 10px;
}
.check-3-start .check-out-3 .pay-method .order-summary ul li{
    padding: 14px 24px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    border-bottom: 1px solid #eee;
}
.check-3-start .check-out-3 .pay-method .order-summary ul li:last-child{
    border-bottom: none;
}
.check-3-start .check-out-3 .pay-method .order-summary ul li.order-total{
    background-color: #eee;
    padding: 15px 20px;
}
.check-3-start .check-out-3 .pay-method .order-summary ul li span.p-name{
    font-weight: 500;
}
.check-3-start .check-out-3 .pay-method .order-summary ul li span.p-price{
    font-weight: 600;
}
.check-3-start .check-out-3 .pay-method .order-summary .check-btn{
    padding: 30px 20px 20px 20px;
}
.check-3-start .check-out-3 .pay-method .order-summary .check-btn a.btn-style1{
    width: 100%;
    text-align: center;
}
/* account page css */
.account-title h1{
    font-size: 30px;
    margin-bottom: 30px;
    text-align: center;
}
.account-area{
    padding: 15px;
    display: flex;
    align-items: flex-start;
    justify-content: space-between;
    box-shadow: 0px 0px 10px 0px rgb(0 0 0 / 10%);
}
.account-area .account{
    width: 50%;
}
.account-area .account h4{
    font-size: 18px;
    margin-bottom: 15px;
}
.account-area .account ul.page-name li.register-id a{
    color: #ec6504;
    margin-top: 5px;
    display: flex;
}
.account-area .account-detail{
    width: 50%;
}
.account-area .account-detail h4{
    font-size: 18px;
    margin-bottom: 15px;
}
.account-area .account-detail ul.a-details li.mail-register{
    margin-top: 3px;
}
.order-details{
    margin-top: 30px;
}
.order-details h4{
    font-size: 18px;
    margin-bottom: 19px;
}
.order-details p{
    padding: 15px;
    box-shadow: 0px 0px 10px 0px rgb(0 0 0 / 10%);
}
/* address page css */
.address-title h1{
    font-size: 30px;
    margin-bottom: 30px;
    text-align: center;
}
.account-link a{
    color: #ec6504;
    text-decoration: underline;
}
.add-area a.address-link{
    padding: 45px;
    margin-top: 30px;
    text-align: center;
    box-shadow: 0px 0px 10px 0px rgb(0 0 0 / 10%);
}
.add-area a.address-link{
    color: #828282;
    text-align: center;
    display: block;
}
.add-area a.address-link:hover{
    color: #ec6504;
}
.add-area .address-link i{
    display: block;
    font-size: 40px;
    margin-bottom: 5px;
}
.add-area .add-title h4{
    margin-top: 30px;
    font-size: 18px;
    
    line-height: 1;
}
.add-area .address-content ul.address-input{
    display: flex;
    flex-wrap: wrap;
    margin-left: -30px;
}
.add-area .address-content ul.address-input li.type-add{
    width: calc(50% - 30px);
    margin-left: 30px;
    margin-top: 30px;
}
.add-area .address-content ul.address-input li.type-add label{
    margin-bottom: 5px;
}
.add-area .address-content ul.address-input li.type-add input{
    width: 100%;
    padding: 8px 10px;
    font-size: 13px;
    border:1px solid #eee;
    border-radius: 4px;
}
.add-area .address-content ul.address-input li.type-add select{
    width: 100%;
    padding: 8px 10px;
    font-size: 13px;
    border:1px solid #eee;
    border-radius: 4px;
}
.add-area .address-content label.check{
    margin-top: 30px;
    cursor: pointer;
}
.add-area .address-content .add-link{
    margin-top: 30px;
}
.add-area .address-content .add-link a:last-child{
    margin-left: 10px;
}
/* shipping page css */
.shipping-title h1{
    font-size: 30px;
    margin-bottom: 30px;
    text-align: center;
}
.shipping{
    margin-bottom: 22px;
}
.shipping span{
    background-color: #fff;
    display: block;
    width: 40px;
    height: 40px;
    font-size: 15px;
    font-weight: 600;
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: 0px 0px 12px 0px rgb(0 0 0 / 10%);
}
.shipping h4.shipping-title{
    background-color: #ec6504;
    color: #fff;
    padding: 10px 15px;
    font-size: 16px;
    margin-bottom: 24px;
    display: block;
    font-weight: 600;
}
.shipping .shipping-content p{
    margin-top: 15px;
}
.shipping .shipping-content p:first-child{
    margin-top: 0px;
}
/* header style-1 products css */
.header-style-pro{
    display: flex;
    flex-wrap: wrap;
    margin-left: -30px;
}
.header-style-pro .header-pro{
    width: calc(25% - 30px);
    margin-left: 30px;
    margin-top: 30px;
}
.header-style-pro .header-pro:nth-child(1),
.header-style-pro .header-pro:nth-child(2),
.header-style-pro .header-pro:nth-child(3),
.header-style-pro .header-pro:nth-child(4){
    margin-top: 0px
}
/* footer style-1 products css */
.footer-style-1-pro{
    display: flex;
    flex-wrap: wrap;
    margin-left: -30px;
}
.footer-style-1-pro .header-pro{
    width: calc(50% - 30px);
    margin-left: 30px;
    margin-top: 30px;
    display: flex;
    flex-wrap: wrap;
}
.footer-style-1-pro .header-pro:nth-child(1),
.footer-style-1-pro .header-pro:nth-child(2){
    margin-top: 0px;
}
.footer-style-1-pro .header-pro:nth-child(3),
.footer-style-1-pro .header-pro:nth-child(4){
    margin-top: 30px;
}
.footer-style-1-pro .header-pro .tred-pro{
    width: 40%;
}
.footer-style-1-pro .header-pro .caption{
    width: calc(60% - 20px);
    margin-left: 20px;
    padding-top: 0px;
}
.footer-style-1-pro .header-pro .caption h3 a{
    font-weight: 600;
    margin-bottom: 5px;
}
.footer-style-1-pro .header-pro .caption .pro-icn{
    position: unset;
    margin-top: 12px;
}
.footer-style-1-pro .header-pro .caption .pro-icn a.w-c-q-icn i{
    background-color: #ec6504;
    color: #fff;
    width: 40px;
    height: 40px;
    display: flex;
    justify-content: center;
    align-items: center;
    line-height: 0px;
    font-size: 14px;
    border-radius: 100%;
    border: 2px solid #ec6504;
    -webkit-transition: all 0.2s ease-in-out 0s;
    -o-transition: all 0.2s ease-in-out 0s;
    transition: all 0.2s ease-in-out 0s;
}
.footer-style-1-pro .header-pro .caption .pro-icn a.w-c-q-icn:hover i {
    background-color: transparent;
    color: #ec6504;
}
/* other page filter css */
.all-filter .categories-page-filter h4{
    font-size: 16px;
    padding-bottom: 23px;
}
.all-filter .categories-page-filter a.filter-link{
    display: none;
}
.all-filter .categories-page-filter ul.all-option{
    height: 271px;
    padding-top: 30px;
    overflow: auto;
    padding-right: 7px;
    border-top: 1px solid #eee;
}
.all-filter .categories-page-filter ul.all-option.collapse:not(.show){
    display: block;
}
.all-filter .categories-page-filter ul.all-option li.grid-list-option{
    margin-top: 16px;
    display: flex;
    align-items: center;
}
.all-filter .categories-page-filter ul.all-option li.grid-list-option:first-child{
    margin-top: 0px;
}
.all-filter .categories-page-filter ul.all-option li.grid-list-option input{
    width: 19px;
    height: 16px;
}
.all-filter .categories-page-filter ul.all-option li.grid-list-option a{
    width: 100%;
    margin-left: 8px;
    font-size: 13px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    line-height: 1;
}
.all-filter .price-filter h4{
    font-size: 16px;
    padding-top: 28px;
    padding-bottom: 27px;
    line-height: 1;
}
.all-filter .price-filter a.filter-link{
    display: none;
}
.all-filter .price-filter ul.all-price{
    padding-top: 30px;
    border-top: 1px solid #eee;
}
.all-filter .price-filter ul.all-price.collapse:not(.show){
    display: block;
}
.all-filter .price-filter ul.all-price li.f-price{
    margin-top: 16px;
    display: flex;
    align-items: center;
}
.all-filter .price-filter ul.all-price li.f-price:first-child{
    margin-top: 0px;
}
.all-filter .price-filter ul.all-price li.f-price input{
    width: 18px;
    height: 16px;
    cursor: pointer;
}
.all-filter .price-filter ul.all-price li.f-price label{
    margin-bottom: 0px;
    margin-left: 9px;
    font-size: 13px;
    cursor: pointer;
    line-height: 1;
}
.all-filter .pro-size h4{
    font-size: 16px;
    padding-top: 28px;
    padding-bottom: 27px;
    line-height: 1;
}
.all-filter .pro-size a.filter-link{
    display: none;
}
.all-filter .pro-size ul.all-size.collapse:not(.show){
    display: block;
}
.all-filter .pro-size ul.all-size{
    padding-top: 30px;
    border-top: 1px solid #eee;
}
.all-filter .pro-size ul.all-size li.choice-size{
    margin-top: 16px;
    display: flex;
    align-items: center;
    cursor: pointer;
}
.all-filter .pro-size ul.all-size li.choice-size:first-child{
    margin-top: 0px;
}
.all-filter .pro-size ul.all-size li.choice-size input{
    width: 18px;
    height: 16px;
    cursor: pointer;
}
.all-filter .pro-size ul.all-size li.choice-size label{
    font-size: 13px;
    margin-left: 8px;
    margin-bottom: 0px;
    cursor: pointer;
    line-height: 1;
}
.all-filter .filter-tag h4{
    font-size: 16px;
    padding-top: 27px;
    padding-bottom: 27px;
    line-height: 1;
}
.all-filter .filter-tag a.filter-link{
    display: none;
}
.all-filter .filter-tag ul.all-tag.collapse:not(.show){
    display: block;
}
.all-filter .filter-tag ul.all-tag {
    padding-top: 15px;
    margin-right: -5px;
}
.all-filter .filter-tag ul.all-tag li.tag{
    display: inline-block;
    margin-top: 10px;
    margin-right: 5px;
}
.all-filter .filter-tag ul.all-tag li.tag a{
    padding: 9px 16px;
    color: #808080;
    font-size: 12px;
    border: 1px solid #eee;
    border-radius: 50px;
}
.all-filter .filter-tag ul.all-tag li.tag a:hover{
    background-color: #000;
    color: #fff;
}
.all-filter .vendor-filter h4{
    font-size: 16px;
    padding-top: 28px;
    padding-bottom: 27px;
    line-height: 1;
    border-bottom: 1px solid #eee;
}
.all-filter .vendor-filter a.filter-link{
    display: none;
}
.all-filter .vendor-filter ul.all-vendor{
    padding-top: 30px;
}
.all-filter .vendor-filter ul.all-vendor.collapse:not(.show){
    display: block;
}
.all-filter .vendor-filter ul.all-vendor li.f-vendor{
    margin-top: 16px;
    display: flex;
    align-items: center;
}
.all-filter .vendor-filter ul.all-vendor li.f-vendor:first-child{
    margin-top: 0px;
}
.all-filter .vendor-filter ul.all-vendor li.f-vendor input{
    width: 18px;
    height: 16px;
}
.all-filter .vendor-filter ul.all-vendor li.f-vendor label{
    margin-bottom: 0px;
    margin-left: 8px;
    font-size: 13px;
    cursor: pointer;
    line-height: 1;
}
.all-filter .filter-banner a.grid-banner{
    margin-top: 30px;
    position: relative;
    overflow: hidden;
    display: block;
}
.all-filter .filter-banner a.grid-banner img{
    width: 100%;
    -webkit-transition: all 0.3s ease-in-out 0s;
    -o-transition: all 0.3s ease-in-out 0s;
    transition: all 0.3s ease-in-out 0s;
}
.all-filter .filter-banner a.grid-banner:hover img {
    transform: scale(1.1);
    -webkit-transition: all 0.3s ease-in-out 0s;
    -o-transition: all 0.3s ease-in-out 0s;
    transition: all 0.3s ease-in-out 0s;
}
/* range price css */
.all-filter .price-filter ul.all-price .price-range {
    display: block;
}
.all-filter .price-filter ul.all-price .price-range .price-range-group {
    position: relative;
    margin-bottom: 30px;
}

/* group range (when using double range, we need to rely on some clever trick) */
.all-filter .price-filter ul.all-price .price-range .group-range {
    --range-min: 0.0%;
    --range-max: 100.0%;
    height: 2px;
    background: linear-gradient(to right, #e2e2e2 0.0%, #333333 0.0%, #333333 100.0%, #e2e2e2 100.0%);
}

/* first we revert the styling of range elements */
.all-filter .price-filter ul.all-price .price-range .range {
    width: 100%;
    padding: 0;
    background: transparent;
    border: none;
    -webkit-appearance: none;
    -moz-appearance: none;
    appearance: none;
}
.all-filter .price-filter ul.all-price .price-range .range::-webkit-slider-thumb {
    -webkit-appearance: none;
}

/* chrome, safari, edge, opera */
.all-filter .price-filter ul.all-price .price-range .range::-webkit-slider-thumb {
    height: 12px;
    width: 4px;
    background: #333333;
    transform: translateY(-5px);
    border-radius: 5px;
    box-shadow: 0px 0px 0px 2px #333333;
    cursor: pointer;
}
.all-filter .price-filter ul.all-price .price-range .range::-webkit-slider-runnable-track {
    width: 100%;
    height: 2px;
    background: #333333;
    cursor: pointer;
}

/* firefox mozilla */
.all-filter .price-filter ul.all-price .price-range .range::-moz-range-thumb {
    height: 12px;
    width: 4px;
    background: #333333;
    border-radius: 5px;
    box-shadow: 0px 0px 0px 2px #333333;
    cursor: pointer;
}
.all-filter .price-filter ul.all-price .price-range .range::-moz-range-progress,
.all-filter .price-filter ul.all-price .price-range .range::-moz-range-track {
    width: 100%;
    height: 2px;
    cursor: pointer;
}
.all-filter .price-filter ul.all-price .price-range .range::-moz-range-progress {
    background-color: #333333;
}
.all-filter .price-filter ul.all-price .price-range .range::-moz-range-track {
    background-color: #e2e2e2;
}

/* group range (when using double range, we need to rely on some clever trick) */
.all-filter .price-filter ul.all-price .price-range .group-range .range {
    height: 2px;
    pointer-events: none;
    vertical-align: top;
}
.all-filter .price-filter ul.all-price .price-range .group-range .range::-webkit-slider-runnable-track {
    background: none;
}
.all-filter .price-filter ul.all-price .price-range .group-range .range::-webkit-slider-thumb {
    pointer-events: auto;
}
.all-filter .price-filter ul.all-price .price-range .group-range .range::-moz-range-progress,
.all-filter .price-filter ul.all-price .price-range .group-range .range::-moz-range-track {
    background: none;
}
.all-filter .price-filter ul.all-price .price-range .group-range .range::-moz-range-thumb {
    pointer-events: auto;
}
.all-filter .price-filter ul.all-price .price-range .group-range .range:last-child {
    position: absolute;
    top: 0;
    left: 0;
}

/* input-prefix css */
.all-filter .price-filter ul.all-price .price-range .price-input-group {
    display: flex;
    align-items: center;
}
.all-filter .price-filter ul.all-price .price-range .price-input-group .price-range-input {
    flex: 1 0 0;
    min-width: 0;
}
.all-filter .price-filter ul.all-price .price-range .price-input-group .input-prefix {
    padding: 10px 15px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    background-color: #ffffff;
    position: relative;
    border: 1px solid #e2e2e2;
    border-radius: 5px;
}
.all-filter .price-filter ul.all-price .price-range .price-input-group .input-prefix label.input-prefix-label {
    color: #333333;
    font-size: 11px;
    position: absolute;
    top: 0px;
    left: 0;
    transform: translateY(-100%);
    margin-bottom: 0px;
    opacity: 0.7;
}
.all-filter .price-filter ul.all-price .price-range .price-input-group .input-prefix span.input-prefix-value {
    font-size: 13px;
}
.all-filter .price-filter ul.all-price .price-range .price-input-group .input-prefix .input-prefix-field {
    font-size: 13px;
    padding: 0px;
    width: 100%;
    text-align: end;
    border: none;
    -webkit-appearance: none;
    -moz-appearance: none;
    appearance: none;
}

/* chrome, safari, edge, opera */
.all-filter .price-filter ul.all-price .price-range .price-input-group .input-prefix .input-prefix-field::-webkit-outer-spin-button,
.all-filter .price-filter ul.all-price .price-range .price-input-group .input-prefix .input-prefix-field::-webkit-inner-spin-button {
    -webkit-appearance: none;
    margin: 0;
}

/* firefox mozilla */
.all-filter .price-filter ul.all-price .price-range .price-input-group .input-prefix input.input-prefix-field[type=number] {
    -moz-appearance: textfield;
}
.all-filter .price-filter ul.all-price .price-range .price-range-delimeter {
    color: #333333;
    font-size: 16px;
    margin: 0px 10px;
}

/* grid-list banner-button css */
.grid-list-banner{
    height: 262px;
    background-position: bottom;
    background-repeat: no-repeat;
    background-size: cover;
    display: flex;
    align-items: center;
}
.grid-list-banner .grid-banner-content{
    width: 50%;
    margin-left: 30px;
}
.grid-list-banner .grid-banner-content h4{
    font-size: 20px;
}
.grid-list-banner .grid-banner-content p{
    font-size: 13px;
    margin-top: 14px;
    color: #333;
}
/* list product css */
.list-product{
    margin-top: 30px;
}
.list-product .list-items{
    display: flex;
    flex-wrap: wrap;
    margin-bottom: 20px;
    padding-bottom: 20px;
    border-bottom: 1px solid #eee;
}
.list-product .list-items:last-child{
    border-bottom: none;
    margin: 0px;
    padding: 0px;
}
.list-product .list-items .tred-pro{
    width: 25%;
    position: relative;
}
.list-product .list-items .tred-pro .Pro-lable span.p-text,
.list-product .list-items .tred-pro .Pro-lable span.p-discount{
    position: absolute;
    top: 5px;
    font-size: 13px;
    color: #fff;
    padding: 2px 10px 2px 15px;
    clip-path: polygon(0 0, 100% 0, 100% 100%, 0 100%, 20% 50%);
}
.list-product .list-items .tred-pro .Pro-lable span.p-text{
    left: 5px;
    background-color: #ec6504;
}
.list-product .list-items .tred-pro .Pro-lable span.p-discount{
    right: 5px;
    background-color: #e30514;
}
.list-product .list-items .caption .pro-icn{
    position: unset;
    margin-top: 14px;
}
.list-product .list-items .caption .pro-icn a.w-c-q-icn i{
    background-color: #ec6504;
    color: #fff;
    width: 40px;
    height: 40px;
    display: flex;
    justify-content: center;
    align-items: center;
    line-height: 0px;
    font-size: 16px;
    border-radius: 100%;
    border: 2px solid #ec6504;
    -webkit-transition: all 0.3s ease-in-out 0s;
    -o-transition: all 0.3s ease-in-out 0s;
    transition: all 0.3s ease-in-out 0s;
}
.list-product .list-items .caption .pro-icn a.w-c-q-icn:hover i{
    background-color: transparent;
    color: #000;
    -webkit-transition: all 0.3s ease-in-out 0s;
    -o-transition: all 0.3s ease-in-out 0s;
    transition: all 0.3s ease-in-out 0s;
}
.list-product .list-items .caption{
    width: calc(75% - 20px);
    margin-left: 20px;
    padding-top: 0px;
}
.list-product .list-items .caption h3{
    font-size: 14px;
    font-weight: 400;
}
.list-product .list-items .caption h3 a{
    display: block;
    white-space: nowrap;
    width: 100%;
    overflow: hidden;
    text-overflow: ellipsis;
    font-weight: 600;
}
.list-product .list-items .caption p.list-description{
    font-size: 13px;
    margin-top: 8px;
    line-height: 21px;
}
.list-product .list-items .caption .rating{
    display: flex;
    margin-top: 14px;
}
.list-product .list-items .caption .rating i{
    color: #ccc;
    font-size: 14px;
    margin-right: 5px;
}
.list-product .list-items .caption .rating i.b-star,
.list-product .list-items .caption .rating i.c-star,
.list-product .list-items .caption .rating i.d-star,
.list-product .list-items .caption .rating i.e-star{
    color: #ec6504;
}
.list-product .list-items .caption .rating i:last-child{
    margin-right: 0px;
}
.list-product .list-items .caption .pro-price{
    margin-top: 15px;
}
.list-product .list-items .caption .pro-price span.new-price{
    font-size: 16px;
    font-weight: 600;
    margin-right: 5px;
    line-height: 1;
}
.list-product .list-items .caption .pro-price span.old-price{
    color: #999;
    font-size: 14px;
    line-height: 1;
}
/* additional image css */
.list-product .list-items .tred-pro .tr-pro-img a img.additional-image{
    position: absolute;
    top: 0px;
    right: 0px;
    left: 0px;
    opacity: 0;
    visibility: hidden;
}
.list-product .list-items .tred-pro:hover .tr-pro-img a img.additional-image{
    opacity: 1;
    visibility: visible;
}
.list-product .list-items .tred-pro .tr-pro-img a img.additional-image,
.list-product .list-items .tred-pro:hover .tr-pro-img a img.additional-image{
    -webkit-transition: all 0.3s ease-in-out 0s;
    -o-transition: all 0.3s ease-in-out 0s;
    transition: all 0.3s ease-in-out 0s;
}
.list-product p.list-all-page{
    margin: 0 auto;
    text-align: center;
    padding-top: 30px;
    font-weight: 700;
}
.list-all-page span.page-title{
    color: #000;
    display: block;
    text-align: center;
    margin-top: 30px;
    font-weight: 600;
}
.list-all-page .page-number{
    text-align: center;
    margin-top: 20px;
}
.list-all-page .page-number a{
    position: relative;
    margin-right: 5px;
}
.list-all-page .page-number a:after{
    background-color: #ec6504;
    content: "";
    position: absolute;
    bottom: 0px;
    left: 1px;
    right: 0px;
    width: 4px;
    height: 4px;
    border-radius: 100%;
    opacity: 0;
    visibility: hidden;
}
.list-all-page .page-number a:hover:after,
.list-all-page .page-number a.active:after{
    opacity: 1;
    visibility: visible;
}
.list-all-page .page-number a:hover,
.list-all-page .page-number a.active{
    color: #ec6504;
}
.list-all-page .page-number a:last-child:after{
    display: none;
}
/* grid Products css */
.grid-list-area .grid-list-select{
    margin-top: 30px;
    display: flex;
    align-items: center;
    justify-content: space-between;
}
.grid-list-area .grid-list-select ul{
    display: flex;
    align-items: center;
}
.grid-list-area .grid-list-select ul.grid-list li a{
    font-size: 17px;
    margin-right: 10px;
}
.grid-list-area .grid-list-select ul.grid-list li a.active,
.grid-list-area .grid-list-select ul.grid-list li a.active,
.grid-4-product .grid-list-select ul.grid-list li a.active,
.grid-2-product .grid-list-select ul.grid-list li a.active {
    color: #ec6504;
}
.grid-list-area .grid-list-select ul.grid-list-selector li label{
    font-weight: 600;
    margin-right: 10px;
}
.grid-list-area .grid-list-select ul.grid-list-selector select{
    min-width: 200px;
    padding: 6px 15px;
    border: 1px solid #eee;
    border-radius: 3px;
}
.grid-list-area .grid-pro ul.grid-product{
    display: flex;
    flex-wrap: wrap;
    margin-left: -30px;
}
.grid-list-area .grid-pro ul.grid-product li.grid-items{
    width: calc(33.33% - 30px);
    margin-left: 30px;
}
.grid-list-area .grid-pro ul.grid-product li.grid-items .tred-pro{
    position: relative;
    margin-top: 30px;
}
.grid-list-area .grid-pro ul.grid-product li.grid-items .tred-pro .Pro-lable span.p-text,
.grid-list-area .grid-pro ul.grid-product li.grid-items .tred-pro .Pro-lable span.p-discount{
    position: absolute;
    top: 5px;
    font-size: 13px;
    color: #fff;
    padding: 2px 10px 2px 15px;
    clip-path: polygon(0 0, 100% 0, 100% 100%, 0 100%, 20% 50%);
}
.grid-list-area .grid-pro ul.grid-product li.grid-items .tred-pro .Pro-lable span.p-text{
    left: 5px;
    background-color: #ec6504;
}
.grid-list-area .grid-pro ul.grid-product li.grid-items .tred-pro .Pro-lable span.p-discount{
    right: 5px;
    background-color: #e30514;
}
.grid-list-area .grid-pro ul.grid-product li.grid-items .tred-pro .pro-icn{
    position: absolute;
    bottom: 15px;
    left: 0px;
    right: 0px;
    text-align: center;
    margin: 0px;
}
.grid-list-area .grid-pro ul.grid-product li.grid-items .tred-pro .pro-icn a.w-c-q-icn:first-child{
    transform: translateX(40px);
}
.grid-list-area .grid-pro ul.grid-product li.grid-items .tred-pro .pro-icn a.w-c-q-icn:last-child{
    transform: translateX(-40px);
}
.grid-list-area .grid-pro ul.grid-product li.grid-items .tred-pro:hover .pro-icn a.w-c-q-icn:first-child{
    margin-right: 15px;
}
.grid-list-area .grid-pro ul.grid-product li.grid-items .tred-pro:hover .pro-icn a.w-c-q-icn:last-child{
    margin-left: 15px;
}
.grid-list-area .grid-pro ul.grid-product li.grid-items .tred-pro:hover .pro-icn a.w-c-q-icn:first-child,
.grid-list-area .grid-pro ul.grid-product li.grid-items .tred-pro:hover .pro-icn a.w-c-q-icn:last-child{
    transform: translateX(0);
    -webkit-transition: all 0.3s ease-in-out 0s;
    -o-transition: all 0.3s ease-in-out 0s;
    transition: all 0.3s ease-in-out 0s;
}
.grid-list-area .grid-pro ul.grid-product li.grid-items .tred-pro .pro-icn a.w-c-q-icn i{
    background-color: #fff;
    color: #000;
    width: 40px;
    height: 40px;
    display: flex;
    justify-content: center;
    align-items: center;
    line-height: 0px;
    font-size: 16px;
    border-radius: 100%;
    -webkit-transition: all 0.2s ease-in-out 0s;
    -o-transition: all 0.2s ease-in-out 0s;
    transition: all 0.2s ease-in-out 0s;
    opacity: 0;
    visibility: hidden;
}
.grid-list-area .grid-pro ul.grid-product li.grid-items .tred-pro .pro-icn a.w-c-q-icn:hover i{
    color: #ec6504;
}
.grid-list-area .grid-pro ul.grid-product li.grid-items .tred-pro:hover .pro-icn a.w-c-q-icn i{
    opacity: 1;
    visibility: visible;
}
.grid-list-area .grid-pro ul.grid-product li.grid-items .caption{
    padding-top: 15px;
}
.grid-list-area .grid-pro ul.grid-product li.grid-items .caption h3{
    font-size: 14px;
    font-weight: 400;
}
.grid-list-area .grid-pro ul.grid-product li.grid-items .caption h3 a{
    display: block;
    white-space: nowrap;
    width: 100%;
    overflow: hidden;
    text-overflow: ellipsis;
}
.grid-list-area .grid-pro ul.grid-product li.grid-items .caption .rating{
    display: flex;
    margin-top: 14px;
}
.grid-list-area .grid-pro ul.grid-product li.grid-items .caption .rating i{
    color: #ccc;
    font-size: 14px;
    margin-right: 5px;
}
.grid-list-area .grid-pro ul.grid-product li.grid-items .caption .rating i.b-star,
.grid-list-area .grid-pro ul.grid-product li.grid-items .caption .rating i.c-star,
.grid-list-area .grid-pro ul.grid-product li.grid-items .caption .rating i.d-star,
.grid-list-area .grid-pro ul.grid-product li.grid-items .caption .rating i.e-star{
    color: #ec6504;
}
.grid-list-area .grid-pro ul.grid-product li.grid-items .caption .rating i:last-child{
    margin-right: 0px;
}
.grid-list-area .grid-pro ul.grid-product li.grid-items .caption .pro-price{
    margin-top: 16px;
}
.grid-list-area .grid-pro ul.grid-product li.grid-items .caption .pro-price span.new-price{
    font-size: 16px;
    font-weight: 600;
    margin-right: 5px;
    line-height: 1;
}
.grid-list-area .grid-pro ul.grid-product li.grid-items .caption .pro-price span.old-price{
    color: #999;
    font-size: 14px;
    line-height: 1;
}
/* additional image css */
.grid-list-area .grid-pro ul.grid-product li.grid-items .tred-pro .tr-pro-img a img.additional-image{
    position: absolute;
    top: 0px;
    right: 0px;
    left: 0px;
    opacity: 0;
    visibility: hidden;
}
.grid-list-area .grid-pro ul.grid-product li.grid-items .tred-pro:hover .tr-pro-img a img.additional-image{
    opacity: 1;
    visibility: visible;
}
.grid-list-area .grid-pro ul.grid-product li.grid-items .tred-pro .tr-pro-img a img.additional-image,
.grid-list-area .grid-pro ul.grid-product li.grid-items .tred-pro:hover .tr-pro-img a img.additional-image{
    -webkit-transition: all 0.3s ease-in-out 0s;
    -o-transition: all 0.3s ease-in-out 0s;
    transition: all 0.3s ease-in-out 0s;
}
.grid-list-area .grid-pro ul.grid-product li.grid-items p{
    margin: 0 auto;
    padding-top: 30px;
    font-weight: 700;
}
/* 2-grid Products css */
.grid-2-product .grid-list-select{
    margin-top: 30px;
    display: flex;
    align-items: center;
    justify-content: space-between;
}
.grid-2-product .grid-list-select ul{
    display: flex;
    align-items: center;
}
.grid-2-product .grid-list-select ul.grid-list li a{
    font-size: 17px;
    margin-right: 15px;
}
.grid-2-product .grid-list-select ul.grid-list-selector li label{
    font-weight: 600;
    margin-right: 10px;
}
.grid-2-product .grid-list-select ul.grid-list-selector select{
    min-width: 200px;
    padding: 6px 15px;
    border: 1px solid #eee;
    border-radius: 3px;
}
.grid-2-product .grid-pro ul.grid-product{
    display: flex;
    flex-wrap: wrap;
    margin-left: -30px;
}
.grid-2-product .grid-pro ul.grid-product li.grid-items{
    width: calc(50% - 30px);
    margin-left: 30px;
}
.grid-2-product .grid-pro ul.grid-product li.grid-items .tred-pro{
    position: relative;
    margin-top: 30px;
}
.grid-2-product .grid-pro ul.grid-product li.grid-items .tred-pro .Pro-lable span.p-text,
.grid-2-product .grid-pro ul.grid-product li.grid-items .tred-pro .Pro-lable span.p-discount{
    position: absolute;
    top: 5px;
    font-size: 13px;
    color: #fff;
    padding: 2px 10px 2px 15px;
    clip-path: polygon(0 0, 100% 0, 100% 100%, 0 100%, 20% 50%);
}
.grid-2-product .grid-pro ul.grid-product li.grid-items .tred-pro .Pro-lable span.p-text{
    left: 5px;
    background-color: #ec6504;
}
.grid-2-product .grid-pro ul.grid-product li.grid-items .tred-pro .Pro-lable span.p-discount{
    right: 5px;
    background-color: #e30514;
}
.grid-2-product .grid-pro ul.grid-product li.grid-items .tred-pro .pro-icn{
    position: absolute;
    bottom: 15px;
    left: 0px;
    right: 0px;
    text-align: center;
    margin: 0px;
}
.grid-2-product .grid-pro ul.grid-product li.grid-items .tred-pro .pro-icn a.w-c-q-icn:first-child{
    transform: translateX(40px);
}
.grid-2-product .grid-pro ul.grid-product li.grid-items .tred-pro .pro-icn a.w-c-q-icn:last-child{
    transform: translateX(-40px);
}
.grid-2-product .grid-pro ul.grid-product li.grid-items .tred-pro:hover .pro-icn a.w-c-q-icn:first-child{
    margin-right: 15px;
}
.grid-2-product .grid-pro ul.grid-product li.grid-items .tred-pro:hover .pro-icn a.w-c-q-icn:last-child{
    margin-left: 15px;
}
.grid-2-product .grid-pro ul.grid-product li.grid-items .tred-pro:hover .pro-icn a.w-c-q-icn:first-child,
.grid-2-product .grid-pro ul.grid-product li.grid-items .tred-pro:hover .pro-icn a.w-c-q-icn:last-child{
    transform: translateX(0);
    -webkit-transition: all 0.3s ease-in-out 0s;
    -o-transition: all 0.3s ease-in-out 0s;
    transition: all 0.3s ease-in-out 0s;
}
.grid-2-product .grid-pro ul.grid-product li.grid-items .tred-pro .pro-icn a.w-c-q-icn i{
    background-color: #fff;
    color: #000;
    width: 40px;
    height: 40px;
    display: flex;
    justify-content: center;
    align-items: center;
    line-height: 0px;
    font-size: 16px;
    border-radius: 100%;
    -webkit-transition: all 0.2s ease-in-out 0s;
    -o-transition: all 0.2s ease-in-out 0s;
    transition: all 0.2s ease-in-out 0s;
    opacity: 0;
    visibility: hidden;
}
.grid-2-product .grid-pro ul.grid-product li.grid-items .tred-pro .pro-icn a.w-c-q-icn:hover i{
    color: #ec6504;
}
.grid-2-product .grid-pro ul.grid-product li.grid-items .tred-pro:hover .pro-icn a.w-c-q-icn i{
    opacity: 1;
    visibility: visible;
}
.grid-2-product .grid-pro ul.grid-product li.grid-items .caption{
    padding-top: 15px;
}
.grid-2-product .grid-pro ul.grid-product li.grid-items .caption h3{
    font-size: 14px;
    font-weight: 400;
}
.grid-2-product .grid-pro ul.grid-product li.grid-items .caption h3 a{
    display: block;
    white-space: nowrap;
    width: 100%;
    overflow: hidden;
    text-overflow: ellipsis;
}
.grid-2-product .grid-pro ul.grid-product li.grid-items .caption .rating{
    display: flex;
    margin-top: 14px;
}
.grid-2-product .grid-pro ul.grid-product li.grid-items .caption .rating i{
    color: #ccc;
    font-size: 14px;
    margin-right: 5px;
}
.grid-2-product .grid-pro ul.grid-product li.grid-items .caption .rating i.b-star,
.grid-2-product .grid-pro ul.grid-product li.grid-items .caption .rating i.c-star,
.grid-2-product .grid-pro ul.grid-product li.grid-items .caption .rating i.d-star,
.grid-2-product .grid-pro ul.grid-product li.grid-items .caption .rating i.e-star{
    color: #ec6504;
}
.grid-2-product .grid-pro ul.grid-product li.grid-items .caption .rating i:last-child{
    margin-right: 0px;
}
.grid-2-product .grid-pro ul.grid-product li.grid-items .caption .pro-price{
    margin-top: 16px;
}
.grid-2-product .grid-pro ul.grid-product li.grid-items .caption .pro-price span.new-price{
    font-size: 16px;
    font-weight: 600;
    margin-right: 5px;
    line-height: 1;
}
.grid-2-product .grid-pro ul.grid-product li.grid-items .caption .pro-price span.old-price{
    color: #999;
    font-size: 14px;
    line-height: 1;
}
/* additional image css */
.grid-2-product .grid-pro ul.grid-product li.grid-items .tred-pro .tr-pro-img a img.additional-image{
    position: absolute;
    top: 0px;
    right: 0px;
    left: 0px;
    opacity: 0;
    visibility: hidden;
}
.grid-2-product .grid-pro ul.grid-product li.grid-items .tred-pro:hover .tr-pro-img a img.additional-image{
    opacity: 1;
    visibility: visible;
}
.grid-2-product .grid-pro ul.grid-product li.grid-items .tred-pro .tr-pro-img a img.additional-image,
.grid-2-product .grid-pro ul.grid-product li.grid-items .tred-pro:hover .tr-pro-img a img.additional-image{
    -webkit-transition: all 0.3s ease-in-out 0s;
    -o-transition: all 0.3s ease-in-out 0s;
    transition: all 0.3s ease-in-out 0s;
}
.grid-2-product .grid-pro ul.grid-product li.grid-items p{
    margin: 0 auto;
    padding-top: 30px;
    font-weight: 700;
}
/* 4-grid Products css */
.grid-4-product .grid-list-select{
    margin-top: 30px;
    display: flex;
    align-items: center;
    justify-content: space-between;
}
.grid-4-product .grid-list-select ul{
    display: flex;
    align-items: center;
}
.grid-4-product .grid-list-select ul.grid-list li a{
    font-size: 17px;
    margin-right: 10px;
}
.grid-4-product .grid-list-select ul.grid-list-selector li label{
    font-weight: 600;
    margin-right: 10px;
}
.grid-4-product .grid-list-select ul.grid-list-selector select{
    min-width: 200px;
    padding: 6px 15px;
    border: 1px solid #eee;
    border-radius: 3px;
}
.grid-4-product .grid-pro ul.grid-product{
    display: flex;
    flex-wrap: wrap;
    margin-left: -30px;
}
.grid-4-product .grid-pro ul.grid-product li.grid-items{
    width: calc(25% - 30px);
    margin-left: 30px;
}
.grid-4-product .grid-pro ul.grid-product li.grid-items .tred-pro{
    position: relative;
    margin-top: 30px;
}
.grid-4-product .grid-pro ul.grid-product li.grid-items .tred-pro .Pro-lable span.p-text,
.grid-4-product .grid-pro ul.grid-product li.grid-items .tred-pro .Pro-lable span.p-discount{
    position: absolute;
    top: 5px;
    font-size: 13px;
    color: #fff;
    padding: 2px 10px 2px 15px;
    clip-path: polygon(0 0, 100% 0, 100% 100%, 0 100%, 20% 50%);
}
.grid-4-product .grid-pro ul.grid-product li.grid-items .tred-pro .Pro-lable span.p-text{
    left: 5px;
    background-color: #ec6504;
}
.grid-4-product .grid-pro ul.grid-product li.grid-items .tred-pro .Pro-lable span.p-discount{
    right: 5px;
    background-color: #e30514;
}
.grid-4-product .grid-pro ul.grid-product li.grid-items .tred-pro .pro-icn{
    position: absolute;
    bottom: 15px;
    left: 0px;
    right: 0px;
    text-align: center;
    margin: 0px;
}
.grid-4-product .grid-pro ul.grid-product li.grid-items .tred-pro .pro-icn a.w-c-q-icn:first-child{
    transform: translateX(40px);
}
.grid-4-product .grid-pro ul.grid-product li.grid-items .tred-pro .pro-icn a.w-c-q-icn:last-child{
    transform: translateX(-40px);
}
.grid-4-product .grid-pro ul.grid-product li.grid-items .tred-pro:hover .pro-icn a.w-c-q-icn:first-child{
    margin-right: 15px;
}
.grid-4-product .grid-pro ul.grid-product li.grid-items .tred-pro:hover .pro-icn a.w-c-q-icn:last-child{
    margin-left: 15px;
}
.grid-4-product .grid-pro ul.grid-product li.grid-items .tred-pro:hover .pro-icn a.w-c-q-icn:first-child,
.grid-4-product .grid-pro ul.grid-product li.grid-items .tred-pro:hover .pro-icn a.w-c-q-icn:last-child{
    transform: translateX(0);
    -webkit-transition: all 0.3s ease-in-out 0s;
    -o-transition: all 0.3s ease-in-out 0s;
    transition: all 0.3s ease-in-out 0s;
}
.grid-4-product .grid-pro ul.grid-product li.grid-items .tred-pro .pro-icn a.w-c-q-icn i{
    background-color: #fff;
    color: #000;
    width: 40px;
    height: 40px;
    display: flex;
    justify-content: center;
    align-items: center;
    line-height: 0px;
    font-size: 16px;
    border-radius: 100%;
    -webkit-transition: all 0.2s ease-in-out 0s;
    -o-transition: all 0.2s ease-in-out 0s;
    transition: all 0.2s ease-in-out 0s;
    opacity: 0;
    visibility: hidden;
}
.grid-4-product .grid-pro ul.grid-product li.grid-items .tred-pro .pro-icn a.w-c-q-icn:hover i{
    color: #ec6504;
}
.grid-4-product .grid-pro ul.grid-product li.grid-items .tred-pro:hover .pro-icn a.w-c-q-icn i{
    opacity: 1;
    visibility: visible;
}
.grid-4-product .grid-pro ul.grid-product li.grid-items .caption{
    padding-top: 15px;
}
.grid-4-product .grid-pro ul.grid-product li.grid-items .caption h3{
    font-size: 14px;
    font-weight: 400;
}
.grid-4-product .grid-pro ul.grid-product li.grid-items .caption h3 a{
    display: block;
    white-space: nowrap;
    width: 100%;
    overflow: hidden;
    text-overflow: ellipsis;
}
.grid-4-product .grid-pro ul.grid-product li.grid-items .caption .rating{
    display: flex;
    margin-top: 14px;
}
.grid-4-product .grid-pro ul.grid-product li.grid-items .caption .rating i{
    color: #ccc;
    font-size: 14px;
    margin-right: 5px;
}
.grid-4-product .grid-pro ul.grid-product li.grid-items .caption .rating i.b-star,
.grid-4-product .grid-pro ul.grid-product li.grid-items .caption .rating i.c-star,
.grid-4-product .grid-pro ul.grid-product li.grid-items .caption .rating i.d-star,
.grid-4-product .grid-pro ul.grid-product li.grid-items .caption .rating i.e-star{
    color: #ec6504;
}
.grid-4-product .grid-pro ul.grid-product li.grid-items .caption .rating i:last-child{
    margin-right: 0px;
}
.grid-4-product .grid-pro ul.grid-product li.grid-items .caption .pro-price{
    margin-top: 16px;
}
.grid-4-product .grid-pro ul.grid-product li.grid-items .caption .pro-price span.new-price{
    font-size: 16px;
    font-weight: 600;
    margin-right: 5px;
    line-height: 1;
}
.grid-4-product .grid-pro ul.grid-product li.grid-items .caption .pro-price span.old-price{
    color: #999;
    font-size: 14px;
    line-height: 1;
}
/* additional image css */
.grid-4-product .grid-pro ul.grid-product li.grid-items .tred-pro .tr-pro-img a img.additional-image{
    position: absolute;
    top: 0px;
    right: 0px;
    left: 0px;
    opacity: 0;
    visibility: hidden;
}
.grid-4-product .grid-pro ul.grid-product li.grid-items .tred-pro:hover .tr-pro-img a img.additional-image{
    opacity: 1;
    visibility: visible;
}
.grid-4-product .grid-pro ul.grid-product li.grid-items .tred-pro .tr-pro-img a img.additional-image,
.grid-4-product .grid-pro ul.grid-product li.grid-items .tred-pro:hover .tr-pro-img a img.additional-image{
    -webkit-transition: all 0.3s ease-in-out 0s;
    -o-transition: all 0.3s ease-in-out 0s;
    transition: all 0.3s ease-in-out 0s;
}
.grid-4-product .grid-pro ul.grid-product li.grid-items p{
    margin: 0 auto;
    padding-top: 30px;
    font-weight: 700;
}
/* product page css */
.pro-page .pro-image .larg-image a.long-img{
    margin-bottom: 20px;
}
.pro-page .pro-image .larg-image a.long-img figure.zoom {
     background-position: 10% 10%;
     background-size: 130%; /* Controls zoom level - further reduced to 130% for minimal zoom effect */
     background-repeat: no-repeat;
     position: relative;
     overflow: hidden;
     cursor: crosshair;
     margin-bottom: 0px;
}
.pro-page .pro-image .larg-image a.long-img figure.zoom img:hover {
     opacity: 0;
}
.pro-page .pro-image .larg-image a.long-img figure.zoom img {
     transition: opacity 0.5s;
     display: block;
     width: 100%;
}
.pro-page .pro-image .larg-image ul.nav.nav-tabs.pro-page-slider{
    position: relative;
    border-bottom: none;
}
.pro-page .pro-image .larg-image ul.nav.nav-tabs.pro-page-slider li.nav-item a{
    padding: 0px;
    border: 1px solid transparent;
    margin-bottom: 1px;
}
.pro-page .pro-image .larg-image ul.nav.nav-tabs.pro-page-slider li.nav-item a.active {
    border: 1px solid #ec6504;
    border-radius: 0px;
}
/* slider arro */
.pro-page .pro-image .larg-image ul.nav.nav-tabs.pro-page-slider .owl-nav button {
    background-color: transparent;
    position: absolute;
    bottom: 50%;
    transform: translateY(50%);
    margin: 0px;
}
.pro-page .pro-image .larg-image ul.nav.nav-tabs.pro-page-slider .owl-nav button.owl-prev {
    left: -10px;
}   
.pro-page .pro-image .larg-image ul.nav.nav-tabs.pro-page-slider .owl-nav button.owl-next {
    right: -10px;
}
.pro-page .pro-image .larg-image ul.nav.nav-tabs.pro-page-slider .owl-nav button i {
    color: #232323;
    font-size: 13px;
    font-weight: 700;
    line-height: 1;
}
.pro-page .pro-image .larg-image ul.nav.nav-tabs.pro-page-slider .owl-nav button:hover i {
    color: #ec6504;
}
.pro-page .pro-image .larg-image ul.nav.nav-tabs.pro-page-slider .owl-nav button i,
.pro-page .pro-image .larg-image ul.nav.nav-tabs.pro-page-slider .owl-nav button:hover i {
    -webkit-transition: all 0.3s ease-in-out 0s;
    -o-transition: all 0.3s ease-in-out 0s;
    transition: all 0.3s ease-in-out 0s;
}
.pro-page .pro-image .pro-info h4{
    font-size: 18px;
    font-weight: 600;
}
.pro-page .pro-image .pro-info .rating{
    border-top: 1px solid #ededed;
    margin-top: 15px;
    padding-top: 19px;
}
.pro-page .pro-image .pro-info .rating i{
    color: #999;
}
.pro-page .pro-image .pro-info .rating i.d-star{
    color: #ec6504;
    margin-right: 2px;
}
.pro-page .pro-image .pro-info .pro-availabale{
    margin-top: 12px;
}
.pro-page .pro-image .pro-info .pro-availabale span.available{
    min-width: 105px;
    font-weight: 600;
}
.pro-page .pro-image .pro-info .pro-availabale span.pro-instock{
    color: #ec6504;
    font-weight: 600;
}
.pro-page .pro-image .pro-info .pro-price{
    margin-top: 10px;
    display: flex;
    align-items: center;
}
.pro-page .pro-image .pro-info .pro-price span.new-price{
    font-size: 16px;
    font-weight: 600;
}
.pro-page .pro-image .pro-info .pro-price span.old-price{
    color: #999;
    margin-left: 5px;
}
.pro-page .pro-image .pro-info .pro-price .Pro-lable span.p-discount{
    background-color: #e30514;
    font-size: 13px;
    color: #fff;
    margin-left: 10px;
    padding: 2px 6px 2px 13px;
    transform: rotate(-35deg);
    clip-path: polygon(0 0, 100% 0, 100% 100%, 0 100%, 20% 50%);
}
.pro-page .pro-image .pro-info span.pro-details{
    margin-top: 20px;
    font-size: 13px;
    font-weight: 600;
}
.pro-page .pro-image .pro-info span.pro-details span.pro-number{
    color: #ec6504;
    font-weight: 600;
}
.pro-page .pro-image .pro-info p{
    margin-top: 9px;
    color: #999;
}
.pro-page .pro-image .pro-info .pro-items{
    display: flex;
    align-items: center;
    margin-top: 12px;
}
.pro-page .pro-image .pro-info .pro-items span.pro-size{
    font-size: 14px;
    font-weight: 600;
}
.pro-page .pro-image .pro-info .pro-items ul.pro-wight{
    display: flex;
    flex-wrap: wrap;
}
.pro-page .pro-image .pro-info .pro-items ul.pro-wight li{
    margin-left: 15px;
}
.pro-page .pro-image .pro-info .pro-items ul.pro-wight li a{
    color: #adadad;
    border-bottom: 2px solid #adadad;
    padding: 2px 8px;
}
.pro-page .pro-image .pro-info .pro-items ul.pro-wight li a.active,
.pro-page .pro-image .pro-info .pro-items ul.pro-wight li a:hover{
    color: #000;
    border-color: #000;
    padding: 2px 8px;
}
.pro-page .pro-image .pro-info .product-color {
    display: flex;
    align-items: center;
    margin-top: 27px;
}
.pro-page .pro-image .pro-info .product-color span.color-label {
    color: #333;
    font-size: 14px;
    font-weight: 700;
}
.pro-page .pro-image .pro-info .product-color span.color {
    margin-left: 15px;
    line-height: 1;
    display: flex;
}
.pro-page .pro-image .pro-info .product-color span.color a {
    padding: 2px;
    margin-right: 10px;
    display: flex;
    align-items: center;
    border: 1px solid transparent;
    border-radius: 100%;
}
.pro-page .pro-image .pro-info .product-color span.color a.active,
.pro-page .pro-image .pro-info .product-color span.color a:hover {
    border-color: #333;
}
.pro-page .pro-image .pro-info .product-color span.color a span {
    width: 25px;
    height: 25px;
    border-radius: 100%;
}
.pro-page .pro-image .pro-info .product-color span.color a:last-child {
    margin-right: 0px;
}
.pro-page .pro-image .pro-info .product-color span.color a:nth-child(1) span {
    background-color: #5fa800;
}
.pro-page .pro-image .pro-info .product-color span.color a:nth-child(2) span {
    background-color: #ec6504;
}
.pro-page .pro-image .pro-info .product-color span.color a:nth-child(3) span {
    background-color: #b58555;
}
.pro-page .pro-image .pro-info .pro-qty{
    display: flex;
    align-items: center;
    margin-top: 27px;
}
.pro-page .pro-image .pro-info .pro-qty span.qty{
    font-weight: 700;
}
.pro-page .pro-image .pro-info .pro-qty .plus-minus{
    border: 1px solid #e2e2e2;
    border-radius: 25px;
}
.pro-page .pro-image .pro-info .pro-qty .plus-minus{
    margin-left: 15px;
}
.pro-page .pro-image .pro-info .pro-qty .plus-minus span{
    display: flex;
}
.pro-page .pro-image .pro-info .pro-qty .plus-minus span a{
    width: 40px;
    height: 35px;
    font-size: 22px;
    display: flex;
    align-items: center;
    justify-content: center;
}
.pro-page .pro-image .pro-info .pro-qty .plus-minus input{
    width: 50px;
    height: 35px;
    border-top: none;
    border-bottom: none;
    padding: 0;
    border-top: none;
    border-bottom: none;
    display: flex;
    align-items: center;
    justify-content: center;
    text-align: center;
}
.pro-page .pro-image .pro-info .pro-btn{
    margin-top: 17px;
}
.pro-page .pro-image .pro-info .pro-btn a {
    margin-left: 5px;
    margin-top: 10px;
}
.pro-page .pro-image .pro-info .pro-btn a:first-child {
    margin-left: 0px;
}
.pro-page .pro-image .pro-info .share{
    margin-top: 27px;
    font-size: 14px;
    font-weight: 600;
    display: flex;
    align-items: center;
}
.pro-page .pro-image .pro-info .share ul.share-icn{
    display: flex;
    align-items: center;
    margin-right: 15px;
}
.pro-page .pro-image .pro-info .share ul.share-icn li{
    margin-left: 15px;
}
.pro-page .pro-image .pro-info .share ul.share-icn li a i{
    font-size: 18px;
}
.pro-page .pro-image .pro-info .share ul.share-icn li a i.fa-facebook{
    color: #3b5999;
}
.pro-page .pro-image .pro-info .share ul.share-icn li a i.fa-twitter{
    color: #55acee;
}
.pro-page .pro-image .pro-info .share ul.share-icn li a i.fa-instagram{
    color: #dd4b39;
}
.pro-page .pro-image .pro-info .share ul.share-icn li a i.fa-pinterest{
    color: #bd081c;
}
.pro-page .pro-image .pro-info .pay-img{
    margin-top: 22px;
}
.pro-page .pro-shipping .product-service{
    background-color: #f9f9f9;
    padding: 25px;
    margin-top: 30px;
}
.pro-page .pro-shipping .product-service:first-child{
    margin-top: 0px;
}
.pro-page .pro-shipping .product-service .icon-title {
    display: flex;
    align-items: center;
}
.pro-page .pro-shipping .product-service .icon-title span{
    color: #ec6504;
    font-size: 30px;
    border-radius: 50px;
    display: flex;
    align-items: center;
}
.pro-page .pro-shipping .product-service .icon-title h4{
    font-size: 16px;
    margin-left: 25px;
    font-weight: 600;
    text-transform: uppercase;
}
.pro-page .pro-shipping .product-service p{
    color: #8e8e8e;
    margin-top: 18px;
}
/* tab css */
.pro-page-content .pro-page-tab ul.nav.nav-tabs{
    border-bottom: none;
    justify-content: center;
    margin-bottom: 60px;
}
.pro-page-content .pro-page-tab ul.nav.nav-tabs li.nav-item{
    margin-right: 60px;
}
.pro-page-content .pro-page-tab ul.nav.nav-tabs li.nav-item:last-child{
    margin-right: 0px;
}
.pro-page-content .pro-page-tab ul.nav.nav-tabs li.nav-item a.nav-link{
    color: #949494;
    font-size: 14px;
    font-weight: 600;
    padding: 10px 30px;
    border-radius: 50px;
    margin-bottom: 0px;
    text-transform: uppercase;
}
.pro-page-content .pro-page-tab ul.nav.nav-tabs li.nav-item a.nav-link.active,
.pro-page-content .pro-page-tab ul.nav.nav-tabs li.nav-item a.nav-link:hover{
    color: #000;
    border: 1px solid #000;
}
.pro-page-content .pro-page-tab .tab-content iframe {
    width: 100%;
}
/* description css */
.pro-page-content .pro-page-tab .tab-content .tab-pane .tab-1content h4{
    font-size: 16px;
}
.pro-page-content .pro-page-tab .tab-content .tab-pane .tab-1content ul.tab-description{
    list-style-type: unset;
    padding-left: 15px;
    margin-top: 18px;
}
.pro-page-content .pro-page-tab .tab-content .tab-pane .tab-1content ul.tab-description li {
    margin-top: 5px;
}
.pro-page-content .pro-page-tab .tab-content .tab-pane .tab-2content {
    margin-top: 17px;
}
.pro-page-content .pro-page-tab .tab-content .tab-pane .tab-2content h4{
    font-size: 16px;
}
.pro-page-content .pro-page-tab .tab-content .tab-pane .tab-2content ul.tab-description{
    list-style-type: unset;
    padding-left: 15px;
    margin-top: 18px;
}
.pro-page-content .pro-page-tab .tab-content .tab-pane .tab-2content ul.tab-description li {
    margin-top: 5px;
}
/* reviews css */
.pro-page-content .pro-page-tab .tab-content .tab-pane h4.reviews-title{
    font-size: 16px;
    margin-bottom: 10px;
    font-weight: 600;
}
.pro-page-content .pro-page-tab .tab-content .tab-pane .customer-reviews{
    border-top: 1px solid #eee;
    margin-top: 28px;
    padding-top: 30px;
}
.pro-page-content .pro-page-tab .tab-content .tab-pane .customer-reviews span.p-rating i.e-star{
    color: #ec6504;
    font-size: 18px;
}
.pro-page-content .pro-page-tab .tab-content .tab-pane .customer-reviews p.review-desck{
    font-size: 13px;
    margin-top: 9px;
    margin-bottom: 9px;
    line-height: 1;
}
.pro-page-content .pro-page-tab .tab-content .tab-pane .customer-reviews a{
    color: #ec6504;
    text-decoration: underline;
}
.pro-page-content .pro-page-tab .tab-content .tab-pane .customer-reviews h4.review-head{
    font-size: 16px;
    margin-top: 10px;
    margin-bottom: 10px;
    line-height: 1;
}
.pro-page-content .pro-page-tab .tab-content .tab-pane .customer-reviews span.reviews-editor{
    font-weight: 500;
}
.pro-page-content .pro-page-tab .tab-content .tab-pane .customer-reviews span.reviews-editor span.review-name{
    font-weight: normal;
}
.pro-page-content .pro-page-tab .tab-content .tab-pane .customer-reviews p.r-description{
    margin-top: 10px;
    line-height: 1;
    font-size: 13px;
}
.pro-page-content .pro-page-tab .tab-content .tab-pane .review-form {
    margin-top: 24px;
}
.pro-page-content .pro-page-tab .tab-content .tab-pane .review-form h4 {
    font-size: 18px;
    color: #333;
    line-height: 1;
}
.pro-page-content .pro-page-tab .tab-content .tab-pane .review-form form {
    margin-top: 12px;
}
.pro-page-content .pro-page-tab .tab-content .tab-pane .review-form form label {
    font-size: 14px;
    margin-top: 15px;
}
.pro-page-content .pro-page-tab .tab-content .tab-pane .review-form form label:first-child {
    margin-top: 0px;
}
.pro-page-content .pro-page-tab .tab-content .tab-pane .review-form form input {
    width: 100%;
    margin-top: 10px;
    font-size: 14px;
    border: 1px solid #eee;
}
.pro-page-content .pro-page-tab .tab-content .tab-pane .review-form form textarea {
    width: 100%;
    margin-top: 10px;
    font-size: 14px;
    border: 1px solid #eee;
}
.pro-page-content .pro-page-tab .tab-content .tab-pane .review-form span {
    display: block;
    margin-top: 10px;
    line-height: 1;
}
.pro-page-content .pro-page-tab .tab-content .tab-pane .review-form span i {
    color: #ddd;
}
/* releted product css */
.pro-releted .section-title{
    text-align: center;
}
.pro-releted .section-title h2{
    font-size: 30px;
    margin-bottom: 30px;
}
.tred-pro{
    position: relative;
}
.tred-pro .tr-pro-img a{
    display: block;
}
.tred-pro .tr-pro-img a img{
    backface-visibility: hidden;
}
.tred-pro .Pro-lable span.p-text,
.tred-pro .Pro-lable span.p-discount{
    position: absolute;
    top: 5px;
    font-size: 13px;
    color: #fff;
    padding: 2px 10px 2px 15px;
    clip-path: polygon(0 0, 100% 0, 100% 100%, 0 100%, 20% 50%);
}
.tred-pro .Pro-lable span.p-text{
    left: 5px;
    background-color: #ec6504;
}
.tred-pro .Pro-lable span.p-discount{
    right: 5px;
    background-color: #e30514;
}
.tred-pro .pro-icn{
    position: absolute;
    bottom: 15px;
    left: 0px;
    right: 0px;
    text-align: center;
    margin: 0px;
}
.tred-pro .pro-icn a.w-c-q-icn:first-child{
    transform: translateX(40px);
}
.tred-pro .pro-icn a.w-c-q-icn:last-child{
    transform: translateX(-40px);
}
.tred-pro:hover .pro-icn a.w-c-q-icn:first-child{
    margin-right: 15px;
}
.tred-pro:hover .pro-icn a.w-c-q-icn:last-child{
    margin-left: 15px;
}
.tred-pro:hover .pro-icn a.w-c-q-icn:first-child,
.tred-pro:hover .pro-icn a.w-c-q-icn:last-child{
    transform: translateX(0);
    -webkit-transition: all 0.3s ease-in-out 0s;
    -o-transition: all 0.3s ease-in-out 0s;
    transition: all 0.3s ease-in-out 0s;
}
.tred-pro .pro-icn a.w-c-q-icn i{
    background-color: #fff;
    color: #000;
    width: 40px;
    height: 40px;
    display: flex;
    justify-content: center;
    align-items: center;
    line-height: 0px;
    font-size: 16px;
    border-radius: 100%;
    -webkit-transition: all 0.2s ease-in-out 0s;
    -o-transition: all 0.2s ease-in-out 0s;
    transition: all 0.2s ease-in-out 0s;
    opacity: 0;
    visibility: hidden;
}
.tred-pro .pro-icn a.w-c-q-icn:hover i{
    color: #ec6504;
}
.tred-pro:hover .pro-icn a.w-c-q-icn i{
    opacity: 1;
    visibility: visible;
}
.caption{
    padding-top: 15px;
}
.caption h3{
    font-size: 14px;
    font-weight: 400;
}
.caption h3 a{
    display: block;
    white-space: nowrap;
    width: 100%;
    overflow: hidden;
    text-overflow: ellipsis;
}
.caption .rating{
    display: flex;
    margin-top: 14px;
}
.caption .rating i{
    color: #ccc;
    font-size: 14px;
    margin-right: 5px;
}
.caption .rating i.b-star,
.caption .rating i.c-star,
.caption .rating i.d-star,
.caption .rating i.e-star{
    color: #ec6504;
}
.caption .rating i:last-child{
    margin-right: 0px;
}
.caption .pro-price{
    margin-top: 16px;
}
.caption .pro-price span.new-price{
    font-size: 16px;
    font-weight: 600;
    margin-right: 5px;
    line-height: 1;
}
.caption .pro-price span.old-price{
    color: #999;
    font-size: 14px;
    line-height: 1;
}
/* additional image css */
.tred-pro .tr-pro-img a img.additional-image{
    position: absolute;
    top: 0px;
    right: 0px;
    left: 0px;
    opacity: 0;
    visibility: hidden;
}
.tred-pro:hover .tr-pro-img a img.additional-image{
    opacity: 1;
    visibility: visible;
}
.tred-pro .tr-pro-img a img.additional-image,
.tred-pro:hover .tr-pro-img a img.additional-image{
    -webkit-transition: all 0.3s ease-in-out 0s;
    -o-transition: all 0.3s ease-in-out 0s;
    transition: all 0.3s ease-in-out 0s;
}
/* search page css */
.search-title{
    text-align: center;
    margin-bottom: 30px;
}
.search-title h3{
    font-size: 30px;
    font-weight: 600;
}
.saerch-input form{
    position: relative;
}
.saerch-input form input{
    width: 100%;
    padding: 10px 15px;
    border: 1px solid #eee;
    background-color: #fff;
    border-radius: 25px;
}
.saerch-input form a.search-btn{
    position: absolute;
    bottom: 50%;
    transform: translateY(50%);
    right: 0px;
    font-size: 18px;
    background-color: #305724;
    padding: 6px 11px;
    color: #fff;
    border: 1px solid #305724;
    border-radius: 50%;
}
.saerch-input form a.search-btn:hover{
    background-color: #ec6504;
    border-color: #ec6504;
}
/* search product css */
.search-pro-area{
    display: flex;
    flex-wrap: wrap;
    margin-left: -30px;
}
.search-pro-area .search-pro-items{
    width: calc(25% - 30px);
    margin-top: 30px;
    margin-left: 30px;
}
.search-pro-area .search-pro-items .search-img{
    position: relative;
}
.search-pro-area .search-pro-items .search-img .pro-icn{
    position: absolute;
    bottom: 20px;
    left: 0px;
    right: 0px;
    text-align: center;
}
.search-pro-area .search-pro-items .search-img .pro-icn a.w-c-q-icn:first-child{
    transform: translateX(40px);
}
.search-pro-area .search-pro-items .search-img .pro-icn a.w-c-q-icn:last-child{
    transform: translateX(-40px);
}
.search-pro-area .search-pro-items .search-img:hover .pro-icn a.w-c-q-icn:first-child{
    margin-right: 15px;
}
.search-pro-area .search-pro-items .search-img:hover .pro-icn a.w-c-q-icn:last-child{
    margin-left: 15px;
}
.search-pro-area .search-pro-items .search-img:hover .pro-icn a.w-c-q-icn:first-child,
.search-pro-area .search-pro-items .search-img:hover .pro-icn a.w-c-q-icn:last-child{
    transform: translateX(0);
    -webkit-transition: all 0.3s ease-in-out 0s;
    -o-transition: all 0.3s ease-in-out 0s;
    transition: all 0.3s ease-in-out 0s;
}
.search-pro-area .search-pro-items .search-img .pro-icn a.w-c-q-icn i{
    background-color: #fff;
    color: #000;
    width: 40px;
    height: 40px;
    display: flex;
    justify-content: center;
    align-items: center;
    line-height: 0px;
    font-size: 16px;
    border-radius: 100%;
    -webkit-transition: all 0.2s ease-in-out 0s;
    -o-transition: all 0.2s ease-in-out 0s;
    transition: all 0.2s ease-in-out 0s;
    opacity: 0;
    visibility: hidden;
}
.search-pro-area .search-pro-items .search-img .pro-icn a.w-c-q-icn:hover i{
    color: #ec6504;
}
.search-pro-area .search-pro-items .search-img:hover .pro-icn a.w-c-q-icn i{
    opacity: 1;
    visibility: visible;
}
.search-pro-area .search-pro-items .search-img a img.search-hover{
    position: absolute;
    top: 0px;
    right: 0px;
    left: 0px;
    opacity: 0;
    visibility: hidden;
    -webkit-transition: all 0.3s ease-in-out 0s;
    -o-transition: all 0.3s ease-in-out 0s;
    transition: all 0.3s ease-in-out 0s;
}
.search-pro-area .search-pro-items:hover .search-img a img.search-hover{
    opacity: 1;
    visibility: visible;
    -webkit-transition: all 0.3s ease-in-out 0s;
    -o-transition: all 0.3s ease-in-out 0s;
    transition: all 0.3s ease-in-out 0s;
}
.search-pro-area .search-pro-items .search-caption h4{
    margin-top: 15px;
    font-size: 14px;
    font-weight: 400;
}
.search-pro-area .search-pro-items .search-caption h4 a{
    display: block;
    white-space: nowrap;
    width: 100%;
    overflow: hidden;
    text-overflow: ellipsis;
}
.search-pro-area .search-pro-items .search-caption span.all-price{
    margin-top: 6px;
}
.search-pro-area .search-pro-items .search-caption span.all-price span.search-new-price{
    color: #222;
    font-size: 14px;
    font-weight: 600;
}
.search-pro-area .search-pro-items .search-caption span.all-price span.search-old-price{
    color: #999;
    font-size: 12px;
    font-weight: 500;
}
/* search style-2 css */
.saerch-style-2-input form{
    position: relative;
}
.saerch-style-2-input form input{
    width: 100%;
    padding: 8px 10px;
    border: 1px solid #eee;
    background-color: #fff;
    border-radius: 25px;
}
.saerch-style-2-input form a.search-btn{
    position: absolute;
    bottom: 50%;
    transform: translateY(50%);
    right: 0px;
    font-size: 18px;
    background-color: #ec6504;
    padding: 5px 10px;
    color: #fff;
    border-radius: 50%;
}
.saerch-style-2-input form a.search-btn:hover{
    background-color: #000;
}
.search-selector{
    margin-top: 30px;
    margin-bottom: 30px;
}
.search-selector ul{
    display: flex;
    align-items: center;
    justify-content: space-between;
}
.search-selector ul li.search-items{
    font-size: 15px;
    font-weight: 500;
}
.search-selector ul li.search-pro-select{
    display: flex;
    align-items: center;
}
.search-selector ul li.search-pro-select form select{
    padding: 8px 10px;
    border: 1px solid #eee;
}
.search-selector ul li.search-pro-select a{
    font-size: 20px;
    margin-right: 20px;
    line-height: 0;
}
.search-selector ul li.search-pro-select a:hover,
.search-selector ul li.search-pro-select a.active{
    color: #ec6504;
}
/* search left column css */
.search-category{
    position: sticky;
    top: 0px;
}
.search-category .select-category h4.search-cat-title{
    padding-bottom: 27px;
    font-size: 16px;
    line-height: 1;
    border-bottom: 1px solid #eee;
}
.search-category .select-category ul{
    height: 223px;
    overflow: auto;
    padding-right: 5px;
}
.search-category .select-category ul{
    max-height: calc(100% - 219px);
    overflow-y: auto;
    scrollbar-width: thin;
    -webkit-scrollbar-width: thin;
}
.search-category .select-category ul::-webkit-scrollbar{
    width: 4px;
}
.search-category .select-category ul::-webkit-scrollbar-track{
    background-color: #eee;
}
.search-category .select-category ul::-webkit-scrollbar-thumb{
    background-color: #c1c1c1;
}
.search-category .select-category ul::-webkit-scrollbar-thumb:hover{
    background-color: #ec6504;
}
.search-category .select-category ul {
    padding-top: 25px;
}
.search-category .select-category ul li{
    margin-top: 5px;
}
.search-category .select-category ul li:first-child {
    margin-top: 0px;
}
.search-category .search-pro-color h4.search-cate-color{
    font-size: 16px;
    padding-top: 27px;
    padding-bottom: 28px;
    line-height: 1;
    border-bottom: 1px solid #eee;
}
.search-category .search-pro-color ul.select-cat-color{
    display: flex;
    flex-wrap: wrap;
    align-items: center;
    padding-top: 30px;
    margin-left: -10px;
    margin-top: -16px;
}
.search-category .search-pro-color ul.select-cat-color li{
    line-height: 0;
    margin-top: 16px;
}
.search-category .search-pro-color ul.select-cat-color li a.cat-color-1,
.search-category .search-pro-color ul.select-cat-color li a.cat-color-2,
.search-category .search-pro-color ul.select-cat-color li a.cat-color-3,
.search-category .search-pro-color ul.select-cat-color li a.cat-color-4,
.search-category .search-pro-color ul.select-cat-color li a.cat-color-5,
.search-category .search-pro-color ul.select-cat-color li a.cat-color-6,
.search-category .search-pro-color ul.select-cat-color li a.cat-color-7,
.search-category .search-pro-color ul.select-cat-color li a.cat-color-8,
.search-category .search-pro-color ul.select-cat-color li a.cat-color-9,
.search-category .search-pro-color ul.select-cat-color li a.cat-color-10,
.search-category .search-pro-color ul.select-cat-color li a.cat-color-11,
.search-category .search-pro-color ul.select-cat-color li a.cat-color-12,
.search-category .search-pro-color ul.select-cat-color li a.cat-color-13,
.search-category .search-pro-color ul.select-cat-color li a.cat-color-14,
.search-category .search-pro-color ul.select-cat-color li a.cat-color-15,
.search-category .search-pro-color ul.select-cat-color li a.cat-color-16,
.search-category .search-pro-color ul.select-cat-color li a.cat-color-17,
.search-category .search-pro-color ul.select-cat-color li a.cat-color-18{
    width: 20px;
    height: 20px;
    margin-left: 10px;
    border-radius: 100%;
}
.search-category .search-pro-color ul.select-cat-color li a.cat-color-1{
    background-color: #86aab0;
}
.search-category .search-pro-color ul.select-cat-color li a.cat-color-2{
    background-color: #e32323;
}
.search-category .search-pro-color ul.select-cat-color li a.cat-color-3{
    background-color: #4f4f51;
}
.search-category .search-pro-color ul.select-cat-color li a.cat-color-4{
    background-color: #efe4d7;
}
.search-category .search-pro-color ul.select-cat-color li a.cat-color-5{
    background-color: #795548;
}
.search-category .search-pro-color ul.select-cat-color li a.cat-color-6{
    background-color: #cdd1c6;
}
.search-category .search-pro-color ul.select-cat-color li a.cat-color-7{
    background-color: #666;
}
.search-category .search-pro-color ul.select-cat-color li a.cat-color-8{
    background-color: #b3b8a8;
}
.search-category .search-pro-color ul.select-cat-color li a.cat-color-9{
    background-color: #e0b756;
}
.search-category .search-pro-color ul.select-cat-color li a.cat-color-10{
    background-color: #6f6963;
}
.search-category .search-pro-color ul.select-cat-color li a.cat-color-11{
    background-color: #9e9e9e;
}
.search-category .search-pro-color ul.select-cat-color li a.cat-color-12{
    background-color: #719297;
}
.search-category .search-pro-color ul.select-cat-color li a.cat-color-13{
    background-color: #b58555;
}
.search-category .search-pro-color ul.select-cat-color li a.cat-color-14{
    background-color: #6ca6b7;
}
.search-category .search-pro-color ul.select-cat-color li a.cat-color-15{
    background-color: #a2c4d4;
}
.search-category .search-pro-color ul.select-cat-color li a.cat-color-16{
    background-color: #bb9290;
}
.search-category .search-pro-color ul.select-cat-color li a.cat-color-17{
    background-color: #877666;
}
.search-category .search-pro-color ul.select-cat-color li a.cat-color-18{
    background-color: #e2e2e2;
}
.search-category .search-pro h4{
    padding-bottom: 28px;
    font-size: 16px;
    padding-top: 28px;
    margin-bottom: 30px;
    border-bottom: 1px solid #eee;
    line-height: 1;
}
.search-category .search-pro .search-pro-main{
    display: flex;
    align-items: center;
    margin-bottom: 20px;
    padding-bottom: 20px;
    border-bottom: 1px solid #eee;
}
.search-category .search-pro .search-pro-main:last-child{
    margin-bottom: 0px;
    padding-bottom: 0px;
    border-bottom: none;
}
.search-category .search-pro .search-pro-main .search-main-caption{
    margin-left: 10px;
    overflow: hidden;
}
.search-category .search-pro .search-pro-main .search-main-caption a{
    font-size: 15px;
    font-weight: 600;
    white-space: nowrap;
    width: 100%;
    overflow: hidden;
    text-overflow: ellipsis;
    display: block;
}
.search-category .search-pro .search-pro-main .search-main-caption span.search-all-price{
    margin-top: 5px;
}
.search-category .search-pro .search-pro-main .search-main-caption span.search-all-price span.search-n-p{
    color: #ec6504;
    font-weight: 600;
}
.search-category .search-pro .search-pro-main .search-main-caption span.search-all-price span.search-o-p{
    font-size: 12px;
    color: #666;
    font-weight: 500;
}
/* search product css */
.style-2-search-pro-area{
    display: flex;
    flex-wrap: wrap;
    margin-left: -30px;
}
.style-2-search-pro-area .search-pro-items{
    width: calc(33.33% - 30px);
    margin-top: 30px;
    margin-left: 30px;
}
.style-2-search-pro-area .search-pro-items:nth-child(1),
.style-2-search-pro-area .search-pro-items:nth-child(2),
.style-2-search-pro-area .search-pro-items:nth-child(3){
    margin-top: 0px;
}
.style-2-search-pro-area .search-pro-items .search-img{
    position: relative;
}
.style-2-search-pro-area .search-pro-items .search-img a{
    position: relative;
    overflow: hidden;
}
.style-2-search-pro-area .search-pro-items .search-img a img{
    -webkit-transition: all 0.5s ease-in-out 0s;
    -o-transition: all 0.5s ease-in-out 0s;
    transition: all 0.5s ease-in-out 0s;
}
.style-2-search-pro-area .search-pro-items .search-img:hover a img{
    transform: scale(1.1);
    -webkit-transition: all 0.5s ease-in-out 0s;
    -o-transition: all 0.5s ease-in-out 0s;
    transition: all 0.5s ease-in-out 0s;
}
.style-2-search-pro-area .search-pro-items .search-img .pro-icn{
    position: absolute;
    bottom: 20px;
    left: 0px;
    right: 0px;
    text-align: center;
}
.style-2-search-pro-area .search-pro-items .search-img .pro-icn a.w-c-q-icn:first-child{
    transform: translateX(40px);
}
.style-2-search-pro-area .search-pro-items .search-img .pro-icn a.w-c-q-icn:last-child{
    transform: translateX(-40px);
}
.style-2-search-pro-area .search-pro-items .search-img:hover .pro-icn a.w-c-q-icn:first-child{
    margin-right: 15px;
}
.style-2-search-pro-area .search-pro-items .search-img:hover .pro-icn a.w-c-q-icn:last-child{
    margin-left: 15px;
}
.style-2-search-pro-area .search-pro-items .search-img:hover .pro-icn a.w-c-q-icn:first-child,
.style-2-search-pro-area .search-pro-items .search-img:hover .pro-icn a.w-c-q-icn:last-child{
    transform: translateX(0);
    -webkit-transition: all 0.3s ease-in-out 0s;
    -o-transition: all 0.3s ease-in-out 0s;
    transition: all 0.3s ease-in-out 0s;
}
.style-2-search-pro-area .search-pro-items .search-img .pro-icn a.w-c-q-icn i{
    background-color: #fff;
    color: #000;
    width: 40px;
    height: 40px;
    display: flex;
    justify-content: center;
    align-items: center;
    line-height: 0px;
    font-size: 16px;
    border-radius: 100%;
    -webkit-transition: all 0.2s ease-in-out 0s;
    -o-transition: all 0.2s ease-in-out 0s;
    transition: all 0.2s ease-in-out 0s;
    opacity: 0;
    visibility: hidden;
}
.style-2-search-pro-area .search-pro-items .search-img .pro-icn a.w-c-q-icn:hover i{
    color: #ec6504;
}
.style-2-search-pro-area .search-pro-items .search-img:hover .pro-icn a.w-c-q-icn i{
    opacity: 1;
    visibility: visible;
}
.style-2-search-pro-area .search-pro-items .search-caption h4{
    margin-top: 15px;
    font-size: 14px;
    font-weight: 400;
}
.style-2-search-pro-area .search-pro-items .search-caption h4 a{
    display: block;
    white-space: nowrap;
    width: 100%;
    overflow: hidden;
    text-overflow: ellipsis;
}
.style-2-search-pro-area .search-pro-items .search-caption span.all-price{
    margin-top: 6px;
}
.style-2-search-pro-area .search-pro-items .search-caption span.all-price span.search-new-price{
    color: #222;
    font-size: 14px;
    font-weight: 600;
}
.style-2-search-pro-area .search-pro-items .search-caption span.all-price span.search-old-price{
    color: #999;
    font-size: 12px;
    font-weight: 500;
}
/* style-2 list search product css */
.style-2-list-search-pro-area{
    display: flex;
    flex-wrap: wrap;
}
.style-2-list-search-pro-area .search-pro-items{
    width: 100%;
    margin-bottom: 20px;
    padding-bottom: 20px;
    border-bottom: 1px solid #eee;
    display: flex;
}
.style-2-list-search-pro-area .search-pro-items:last-child{
    margin-bottom: 0px;
    padding-bottom: 0px;
    border-bottom: none;
}
.style-2-list-search-pro-area .search-pro-items:nth-child(1){
    margin-top: 0px;
}
.style-2-list-search-pro-area .search-pro-items .search-img{
    width: 25%;
}
.style-2-list-search-pro-area .search-pro-items .search-img a{
    position: relative;
    overflow: hidden;
    display: block;
}
.style-2-list-search-pro-area .search-pro-items .search-img a img{
    -webkit-transition: all 0.5s ease-in-out 0s;
    -o-transition: all 0.5s ease-in-out 0s;
    transition: all 0.5s ease-in-out 0s;
}
.style-2-list-search-pro-area .search-pro-items:hover .search-img a img{
    transform: scale(1.1);
    -webkit-transition: all 0.5s ease-in-out 0s;
    -o-transition: all 0.5s ease-in-out 0s;
    transition: all 0.5s ease-in-out 0s;
}
.style-2-list-search-pro-area .search-pro-items .search-caption .pro-icn{
    margin-top: 11px;
}
.style-2-list-search-pro-area .search-pro-items .search-caption .pro-icn a.w-c-q-icn i{
    background-color: #ec6504;
    color: #fff;
    width: 40px;
    height: 40px;
    display: flex;
    justify-content: center;
    align-items: center;
    line-height: 0px;
    font-size: 16px;
    border-radius: 100%;
    border: 2px solid #ec6504;
    -webkit-transition: all 0.3s ease-in-out 0s;
    -o-transition: all 0.3s ease-in-out 0s;
    transition: all 0.3s ease-in-out 0s;
}
.style-2-list-search-pro-area .search-pro-items .search-caption .pro-icn a.w-c-q-icn:hover i{
    background-color: transparent;
    color: #ec6504;
    -webkit-transition: all 0.3s ease-in-out 0s;
    -o-transition: all 0.3s ease-in-out 0s;
    transition: all 0.3s ease-in-out 0s;
}
.style-2-list-search-pro-area .search-pro-items .search-caption{
    width: 75%;
    margin-left: 20px;
}
.style-2-list-search-pro-area .search-pro-items .search-caption h4{
    font-size: 14px;
    font-weight: 400;
}
.style-2-list-search-pro-area .search-pro-items .search-caption h4 a{
    display: block;
    white-space: nowrap;
    width: 100%;
    overflow: hidden;
    text-overflow: ellipsis;
}
.style-2-list-search-pro-area .search-pro-items .search-caption span.all-price{
    margin-top: 6px;
}
.style-2-list-search-pro-area .search-pro-items .search-caption span.all-price span.search-new-price{
    color: #222;
    font-size: 14px;
    font-weight: 600;
}
.style-2-list-search-pro-area .search-pro-items .search-caption span.all-price span.search-old-price{
    color: #999;
    font-size: 12px;
    font-weight: 500;
}
.style-2-list-search-pro-area .search-pro-items .search-caption p.search-pro-desc{
    margin-top: 5px;
}
/* pagination css */
.all-page span.page-title{
    color: #333;
    display: block;
    text-align: center;
    margin-top: 30px;
    font-weight: 500;
}
.all-page .page-number{
    text-align: center;
    margin-top: 19px;
}
.all-page .page-number a{
    position: relative;
    margin-right: 5px;
}
.all-page .page-number a:after{
    background-color: #ec6504;
    content: "";
    position: absolute;
    bottom: 0px;
    left: 1px;
    right: 0px;
    width: 4px;
    height: 4px;
    border-radius: 100%;
    opacity: 0;
    visibility: hidden;
}
.all-page .page-number a:hover:after,
.all-page .page-number a.active:after{
    opacity: 1;
    visibility: visible;
}
.all-page .page-number a:hover,
.all-page .page-number a.active{
    color: #ec6504;
}
.all-page .page-number a:last-child:after{
    display: none;
}
/* blog left-right column css */
.left-column{
    position: sticky;
    top: 0px;
}
.left-column .blog-search h4{
    font-size: 16px;
}
.left-column .blog-search form{
    position: relative;
}
.left-column .blog-search form input{
    width: 100%;
    color: #aeaeae;
    font-size: 13px;
    padding: 8px 15px;
    margin-top: 23px;
    border: 1px solid #e2e2e2;
    border-radius: 4px;
}
.left-column.style-1 .blog-search form input{
    margin-top: 23px;
    border-radius: 25px;
}
.left-column .blog-search form a{
    position: absolute;
    bottom: 35%;
    right: 15px;
    transform: translateY(50%);
    font-size: 16px;
    color: #999;
    line-height: 0;
}
.left-column .blog-head h4{
    font-size: 18px;
    margin-top: 15px;
}
.left-column .blog-title h4{
    font-size: 16px;
    margin-top: 30px;
    margin-bottom: 23px;
    padding-top: 23px;
    border-top: 1px solid #eee;
}
.left-column .left-blog .blog-item{
    display: flex;
    margin-bottom: 20px;
    align-items: center;
}
.left-column .left-blog .blog-item:last-child{
    margin-bottom: 0px;
}
.left-column .left-blog .blog-item .l-blog-image{
    margin-right: 15px;
}
.left-column .left-blog .blog-item .l-blog-caption h4{
    font-size: 14px;
    margin-top: 5px;
    width: 100%;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}
.left-column.style-1 .left-blog .blog-item .l-blog-caption h4 a:hover,
.left-column.style-5 .left-blog .blog-item .l-blog-caption h4 a:hover{
    color: #ec6504;
}
.left-column.style-2 .left-blog .blog-item .l-blog-caption h4 a:hover,
.left-column.style-3 .left-blog .blog-item .l-blog-caption h4 a:hover{
    color: #5fa800;
}
.left-column.style-6 .left-blog .blog-item .l-blog-caption h4 a:hover{
    color: #73841b;
}
.left-column .blog-tag h4{
    font-size: 16px;
    margin-top: 30px;
    margin-bottom: 18px;
    padding-top: 23px;
    border-top: 1px solid #eee;
}
.left-column .blog-tag ul.tegs{
    display: flex;
    flex-wrap: wrap;
    margin-top: -10px;
    margin-left: -10px;
}
.left-column .blog-tag ul.tegs li {
    margin-top: 10px;
}
.left-column .blog-tag ul.tegs li a{
    color: #999;
    font-size: 12px;
    padding: 9px 16px;
    margin-left: 10px;
    border: 1px solid #e2e2e2;
    border-radius: 4px;
}
.left-column .blog-tag ul.tegs li a:hover{
    background-color: #000;
    color: #fff;
}
.left-column .blog-tag.style-1 ul.tegs li a{
    border-radius: 25px;
}
/* right column css */
.right-area{
    margin-top: 28px;
    margin-bottom: -12px;
}
.right-c .right-area {
    margin-top: 0px;
}
.right-area .right-column-start h4{
    font-size: 16px;
    margin-top: 0px;
    margin-bottom: 32px;
    line-height: 1;
}
.right-area .right-column-start .archive-link h5{
    display: inline;
    color: #fff;
    font-size: 14px;
    padding: 5px 15px;
    background-color: #ec6504;
    font-weight: 400;
    line-height: 1;
    border-radius: 5px;
}
.right-area.style-3 .right-column-start .archive-link h5 {
    background-color: #5fa800;
}
.right-area.style-6 .right-column-start .archive-link h5 {
    background-color: #73841b;
}
.right-area.style-7 .right-column-start .archive-link h5 {
    background-color: #cd7752;
}
.right-area .right-column-start .archive-link ul {
    margin-top: 29px;
}
.right-area .right-column-start .archive-link ul li{
    margin-top: 3px;
    width: 100%;
}
.right-area .right-column-start .archive-link ul li a {
    width: 100%;
    text-overflow: ellipsis;
    white-space: nowrap;
    overflow: hidden;
}
.right-area .right-column-start .archive-link ul li:first-child{
    margin-top: 0px;
}
.right-area .right-column-start .archive-link ul li a i{
    font-size: 12px;
}
.right-area .b-Reply{
    margin-top: 22px;
}
.right-area.style-1 .b-Reply {
    margin-top: 16px;
}
.right-area.style-2 .b-Reply {
    margin-top: 16px;
}
.right-area.style-5 .b-Reply {
    margin-top: 16px;
}
.right-area .b-Reply h4{
    font-size: 16px;
    
    line-height: 1;
}
.right-area .b-Reply form {
    margin-top: 17px;
}
.right-area.style-1 .b-Reply form {
    margin-top: 27px;
}
.right-area.style-2 .b-Reply form {
    margin-top: 27px;
}
.right-area.style-5 .b-Reply form {
    margin-top: 27px;
}
.right-area .b-Reply form input{
    width: 100%;
    padding: 8px 15px;
    margin-top: 16px;
}
.right-area .b-Reply form input:first-child{
    margin-top: 0px;
}
.right-area .b-Reply form a.Reply-link{
    background-color: #ec6504;
    color: #fff;
    width: 100%;
    padding: 8px 15px;
    margin-top: 30px;
    text-align: center;
    font-weight: 500;
    border: 2px solid #ec6504;
}
.right-area .b-Reply form a.Reply-link:hover{
    background-color: transparent;
    color: #000;
    border-color: #ec6504;
}
.right-area .r-image{
    padding: 10px;
    margin-top: 20px;
    border: 1px solid #eee;
}
/* full grid blog css */
.blog-style-1-full-grid{
    counter-reset: my-sec-counter;
    display: flex;
    flex-wrap: wrap;
    margin-left: -30px;
    margin-top: -30px;
}
.blog-style-1-full-grid .blog-start{
    position: relative;
}
.blog-style-1-full-grid .blog-start .blog-post{
    margin-top: 15px;
    border: 1px solid #eee;
}
.blog-style-1-full-grid .blog-start:before,
.blog-style-1-full-grid .blog-start:after{
    background-color: #ec6504;
}
.blog-style-1-full-grid .blog-start:before{
    counter-increment: my-sec-counter;
    content: counter(my-sec-counter);
    position: absolute;
    top: 0px;
    left: 15px;
    width: 50px;
    height: 50px;
    font-size: 24px;
    font-weight: 600;
    color: #fff;
    border-radius: 0px 5% 25% 25%;
    align-items: center;
    justify-content: center;
    display: flex;
}
.blog-style-1-full-grid .blog-start:after{
    content: "";
    width: 20px;
    height: 15px;
    position: absolute;
    top: 0px;
    left: 0px;
    opacity: 0.8;
    border-radius: 20px 5px 0px 0px;
}
.blog-style-1-full-grid .blog-start{
    width: calc(33.33% - 30px);
    margin-top: 30px;
    margin-left: 30px;
    border-radius: 5px;;
}
.blog-style-1-full-grid .blog-start .blog-image{
    display: flex;
}
.blog-style-1-full-grid .blog-start .blog-image a img,
.blog-style-1-left-grid .blog-start .blog-image a img,
.blog-style-1-right-grid .blog-start .blog-image a img {
    backface-visibility: hidden;
}
.blog-style-1-full-grid .blog-start .blog-content{
    padding: 30px;
}
.blog-style-1-full-grid .blog-start .blog-content .blog-title h6{
    font-size: 16px;
}
.blog-style-1-full-grid .blog-start .blog-content .blog-title h6 a{
    display: block;
    width: 100%;
    white-space: nowrap;
    text-overflow: ellipsis;
    overflow: hidden;
}
.blog-style-1-full-grid .blog-start .blog-content span.blog-admin{
    display: block;
    margin-top: 7px;
    line-height: 1;
}
.blog-style-1-full-grid .blog-start .blog-content span.blog-admin span.blog-editor{
    color: #000;
    font-weight: 600;
}
.blog-style-1-full-grid .blog-start .blog-content p.blog-description{
    margin-top: 21px;
}
.blog-style-1-full-grid .blog-start .blog-content a.read-link{
    display: flex;
    align-items: center;
    margin-top: 20px;
    font-size: 14px;
    font-weight: 600;
    line-height: 1;
    -webkit-transition: all 0s ease-in-out 0s;
    -o-transition: all 0s ease-in-out 0s;
    transition: all 0s ease-in-out 0s;
}
.blog-style-1-full-grid .blog-start .blog-content a.read-link span{
    color: #222;
}
.blog-style-1-full-grid .blog-start .blog-content a.read-link:hover span{
    color: #ec6504;
}
.blog-style-1-full-grid .blog-start .blog-content a.read-link i{
    padding-left: 5px;
    font-size: 12px;
}
.blog-style-1-full-grid .blog-start .blog-content a.read-link:hover i{
    padding-left: 10px;
}
.blog-style-1-full-grid .blog-start .blog-content a.read-link span,
.blog-style-1-full-grid .blog-start .blog-content a.read-link:hover span,
.blog-style-1-full-grid .blog-start .blog-content a.read-link i,
.blog-style-1-full-grid .blog-start .blog-content a.read-link:hover i{
    -webkit-transition: all 0.3s ease-in-out 0s;
    -o-transition: all 0.3s ease-in-out 0s;
    transition: all 0.3s ease-in-out 0s;
}
.blog-style-1-full-grid .blog-start .blog-content .blog-date-comment{
    margin-top: 12px;
    display: flex;
    justify-content: space-between;
    line-height: 1
}
.blog-style-1-full-grid .blog-start .blog-content .blog-date-comment a{
    font-weight: 600;
}
/* left-right grid blog css */
.blog-style-1-left-grid,
.blog-style-1-right-grid{
    counter-reset: my-sec-counter;
    display: flex;
    flex-wrap: wrap;
    margin-top: -30px;
    margin-left: -20px;
}
.blog-style-1-left-grid .blog-start,
.blog-style-1-right-grid .blog-start{
    position: relative;
}
.blog-style-1-left-grid .blog-start .blog-post,
.blog-style-1-right-grid .blog-start .blog-post{
    margin-top: 15px;
    border: 1px solid #eee;
}
.blog-style-1-left-grid .blog-start:before,
.blog-style-1-left-grid .blog-start:after,
.blog-style-1-right-grid .blog-start:before,
.blog-style-1-right-grid .blog-start:after{
    background-color: #ec6504;
}
.blog-style-1-left-grid .blog-start:before,
.blog-style-1-right-grid .blog-start:before{
    counter-increment: my-sec-counter;
    content: counter(my-sec-counter);
    position: absolute;
    top: 0px;
    left: 15px;
    width: 50px;
    height: 50px;
    font-size: 24px;
    font-weight: 600;
    color: #fff;
    border-radius: 0px 5% 25% 25%;
    align-items: center;
    justify-content: center;
    display: flex;
}
.blog-style-1-left-grid .blog-start:after,
.blog-style-1-right-grid .blog-start:after{
    content: "";
    width: 20px;
    height: 15px;
    position: absolute;
    top: 0px;
    left: 0px;
    opacity: 0.8;
    border-radius: 20px 5px 0px 0px;
}
.blog-style-1-left-grid .blog-start,
.blog-style-1-right-grid .blog-start{
    width: calc(33.33% - 20px);
    margin-top: 30px;
    margin-left: 20px;
    border-radius: 5px;;
}
.blog-style-1-left-grid .blog-start .blog-image,
.blog-style-1-right-grid .blog-start .blog-image{
    display: flex;
}
.blog-style-1-left-grid .blog-start .blog-content,
.blog-style-1-right-grid .blog-start .blog-content{
    padding: 30px;
}
.blog-style-1-left-grid .blog-start .blog-content .blog-date-comment,
.blog-style-1-right-grid .blog-start .blog-content .blog-date-comment{
    margin-top: 12px;
    display: flex;
    justify-content: space-between;
    line-height: 1
}
.blog-style-1-left-grid .blog-start .blog-content .blog-date-comment a,
.blog-style-1-right-grid .blog-start .blog-content .blog-date-comment a{
    font-weight: 600;
}
.blog-style-1-left-grid .blog-start .blog-content .blog-title h6,
.blog-style-1-right-grid .blog-start .blog-content .blog-title h6 {
    font-size: 16px;
} 
.blog-style-1-left-grid .blog-start .blog-content .blog-title h6 a,
.blog-style-1-right-grid .blog-start .blog-content .blog-title h6 a{
    display: block;
    width: 100%;
    white-space: nowrap;
    text-overflow: ellipsis;
    overflow: hidden;
}
.blog-style-1-left-grid .blog-start .blog-content p.blog-description,
.blog-style-1-right-grid .blog-start .blog-content p.blog-description{
    margin-top: 21px;
    font-size: 14px;
}
.blog-style-1-left-grid .blog-start .blog-content a.read-link,
.blog-style-1-right-grid .blog-start .blog-content a.read-link{
    display: flex;
    align-items: center;
    margin-top: 20px;
    font-size: 14px;
    font-weight: 600;
    line-height: 1;
    -webkit-transition: all 0s ease-in-out 0s;
    -o-transition: all 0s ease-in-out 0s;
    transition: all 0s ease-in-out 0s;
}
.blog-style-1-left-grid .blog-start .blog-content a.read-link span,
.blog-style-1-right-grid .blog-start .blog-content a.read-link span{
    color: #222;
}
.blog-style-1-left-grid .blog-start .blog-content a.read-link:hover span,
.blog-style-1-right-grid .blog-start .blog-content a.read-link:hover span{
    color: #ec6504;
}
.blog-style-1-left-grid .blog-start .blog-content a.read-link i,
.blog-style-1-right-grid .blog-start .blog-content a.read-link i{
    padding-left: 5px;
    font-size: 12px;
}
.blog-style-1-left-grid .blog-start .blog-content a.read-link:hover i,
.blog-style-1-right-grid .blog-start .blog-content a.read-link:hover i{
    padding-left: 10px;
}
.blog-style-1-left-grid .blog-start .blog-content a.read-link span,
.blog-style-1-left-grid .blog-start .blog-content a.read-link:hover span,
.blog-style-1-left-grid .blog-start .blog-content a.read-link i,
.blog-style-1-left-grid .blog-start .blog-content a.read-link:hover i,
.blog-style-1-right-grid .blog-start .blog-content a.read-link span,
.blog-style-1-right-grid .blog-start .blog-content a.read-link:hover span,
.blog-style-1-right-grid .blog-start .blog-content a.read-link i,
.blog-style-1-right-grid .blog-start .blog-content a.read-link:hover i{
    -webkit-transition: all 0.3s ease-in-out 0s;
    -o-transition: all 0.3s ease-in-out 0s;
    transition: all 0.3s ease-in-out 0s;
}
.blog-style-1-left-grid .blog-start .blog-content span.blog-admin,
.blog-style-1-right-grid .blog-start .blog-content span.blog-admin{
    display: block;
    margin-top: 7px;
    line-height: 1;
}
.blog-style-1-left-grid .blog-start .blog-content span.blog-admin span.blog-editor,
.blog-style-1-right-grid .blog-start .blog-content span.blog-admin span.blog-editor{
    color: #000;
    font-weight: 600;
}
/* full blog list css */
.blog-style-1-list{
    counter-reset: my-sec-counter;
    display: flex;
    flex-wrap: wrap;
    margin-top: -30px;
    margin-left: -30px;
}
.blog-style-1-list .blog-start{
    position: relative;
}
.blog-style-1-list .blog-start .blog-post{
    margin-top: 15px;
    display: flex;
    align-items: center;
}
.blog-style-1-list .blog-start:before,
.blog-style-1-list .blog-start:after{
    background-color: #ec6504;
}
.blog-style-1-list .blog-start:before{
    counter-increment: my-sec-counter;
    content: counter(my-sec-counter);
    position: absolute;
    top: 0px;
    left: 15px;
    width: 50px;
    height: 50px;
    font-size: 24px;
    font-weight: 600;
    color: #fff;
    border-radius: 0px 5% 25% 25%;
    align-items: center;
    justify-content: center;
    display: flex;
}
.blog-style-1-list .blog-start:after{
    content: "";
    width: 20px;
    height: 15px;
    position: absolute;
    top: 0px;
    left: 0px;
    opacity: 0.8;
    border-radius: 20px 5px 0px 0px;
}
.blog-style-1-list .blog-start{
    width: calc(100% - 30px);
    margin-top: 30px;
    margin-left: 30px;
    border-radius: 5px;
}
.blog-style-1-list .blog-start .blog-image{
    width: 50%;
}
.blog-style-1-list .blog-start .blog-content{
    width: 50%;
    margin-left: 30px;
}
.blog-style-1-list .blog-start .blog-content .blog-title h6{
    font-size: 16px;
}
.blog-style-1-list .blog-start .blog-content .blog-title h6 a{
    display: block;
    width: 100%;
    white-space: nowrap;
    text-overflow: ellipsis;
    overflow: hidden;
}
.blog-style-1-list .blog-start .blog-content .date-comm-adit{
    display: flex;
    align-items: center;
    justify-content: space-between;
    border-top: 1px solid #dfdfdf;
    margin-top: 23px;
    padding-top: 25px;
}
.blog-style-1-list .blog-start .blog-content .date-comm-adit span.blog-admin i{
    margin-right: 5px;
}
.blog-style-1-list .blog-start .blog-content .date-comm-adit span.blog-admin span.blog-editor{
    font-weight: 600;
    margin-left: 5px;
}
.blog-style-1-list .blog-start .blog-content .date-comm-adit span.blog-date i{
    margin-right: 5px;
}
.blog-style-1-right-blog .blog-start .blog-content .date-comm-adit a,
.blog-style-1-left-list-blog .blog-start .blog-content .date-comm-adit a,
.blog-style-1-list .blog-start .blog-content .date-comm-adit a{
    font-weight: 600;
    display: flex;
    align-items: center;
}
.blog-style-1-list .blog-start .blog-content .date-comm-adit a i{
    margin-right: 5px;
}
.blog-style-1-list .blog-start .blog-content p.blog-description{
    margin-top: 20px;
    color: #999;
}
.blog-style-1-list .blog-start .blog-content a.read-link{
    display: flex;
    align-items: center;
    margin-top: 20px;
    font-size: 14px;
    font-weight: 600;
    line-height: 1;
    -webkit-transition: all 0s ease-in-out 0s;
    -o-transition: all 0s ease-in-out 0s;
    transition: all 0s ease-in-out 0s;
}
.blog-style-1-list .blog-start .blog-content a.read-link span{
    color: #222;
}
.blog-style-1-list .blog-start .blog-content a.read-link:hover span{
    color: #ec6504;
}
.blog-style-1-list .blog-start .blog-content a.read-link i{
    padding-left: 5px;
    font-size: 12px;
}
.blog-style-1-list .blog-start .blog-content a.read-link:hover i{
    padding-left: 10px;
}
.blog-style-1-list .blog-start .blog-content a.read-link span,
.blog-style-1-list .blog-start .blog-content a.read-link:hover span,
.blog-style-1-list .blog-start .blog-content a.read-link i,
.blog-style-1-list .blog-start .blog-content a.read-link:hover i{
    -webkit-transition: all 0.3s ease-in-out 0s;
    -o-transition: all 0.3s ease-in-out 0s;
    transition: all 0.3s ease-in-out 0s;
}
/* left-right blog list css */
.blog-style-1-left-list-blog,
.blog-style-1-right-blog{
    counter-reset: my-sec-counter;
    display: flex;
    flex-wrap: wrap;
    margin-top: -30px;
    margin-left: -30px;
}
.blog-style-1-left-list-blog .blog-start,
.blog-style-1-right-blog .blog-start{
    position: relative;
}
.blog-style-1-left-list-blog .blog-start .blog-post,
.blog-style-1-right-blog .blog-start .blog-post{
    margin-top: 15px;
    display: flex;
    align-items: center;
}
.blog-style-1-left-list-blog .blog-start:before,
.blog-style-1-left-list-blog .blog-start:after,
.blog-style-1-right-blog .blog-start:before,
.blog-style-1-right-blog .blog-start:after{
    background-color: #ec6504;
}
.blog-style-1-left-list-blog .blog-start:before,
.blog-style-1-right-blog .blog-start:before{
    counter-increment: my-sec-counter;
    content: counter(my-sec-counter);
    position: absolute;
    top: 0px;
    left: 15px;
    width: 50px;
    height: 50px;
    font-size: 24px;
    font-weight: 600;
    color: #fff;
    border-radius: 0px 5% 25% 25%;
    align-items: center;
    justify-content: center;
    display: flex;
}
.blog-style-1-left-list-blog .blog-start:after,
.blog-style-1-right-blog .blog-start:after{
    content: "";
    width: 20px;
    height: 15px;
    position: absolute;
    top: 0px;
    left: 0px;
    opacity: 0.8;
    border-radius: 20px 5px 0px 0px;
}
.blog-style-1-left-list-blog .blog-start,
.blog-style-1-right-blog .blog-start{
    width: calc(100% - 30px);
    margin-top: 30px;
    margin-left: 30px;
    border-radius: 5px;;
}
.blog-style-1-left-list-blog .blog-start .blog-image,
.blog-style-1-right-blog .blog-start .blog-image{
    display: flex;
    width: 50%;
}
.blog-style-1-list .blog-start .blog-image a img,
.blog-style-1-left-list-blog .blog-start .blog-image a img,
.blog-style-1-right-blog .blog-start .blog-image a img {
    backface-visibility: hidden;
}
.blog-style-1-left-list-blog .blog-start .blog-content,
.blog-style-1-right-blog .blog-start .blog-content{
    width: 50%;
    margin-left: 30px;
}
.blog-style-1-right-blog .blog-start .blog-content .blog-date,
.blog-style-1-left-list-blog .blog-start .blog-content .blog-date,
.blog-style-1-list .blog-start .blog-content .blog-date{
    display: flex;
    align-items: center;
    line-height: 1;
    font-weight: 600;
}
.blog-style-1-left-list-blog .blog-start .blog-content .blog-date-comment a,
.blog-style-1-right-blog .blog-start .blog-content .blog-date-comment a{
    font-weight: 600;
}
.blog-style-1-left-list-blog .blog-start .blog-content .blog-title h6,
.blog-style-1-right-blog .blog-start .blog-content .blog-title h6{
    font-size: 16px;
}
.blog-style-1-left-list-blog .blog-start .blog-content .blog-title h6 a,
.blog-style-1-right-blog .blog-start .blog-content .blog-title h6 a{
    display: block;
    width: 100%;
    white-space: nowrap;
    text-overflow: ellipsis;
    overflow: hidden;
}
.blog-style-1-left-list-blog .blog-start .blog-content .date-comm-adit,
.blog-style-1-right-blog .blog-start .blog-content .date-comm-adit{
    display: flex;
    align-items: center;
    justify-content: space-between;
    border-top: 1px solid #dfdfdf;
    margin-top: 23px;
    padding-top: 25px;
}
.blog-style-1-left-list-blog .blog-start .blog-content .date-comm-adit span.blog-date i,
.blog-style-1-right-blog .blog-start .blog-content .date-comm-adit span.blog-date i{
    margin-right: 5px;
}
.blog-style-1-left-list-blog .blog-start .blog-content .date-comm-adit a,
.blog-style-1-right-blog .blog-start .blog-content .date-comm-adit a{
    font-size: 14px;
    font-weight: 600;
}
.blog-style-1-left-list-blog .blog-start .blog-content .date-comm-adit a i,
.blog-style-1-right-blog .blog-start .blog-content .date-comm-adit a i {
    margin-right: 5px;
}
.blog-style-1-left-list-blog .blog-start .blog-content .date-comm-adit span.blog-admin,
.blog-style-1-right-blog .blog-start .blog-content .date-comm-adit span.blog-admin,
.blog-style-1-list .blog-start .blog-content .date-comm-adit span.blog-admin {
    display: flex;
    align-items: center;
    line-height: 1;
}
.blog-style-1-left-list-blog .blog-start .blog-content .date-comm-adit span.blog-admin i,
.blog-style-1-right-blog .blog-start .blog-content .date-comm-adit span.blog-admin i{
    margin-right: 5px;
}
.blog-style-1-left-list-blog .blog-start .blog-content .date-comm-adit span.blog-admin span.blog-editor,
.blog-style-1-right-blog .blog-start .blog-content .date-comm-adit span.blog-admin span.blog-editor{
    font-weight: 600;
    margin-left: 5px;
}
.blog-style-1-left-list-blog .blog-start .blog-content p.blog-description,
.blog-style-1-right-blog .blog-start .blog-content p.blog-description{
    margin-top: 20px;
    color: #999;
}
.blog-style-1-left-list-blog .blog-start .blog-content a.read-link,
.blog-style-1-right-blog .blog-start .blog-content a.read-link{
    display: flex;
    align-items: center;
    margin-top: 20px;
    font-size: 14px;
    font-weight: 600;
    line-height: 1;
    -webkit-transition: all 0s ease-in-out 0s;
    -o-transition: all 0s ease-in-out 0s;
    transition: all 0s ease-in-out 0s;
}
.blog-style-1-left-list-blog .blog-start .blog-content a.read-link span,
.blog-style-1-right-blog .blog-start .blog-content a.read-link span{
    color: #222;
}
.blog-style-1-left-list-blog .blog-start .blog-content a.read-link:hover span,
.blog-style-1-right-blog .blog-start .blog-content a.read-link:hover span{
    color: #ec6504;
}
.blog-style-1-left-list-blog .blog-start .blog-content a.read-link i,
.blog-style-1-right-blog .blog-start .blog-content a.read-link i{
    padding-left: 5px;
    font-size: 12px;
}
.blog-style-1-left-list-blog .blog-start .blog-content a.read-link:hover i,
.blog-style-1-right-blog .blog-start .blog-content a.read-link:hover i{
    padding-left: 10px;
}
.blog-style-1-left-list-blog .blog-start .blog-content a.read-link span,
.blog-style-1-left-list-blog .blog-start .blog-content a.read-link:hover span,
.blog-style-1-left-list-blog .blog-start .blog-content a.read-link i,
.blog-style-1-left-list-blog .blog-start .blog-content a.read-link:hover i,
.blog-style-1-right-blog .blog-start .blog-content a.read-link span,
.blog-style-1-right-blog .blog-start .blog-content a.read-link:hover span,
.blog-style-1-right-blog .blog-start .blog-content a.read-link i,
.blog-style-1-right-blog .blog-start .blog-content a.read-link:hover i{
    -webkit-transition: all 0.3s ease-in-out 0s;
    -o-transition: all 0.3s ease-in-out 0s;
    transition: all 0.3s ease-in-out 0s;
}
/* full-left-right details blog css */
.blog-style-1-details .single-blog-content,
.blog-style-1-left-details .single-blog-content,
.blog-style-1-right-details .single-blog-content{
    margin-top: 33px;
}
.blog-style-1-details .single-blog-content .single-b-title h4,
.blog-style-1-left-details .single-blog-content .single-b-title h4,
.blog-style-1-right-details .single-blog-content .single-b-title h4{
    font-size: 16px;
}
.blog-style-1-details .single-blog-content .date-edit-comments,
.blog-style-1-left-details .single-blog-content .date-edit-comments,
.blog-style-1-right-details .single-blog-content .date-edit-comments{
    margin-top: 23px;
}
.blog-style-1-details .single-blog-content .date-edit-comments .blog-info-wrap,
.blog-style-1-left-details .single-blog-content .date-edit-comments .blog-info-wrap,
.blog-style-1-right-details .single-blog-content .date-edit-comments .blog-info-wrap {
    display: flex;
    flex-wrap: wrap;
    align-items: center;
    margin: -15px 0px 0px -30px;
}
.blog-style-1-details .single-blog-content .date-edit-comments .blog-info-wrap span.blog-data,
.blog-style-1-left-details .single-blog-content .date-edit-comments .blog-info-wrap span.blog-data,
.blog-style-1-right-details .single-blog-content .date-edit-comments .blog-info-wrap span.blog-data {
    margin: 15px 0px 0px 30px;
}
.blog-style-1-details .single-blog-content .date-edit-comments .blog-info-wrap span.date,
.blog-style-1-left-details .single-blog-content .date-edit-comments .blog-info-wrap span.date,
.blog-style-1-right-details .single-blog-content .date-edit-comments .blog-info-wrap span.date{
    display: flex;
    align-items: center;
    line-height: 1;
}
.blog-style-1-details .single-blog-content .date-edit-comments .blog-info-wrap span.date span.blog-d-n-c,
.blog-style-1-left-details .single-blog-content .date-edit-comments .blog-info-wrap span.date span.blog-d-n-c,
.blog-style-1-right-details .single-blog-content .date-edit-comments .blog-info-wrap span.date span.blog-d-n-c{
    margin-left: 5px;
}
.blog-style-1-details .single-blog-content .date-edit-comments .blog-info-wrap span.blog-edit,
.blog-style-1-left-details .single-blog-content .date-edit-comments .blog-info-wrap span.blog-edit,
.blog-style-1-right-details .single-blog-content .date-edit-comments .blog-info-wrap span.blog-edit{
    display: flex;
    align-items: center;
    line-height: 1;
}
.blog-style-1-details .single-blog-content .date-edit-comments .blog-info-wrap span.blog-edit span.blog-d-n-c,
.blog-style-1-left-details .single-blog-content .date-edit-comments .blog-info-wrap span.blog-edit span.blog-d-n-c,
.blog-style-1-right-details .single-blog-content .date-edit-comments .blog-info-wrap span.blog-edit span.blog-d-n-c{
    margin-left: 5px;
}
.blog-style-1-left-details .single-blog-content .date-edit-comments .blog-info-wrap span.comments,
.blog-style-1-details .single-blog-content .date-edit-comments .blog-info-wrap span.comments,
.blog-style-1-right-details .single-blog-content .date-edit-comments .blog-info-wrap span.comments{
    display: flex;
    align-items: center;
    line-height: 1;
}
.blog-style-1-left-details .single-blog-content .date-edit-comments .blog-info-wrap span.comments span.blog-d-n-c,
.blog-style-1-details .single-blog-content .date-edit-comments .blog-info-wrap span.comments span.blog-d-n-c,
.blog-style-1-right-details .single-blog-content .date-edit-comments .blog-info-wrap span.comments span.blog-d-n-c{
    margin-left: 5px;
}
.blog-style-1-details .single-blog-content .blog-description,
.blog-style-1-left-details .single-blog-content .blog-description,
.blog-style-1-right-details .single-blog-content .blog-description{
    margin-top: 23px;
}
.blog-style-1-details .single-blog-content .blog-description p,
.blog-style-1-left-details .single-blog-content .blog-description p,
.blog-style-1-right-details .single-blog-content .blog-description p{
    margin-top: 2px;
    letter-spacing: 0.6px;
}
.blog-style-1-details .single-blog-content .blog-description .blog-image-description,
.blog-style-1-left-details .single-blog-content .blog-description .blog-image-description,
.blog-style-1-right-details .single-blog-content .blog-description .blog-image-description{
    margin-top: 22px;
}
.blog-style-1-details .single-blog-content .blog-description .blog-image-description img,
.blog-style-1-left-details .single-blog-content .blog-description .blog-image-description img,
.blog-style-1-right-details .single-blog-content .blog-description .blog-image-description img{
    float: left;
    margin-right: 15px;
}
.blog-style-1-details .single-blog-content .blog-description .blog-image-description p.bold-description,
.blog-style-1-left-details .single-blog-content .blog-description .blog-image-description p.bold-description,
.blog-style-1-right-details .single-blog-content .blog-description .blog-image-description p.bold-description{
    font-size: 14px;
    font-weight: 600;
}
.blog-style-1-details .single-blog-content .blog-description p.color-description,
.blog-style-1-left-details .single-blog-content .blog-description p.color-description,
.blog-style-1-right-details .single-blog-content .blog-description p.color-description{
    background-color: #f7f7f7;
    font-size: 16px;
    color: #ec6504;
    margin: 23px 0px;
    padding: 30px;
    border-left: 1px solid #eee;
    font-weight: 500;
    letter-spacing: 0.5px;
}
.blog-style-1-details .single-blog-content .blog-info,
.blog-style-1-left-details .single-blog-content .blog-info,
.blog-style-1-right-details .single-blog-content .blog-info{
    padding: 30px;
    margin-top: 22px;
    background-color: #f7f7f7;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-direction: column;
}
.blog-style-1-details .single-blog-content .blog-info i.fa-quote-left,
.blog-style-1-left-details .single-blog-content .blog-info i.fa-quote-left,
.blog-style-1-right-details .single-blog-content .blog-info i.fa-quote-left{
    font-size: 30px;
    color: #ec6504;
}
.blog-style-1-details .single-blog-content .blog-info h6,
.blog-style-1-left-details .single-blog-content .blog-info h6,
.blog-style-1-right-details .single-blog-content .blog-info h6{
    color: #ec6504;
    font-size: 16px;
    margin-top: 9px;
    font-weight: 500;
}
.blog-style-1-details .single-blog-content .b-link,
.blog-style-1-left-details .single-blog-content .b-link,
.blog-style-1-right-details .single-blog-content .b-link{
    margin-top: 30px;
    display: flex;
    align-items: center;
    justify-content: space-between;
}
.blog-style-1-details .single-blog-content .b-link .blog-tag a,
.blog-style-1-left-details .single-blog-content .b-link .blog-tag a,
.blog-style-1-right-details .single-blog-content .b-link .blog-tag a{
    background-color: #ec6504;
    padding: 5px 10px;
    color: #fff;
    border-radius: 4px;
}
.blog-style-1-details .single-blog-content .b-link .blog-tag a:hover,
.blog-style-1-left-details .single-blog-content .b-link .blog-tag a:hover,
.blog-style-1-right-details .single-blog-content .b-link .blog-tag a:hover{
    background-color: #000;
    color: #fff;
}
.blog-style-1-details .single-blog-content .blog-social,
.blog-style-1-left-details .single-blog-content .blog-social,
.blog-style-1-right-details .single-blog-content .blog-social{
    display: flex;
    align-items: center;
    justify-content: flex-start;
}
.blog-style-1-details .single-blog-content .blog-social a.facebook,
.blog-style-1-details .single-blog-content .blog-social a.twitter,
.blog-style-1-details .single-blog-content .blog-social a.insta,
.blog-style-1-details .single-blog-content .blog-social a.pinterest,
.blog-style-1-left-details .single-blog-content .blog-social a.facebook,
.blog-style-1-left-details .single-blog-content .blog-social a.twitter,
.blog-style-1-left-details .single-blog-content .blog-social a.insta,
.blog-style-1-left-details .single-blog-content .blog-social a.pinterest,
.blog-style-1-right-details .single-blog-content .blog-social a.facebook,
.blog-style-1-right-details .single-blog-content .blog-social a.twitter,
.blog-style-1-right-details .single-blog-content .blog-social a.insta,
.blog-style-1-right-details .single-blog-content .blog-social a.pinterest{
    width: 35px;
    height: 35px;
    font-size: 14px;
    margin-right: 5px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50px;
}
.blog-style-1-details .single-blog-content .blog-social a.facebook,
.blog-style-1-left-details .single-blog-content .blog-social a.facebook,
.blog-style-1-right-details .single-blog-content .blog-social a.facebook{
    background-color: #3b5999;
    color: #fff;
}
.blog-style-1-details .single-blog-content .blog-social a.twitter,
.blog-style-1-left-details .single-blog-content .blog-social a.twitter,
.blog-style-1-right-details .single-blog-content .blog-social a.twitter{
    background-color: #55acee;
    color: #fff;
}
.blog-style-1-details .single-blog-content .blog-social a.insta,
.blog-style-1-left-details .single-blog-content .blog-social a.insta,
.blog-style-1-right-details .single-blog-content .blog-social a.insta{
    background-color: #dd4b39;
    color: #fff;
}
.blog-style-1-details .single-blog-content .blog-social a.pinterest,
.blog-style-1-left-details .single-blog-content .blog-social a.pinterest,
.blog-style-1-right-details .single-blog-content .blog-social a.pinterest{
    background-color: #bd081c;
    color: #fff;
}
.blog-style-1-details .single-blog-content .blog-social a:hover,
.blog-style-1-left-details .single-blog-content .blog-social a:hover,
.blog-style-1-right-details .single-blog-content .blog-social a:hover{
    background-color: #ec6504;
}
.blog-style-1-details .single-blog-content .blog-comments,
.blog-style-1-left-details .single-blog-content .blog-comments,
.blog-style-1-right-details .single-blog-content .blog-comments{
    margin-top: 23px;
}
.blog-style-1-details .single-blog-content .blog-comments h4,
.blog-style-1-left-details .single-blog-content .blog-comments h4,
.blog-style-1-right-details .single-blog-content .blog-comments h4{
    font-size: 18px;
}
.blog-style-1-details .single-blog-content .blog-comments h4 span,
.blog-style-1-left-details .single-blog-content .blog-comments h4 span,
.blog-style-1-right-details .single-blog-content .blog-comments h4 span{
    color: #ec6504;
}
.blog-style-1-details .single-blog-content .blog-comments .blog-comment-info,
.blog-style-1-left-details .single-blog-content .blog-comments .blog-comment-info,
.blog-style-1-right-details .single-blog-content .blog-comments .blog-comment-info {
    margin-top: 23px;
    padding-top: 30px;
    border-top: 1px solid #eee;
}
.blog-style-1-details .single-blog-content .blog-comments .blog-comment-info ul.comments-arae,
.blog-style-1-left-details .single-blog-content .blog-comments .blog-comment-info ul.comments-arae,
.blog-style-1-right-details .single-blog-content .blog-comments .blog-comment-info ul.comments-arae {
    display: flex;
    margin-top: 24px;
}
.blog-style-1-details .single-blog-content .blog-comments .blog-comment-info ul.comments-arae.comment-reply,
.blog-style-1-left-details .single-blog-content .blog-comments .blog-comment-info ul.comments-arae.comment-reply,
.blog-style-1-right-details .single-blog-content .blog-comments .blog-comment-info ul.comments-arae.comment-reply {
    margin-left: 50px;
}
.blog-style-1-details .single-blog-content .blog-comments .blog-comment-info ul.comments-arae.all-reply,
.blog-style-1-left-details .single-blog-content .blog-comments .blog-comment-info ul.comments-arae.all-reply,
.blog-style-1-right-details .single-blog-content .blog-comments .blog-comment-info ul.comments-arae.all-reply {
    margin-top: 24px;
    padding-top: 30px;
    border-top: 1px solid #eee;
}
.blog-style-1-details .single-blog-content .blog-comments .blog-comment-info ul.comments-arae:first-of-type,
.blog-style-1-left-details .single-blog-content .blog-comments .blog-comment-info ul.comments-arae:first-of-type,
.blog-style-1-right-details .single-blog-content .blog-comments .blog-comment-info ul.comments-arae:first-of-type {
    margin-top: 0px;
}
.blog-style-1-details .single-blog-content .blog-comments .blog-comment-info ul.comments-arae li.comments-man,
.blog-style-1-left-details .single-blog-content .blog-comments .blog-comment-info ul.comments-arae li.comments-man,
.blog-style-1-right-details .single-blog-content .blog-comments .blog-comment-info ul.comments-arae li.comments-man{
    width: 45px;
    height: 45px;
    background-color: #ec6504;
    color: #fff;
    margin-right: 15px;
    font-size: 15px;
    border-radius: 3px;
    display: flex;
    align-items: center;
    text-align: center;
    justify-content: center;
    font-weight: 600;
}
.blog-style-1-details .single-blog-content .blog-comments .blog-comment-info ul.comments-arae li.comments-content,
.blog-style-1-left-details .single-blog-content .blog-comments .blog-comment-info ul.comments-arae li.comments-content,
.blog-style-1-right-details .single-blog-content .blog-comments .blog-comment-info ul.comments-arae li.comments-content {
    width: calc(100% - 45px);
}
.blog-style-1-details .single-blog-content .blog-comments .blog-comment-info ul.comments-arae li.comments-content span.comments-result,
.blog-style-1-left-details .single-blog-content .blog-comments .blog-comment-info ul.comments-arae li.comments-content span.comments-result,
.blog-style-1-right-details .single-blog-content .blog-comments .blog-comment-info ul.comments-arae li.comments-content span.comments-result{
    display: block;
}
.blog-style-1-details .single-blog-content .blog-comments .blog-comment-info ul.comments-arae li.comments-content span.comment-name,
.blog-style-1-left-details .single-blog-content .blog-comments .blog-comment-info ul.comments-arae li.comments-content span.comment-name,
.blog-style-1-right-details .single-blog-content .blog-comments .blog-comment-info ul.comments-arae li.comments-content span.comment-name {
    margin-top: 5px;
}
.blog-style-1-details .single-blog-content .blog-comments .blog-comment-info ul.comments-arae li.comments-content span.comment-name i,
.blog-style-1-left-details .single-blog-content .blog-comments .blog-comment-info ul.comments-arae li.comments-content span.comment-name i,
.blog-style-1-right-details .single-blog-content .blog-comments .blog-comment-info ul.comments-arae li.comments-content span.comment-name i {
    font-style: normal;
}
.blog-style-1-details .single-blog-content .blog-comments .blog-comment-info ul.comments-arae li.comments-content span.comments-result.c-date,
.blog-style-1-left-details .single-blog-content .blog-comments .blog-comment-info ul.comments-arae li.comments-content span.comments-result.c-date,
.blog-style-1-right-details .single-blog-content .blog-comments .blog-comment-info ul.comments-arae li.comments-content span.comments-result.c-date{
    font-weight: 600;
    margin-top: 6px;
}
.blog-style-1-details .single-blog-content .blog-comments .blog-comment-info ul.comments-arae li.comments-content span.comments-result.c-date a.Reply,
.blog-style-1-left-details .single-blog-content .blog-comments .blog-comment-info ul.comments-arae li.comments-content span.comments-result.c-date a.Reply,
.blog-style-1-right-details .single-blog-content .blog-comments .blog-comment-info ul.comments-arae li.comments-content span.comments-result.c-date a.Reply {
    margin-left: 30px;
    color: #ec6504;
    font-weight: 500;
}
.blog-style-1-details .single-blog-content .blog-comments .blog-comment-info ul.comments-arae li.comments-content span span.comments-title,
.blog-style-1-left-details .single-blog-content .blog-comments .blog-comment-info ul.comments-arae li.comments-content span span.comments-title,
.blog-style-1-right-details .single-blog-content .blog-comments .blog-comment-info ul.comments-arae li.comments-content span span.comments-title{
    font-weight: 600;
    color: #ec6504;
}
.blog-style-1-details .single-blog-content .comments-form,
.blog-style-1-left-details .single-blog-content .comments-form,
.blog-style-1-right-details .single-blog-content .comments-form{
    margin-top: 24px;
    padding-top: 23px;
    border-top: 1px solid #eee;
}
.blog-style-1-details .single-blog-content .comments-form h4,
.blog-style-1-left-details .single-blog-content .comments-form h4,
.blog-style-1-right-details .single-blog-content .comments-form h4{
    font-size: 18px;
}
.blog-style-1-details .single-blog-content .comments-form form,
.blog-style-1-left-details .single-blog-content .comments-form form,
.blog-style-1-right-details .single-blog-content .comments-form form{
    margin-top: 18px;
}
.blog-style-1-details .single-blog-content .comments-form form label,
.blog-style-1-left-details .single-blog-content .comments-form form label,
.blog-style-1-right-details .single-blog-content .comments-form form label{
    margin-top: 15px;
}
.blog-style-1-details .single-blog-content .comments-form form label:first-child,
.blog-style-1-left-details .single-blog-content .comments-form form label:first-child,
.blog-style-1-right-details .single-blog-content .comments-form form label:first-child {
    margin-top: 0px;
}
.blog-style-1-details .single-blog-content .comments-form form input,
.blog-style-1-left-details .single-blog-content .comments-form form input,
.blog-style-1-right-details .single-blog-content .comments-form form input{
    width: 100%;
    padding: 10px 15px;
    margin-top: 10px;
    border: 1px solid #eee;
    border-radius: 3px;
}
.blog-style-1-details .single-blog-content .comments-form form input:focus,
.blog-style-1-left-details .single-blog-content .comments-form form input:focus,
.blog-style-1-right-details .single-blog-content .comments-form form input:focus{
    border-color: #ec6504;
}
.blog-style-1-details .single-blog-content .comments-form form textarea,
.blog-style-1-left-details .single-blog-content .comments-form form textarea,
.blog-style-1-right-details .single-blog-content .comments-form form textarea{
    width: 100%;
    margin-top: 10px;
    min-height: 100px;
    padding: 10px 15px;
    border: 1px solid #eee;
    border-radius: 3px;
    resize: unset;
}
.blog-style-1-details .single-blog-content .comments-form form textarea:focus,
.blog-style-1-left-details .single-blog-content .comments-form form textarea:focus,
.blog-style-1-right-details .single-blog-content .comments-form form textarea:focus{
    border-color: #ec6504;
}
.blog-style-1-details .single-blog-content .comments-form a.btn-style1,
.blog-style-1-left-details .single-blog-content .comments-form a.btn-style1,
.blog-style-1-right-details .single-blog-content .comments-form a.btn-style1{
    margin-top: 24px;
}
/* center blog css */
.cetner-blog-area{
    counter-reset: my-sec-counter;
    display: flex;
    flex-wrap: wrap;
}
.cetner-blog-area .blog-start{
    position: relative;
}
.cetner-blog-area .blog-start .blog-post{
    margin-top: 15px;
    border: 1px solid #eee;
}
.cetner-blog-area .blog-start:before,
.cetner-blog-area .blog-start:after{
    background-color: #ec6504;
}
.cetner-blog-area .blog-start:before{
    counter-increment: my-sec-counter;
    content: counter(my-sec-counter);
    position: absolute;
    top: 0px;
    left: 15px;
    width: 60px;
    height: 60px;
    font-size: 30px;
    font-weight: 600;
    color: #fff;
    border-radius: 0px 5% 25% 25%;
    align-items: center;
    justify-content: center;
    display: flex;
}
.cetner-blog-area .blog-start:after{
    content: "";
    width: 20px;
    height: 15px;
    position: absolute;
    top: 0px;
    left: 0px;
    opacity: 0.8;
    border-radius: 20px 5px 0px 0px;
}
.cetner-blog-area .blog-start{
    width: 100%;
    margin-top: 30px;
    border-radius: 5px;
}
.cetner-blog-area .blog-start:nth-child(1){
    margin-top: 0px;
}
.cetner-blog-area .blog-start .blog-image{
    display: flex;
}
.cetner-blog-area .blog-start .blog-content{
    padding: 30px;
}
.cetner-blog-area .blog-start .blog-content .blog-title h6{
    font-size: 16px;
}
.cetner-blog-area .blog-start .blog-content .blog-title h6 a{
    display: block;
    width: 100%;
    white-space: nowrap;
    text-overflow: ellipsis;
    overflow: hidden;
}
.cetner-blog-area .blog-start .blog-content span.blog-admin{
    display: block;
    margin-top: 7px;
    line-height: 1;
}
.cetner-blog-area .blog-start .blog-content span.blog-admin span.blog-editor{
    color: #000;
    font-weight: 600;
}
.cetner-blog-area .blog-start .blog-content p.blog-description{
    font-size: 14px;
    margin-top: 21px;
}
.cetner-blog-area .blog-start .blog-content a.read-link{
    display: flex;
    align-items: center;
    margin-top: 20px;
    font-size: 14px;
    font-weight: 600;
    line-height: 1;
    -webkit-transition: all 0s ease-in-out 0s;
    -o-transition: all 0s ease-in-out 0s;
    transition: all 0s ease-in-out 0s;
}
.cetner-blog-area .blog-start .blog-content a.read-link span{
    color: #222;
}
.cetner-blog-area .blog-start .blog-content a.read-link:hover span{
    color: #ec6504;
}
.cetner-blog-area .blog-start .blog-content a.read-link i{
    padding-left: 5px;
    font-size: 12px;
}
.cetner-blog-area .blog-start .blog-content a.read-link:hover i{
    padding-left: 10px;
}
.cetner-blog-area .blog-start .blog-content a.read-link span,
.cetner-blog-area .blog-start .blog-content a.read-link:hover span,
.cetner-blog-area .blog-start .blog-content a.read-link i,
.cetner-blog-area .blog-start .blog-content a.read-link:hover i{
    -webkit-transition: all 0.3s ease-in-out 0s;
    -o-transition: all 0.3s ease-in-out 0s;
    transition: all 0.3s ease-in-out 0s;
}
.cetner-blog-area .blog-start .blog-content .blog-date-comment{
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-top: 9px;
}
.cetner-blog-area .blog-start .blog-content .blog-date-comment a{
    font-size: 14px;
    font-weight: 600;
}
/* center blog right column btn css */
.right-area .b-Reply form a.btn-style1{
    width: 100%;
    margin-top: 30px;
}
.center-right-area.style-3 .b-Reply form a.Reply-link-3{
    background-color: #5fa800;
    width: 100%;
    position: relative;
    color: #fff;
    font-size: 13px;
    padding: 8px 25px;
    margin-top: 30px;
    border: 2px solid #5fa800;
    text-align: center;
    text-transform: uppercase;
    font-weight: 600;
    border-radius: 4px;
}
.right-area.style-5 .b-Reply form a.Reply-link{
    position: relative;
    color: #fff;
    font-size: 14px;
    padding: 10px 30px;
    background-color: #ec6504;
    
    font-weight: 600;
    border: 2px solid #ec6504;
    border-radius: 3px;
    border-color: #ec6504;
}
.right-area.style-5 .b-Reply form a.Reply-link:hover{
    background-color: transparent;
    color: #ec6504;
    border-color: #ec6504;
}
.center-right-area.style-6 .b-Reply form a.Reply-link-6{
    position: relative;
    width: 100%;
    background-color: #73841b;
    color: #fff;
    font-size: 14px;
    padding: 10px 30px;
    margin-top: 30px;
    text-align: center;
    text-transform: uppercase;
    font-weight: 600;
    border-radius: 50px;
    border-color: #73841b;
}
/* style-2 blog pagination css */
.all-page span.page-title{
    color: #333;
    display: block;
    text-align: center;
    margin-top: 30px;
    font-weight: 500;
}
.all-page .page-number{
    text-align: center;
    margin-top: 19px;
}
.all-page .page-number a{
    position: relative;
    margin-right: 5px;
}
.all-page .page-number a:after{
    background-color: #5fa800;
    content: "";
    position: absolute;
    bottom: 0px;
    left: 1px;
    right: 0px;
    width: 4px;
    height: 4px;
    border-radius: 100%;
    opacity: 0;
    visibility: hidden;
}
.all-page .page-number a:hover:after,
.all-page .page-number a.active:after{
    opacity: 1;
    visibility: visible;
}
.all-page .page-number a:hover,
.all-page .page-number a.active{
    color: #5fa800;
}
.all-page .page-number a:last-child:after{
    display: none;
}
/* right column css */
.right-area.style-1 .right-column-start .archive-link ul li a:hover,
.right-area.style-5 .right-column-start .archive-link ul li a:hover{
    color: #ec6504;
}
.right-area.style-2 .right-column-start .archive-link ul li a:hover,
.center-right-area.style-3 .right-column-start .archive-link ul li a:hover,
.right-area.style-3 .right-column-start .archive-link ul li a:hover{
    color: #5fa800;
}
.center-right-area.style-6 .right-column-start .archive-link ul li a:hover{
    color: #73841b;
}
.right-area.style-7 .right-column-start .archive-link ul li a:hover{
    color: #cd7752;
}
.right-area .b-Reply form a.Reply-link{
    background-color: #5fa800;
    color: #fff;
    width: 100%;
    padding: 8px 15px;
    margin-top: 30px;
    text-align: center;
    font-weight: 500;
    border: 2px solid #5fa800;
}
.right-area .b-Reply form a.Reply-link:hover{
    background-color: transparent;
    color: #000;
    border-color: #5fa800;
}
/* left-right-full grid blog css */
.full-blog-style-2,
.left-style-2-blog,
.right-style-2-blog{
    display: flex;
    flex-wrap: wrap;
    margin-left: -30px;
    margin-top: -30px;
}
.full-blog-style-2 .blog-start,
.left-style-2-blog .blog-start,
.right-style-2-blog .blog-start{
    width: calc(33.33% - 30px);
    margin-left: 30px;
    margin-top: 30px;
}
.full-blog-style-2 .blog-start .blog-image a,
.left-style-2-blog .blog-start .blog-image a,
.right-style-2-blog .blog-start .blog-image a{
    display: block;
}
.full-blog-style-2 .blog-start .blog-image a img,
.left-style-2-blog .blog-start .blog-image a img,
.right-style-2-blog .blog-start .blog-image a img{
    backface-visibility: hidden;
}
.full-blog-style-2 .blog-start .blog-content,
.left-style-2-blog .blog-start .blog-content,
.right-style-2-blog .blog-start .blog-content{
    padding-top: 30px;
}
.full-blog-style-2 .blog-start .blog-content .blog-date-comment,
.left-style-2-blog .blog-start .blog-content .blog-date-comment,
.right-style-2-blog .blog-start .blog-content .blog-date-comment{
    display: flex;
    justify-content: space-between;
}
.right-style-2 .blog-start .blog-content .blog-date-comment span.blog-date,
.right-style-2 .blog-start .blog-content .blog-date-comment a,
.left-style-2-blog .blog-start .blog-content .blog-date-comment span.blog-date,
.right-style-2-blog .blog-start .blog-content .blog-date-comment a {
    display: flex;
    align-items: center;
    line-height: 1;
} 
.right-style-2 .blog-start .blog-content .blog-date-comment span.blog-date i,
.right-style-2 .blog-start .blog-content .blog-date-comment a i,
.left-style-2-blog .blog-start .blog-content .blog-date-comment span.blog-date i,
.right-style-2-blog .blog-start .blog-content .blog-date-comment a i {
    margin-right: 5px;
} 
.full-blog-style-2 .blog-start .blog-content .blog-date-comment span.blog-date{
    display: flex;
    align-items: center;
    line-height: 1;
} 
.full-blog-style-2 .blog-start .blog-content .blog-date-comment span.blog-date i{
    margin-right: 5px;
} 
.full-blog-style-2 .blog-start .blog-content .blog-date-comment a,
.left-style-2-blog .blog-start .blog-content .blog-date-comment a,
.right-style-2-blog .blog-start .blog-content .blog-date-comment a{
    display: flex;
    align-items: center;
    line-height: 1;
}
.full-blog-style-2 .blog-start .blog-content .blog-date-comment a:hover,
.left-style-2-blog .blog-start .blog-content .blog-date-comment a:hover,
.right-style-2-blog .blog-start .blog-content .blog-date-comment a:hover{
    color: #5fa800;
}
.left-style-2-blog .blog-start .blog-content .blog-date-comment span.blog-date i,
.full-blog-style-2 .blog-start .blog-content .blog-date-comment a i,
.left-style-2-blog .blog-start .blog-content .blog-date-comment a i{
    margin-right: 5px;
}
.full-blog-style-2 .blog-start .blog-content .blog-title h6,
.left-style-2-blog .blog-start .blog-content .blog-title h6,
.right-style-2-blog .blog-start .blog-content .blog-title h6{
    font-size: 16px;
    margin-top: 20px;
    padding-top: 13px;
    border-top: 1px solid #ededed;
}
.full-blog-style-2 .blog-start .blog-content .blog-title h6 a,
.left-style-2-blog .blog-start .blog-content .blog-title h6 a,
.right-style-2-blog .blog-start .blog-content .blog-title h6 a{
    display: block;
    width: 100%;
    white-space: nowrap;
    text-overflow: ellipsis;
    overflow: hidden;
}
.full-blog-style-2 .blog-start .blog-content .blog-title h6 a:hover,
.left-style-2-blog .blog-start .blog-content .blog-title h6 a:hover,
.right-style-2-blog .blog-start .blog-content .blog-title h6 a:hover{
    color: #5fa800;
}
.full-blog-style-2 .blog-start .blog-content p.blog-description,
.left-style-2-blog .blog-start .blog-content p.blog-description,
.right-style-2-blog .blog-start .blog-content p.blog-description{
    margin-top: 16px;
}
.full-blog-style-2 .blog-start .blog-content .more-blog,
.left-style-2-blog .blog-start .blog-content .more-blog,
.right-style-2-blog .blog-start .blog-content .more-blog{
    margin-top: 17px;
    display: flex;
    align-items: center;
    justify-content: space-between;
}
.full-blog-style-2 .blog-start .blog-content .more-blog a.read-link,
.left-style-2-blog .blog-start .blog-content .more-blog a.read-link,
.right-style-2-blog .blog-start .blog-content .more-blog a.read-link{
    color: #5fa800;
    font-size: 14px;
    font-weight: 600;
}
.full-blog-style-2 .blog-start .blog-content .more-blog a.read-link i,
.left-style-2-blog .blog-start .blog-content .more-blog a.read-link i,
.right-style-2-blog .blog-start .blog-content .more-blog a.read-link i{
    font-size: 12px;
}
.full-blog-style-2 .blog-start .blog-content .more-blog a.read-link i,
.full-blog-style-2 .blog-start .blog-content .more-blog a.read-link:hover i,
.left-style-2-blog .blog-start .blog-content .more-blog a.read-link i,
.left-style-2-blog .blog-start .blog-content .more-blog a.read-link:hover i,
.right-style-2-blog .blog-start .blog-content .more-blog a.read-link i,
.right-style-2-blog .blog-start .blog-content .more-blog a.read-link:hover i{
    -webkit-transition: all 0.3s ease-in-out 0s;
    -o-transition: all 0.3s ease-in-out 0s;
    transition: all 0.3s ease-in-out 0s;
}
.full-blog-style-2 .blog-start .blog-content .more-blog a.read-link:hover i,
.left-style-2-blog .blog-start .blog-content .more-blog a.read-link:hover i,
.right-style-2-blog .blog-start .blog-content .more-blog a.read-link:hover i{
    margin-left: 8px;
}
.full-blog-style-2 .blog-start .blog-content .more-blog span.blog-admin span.blog-editor,
.left-style-2-blog .blog-start .blog-content .more-blog span.blog-admin span.blog-editor,
.right-style-2-blog .blog-start .blog-content .more-blog span.blog-admin span.blog-editor{
    font-weight: 600;
}
/* left-right-full list blog css */
.full-blog-list-style-2 .blog-start{
    width: 100%;
    display: flex;
    align-items: center;
    border-bottom: 1px solid #eee;
    margin-bottom: 20px;
    padding-bottom: 20px;
}
.left-blog-list-style-2 .blog-start{
    width: 100%;
    display: flex;
    align-items: center;
    border-bottom: 1px solid #eee;
    margin-bottom: 20px;
    padding-bottom: 20px;
}
.right-blog-list-style-2 .blog-start{
    width: 100%;
    display: flex;
    align-items: center;
    border-bottom: 1px solid #eee;
    margin-bottom: 20px;
    padding-bottom: 20px;
}
.full-blog-list-style-2 .blog-start .blog-image,
.left-blog-list-style-2 .blog-start .blog-image,
.right-blog-list-style-2 .blog-start .blog-image{
    width: 50%;
}
.full-blog-list-style-2 .blog-start .blog-image a,
.left-blog-list-style-2 .blog-start .blog-image a,
.right-blog-list-style-2 .blog-start .blog-image a{
    display: block;
}
.full-blog-list-style-2 .blog-start .blog-image a img,
.left-blog-list-style-2 .blog-start .blog-image a img,
.right-blog-list-style-2 .blog-start .blog-image a img {
    backface-visibility: hidden;
}
.full-blog-list-style-2 .blog-start .blog-content,
.left-blog-list-style-2 .blog-start .blog-content,
.right-blog-list-style-2 .blog-start .blog-content{
    width: calc(50% - 30px);
    margin-left: 30px;
}
.full-blog-list-style-2 .blog-start .blog-content .blog-date-comment,
.left-blog-list-style-2 .blog-start .blog-content .blog-date-comment,
.right-blog-list-style-2 .blog-start .blog-content .blog-date-comment{
    display: flex;
    justify-content: space-between;
}
.full-blog-list-style-2 .blog-start .blog-content .blog-date-comment a,
.left-blog-list-style-2 .blog-start .blog-content .blog-date-comment a,
.right-blog-list-style-2 .blog-start .blog-content .blog-date-comment a,
.full-blog-list-style-2 .blog-start .blog-content .blog-date-comment span.blog-date,
.left-blog-list-style-2 .blog-start .blog-content .blog-date-comment span.blog-date,
.right-blog-list-style-2 .blog-start .blog-content .blog-date-comment span.blog-date {
    display: flex;
    align-items: center;
    line-height: 1;
}
.full-blog-list-style-2 .blog-start .blog-content .blog-date-comment a:hover,
.left-blog-list-style-2 .blog-start .blog-content .blog-date-comment a:hover,
.right-blog-list-style-2 .blog-start .blog-content .blog-date-comment a:hover{
    color: #5fa800;
}
.full-blog-list-style-2 .blog-start .blog-content .blog-date-comment a i,
.left-blog-list-style-2 .blog-start .blog-content .blog-date-comment a i,
.right-blog-list-style-2 .blog-start .blog-content .blog-date-comment a i,
.full-blog-list-style-2 .blog-start .blog-content .blog-date-comment span.blog-date i,
.left-blog-list-style-2 .blog-start .blog-content .blog-date-comment span.blog-date i,
.right-blog-list-style-2 .blog-start .blog-content .blog-date-comment span.blog-date i{
    margin-right: 5px;
}
.full-blog-list-style-2 .blog-start .blog-content .blog-title h6,
.left-blog-list-style-2 .blog-start .blog-content .blog-title h6,
.right-blog-list-style-2 .blog-start .blog-content .blog-title h6{
    font-size: 16px;
    margin-top: 30px;
    padding-top: 23px;
    border-top: 1px solid #ededed;
}
.full-blog-list-style-2 .blog-start .blog-content .blog-title h6 a,
.left-blog-list-style-2 .blog-start .blog-content .blog-title h6 a,
.right-blog-list-style-2 .blog-start .blog-content .blog-title h6 a{
    display: block;
    width: 100%;
    white-space: nowrap;
    text-overflow: ellipsis;
    overflow: hidden;
}
.full-blog-list-style-2 .blog-start .blog-content .blog-title h6 a:hover,
.left-blog-list-style-2 .blog-start .blog-content .blog-title h6 a:hover,
.right-blog-list-style-2 .blog-start .blog-content .blog-title h6 a:hover{
    color: #5fa800;
}
.full-blog-list-style-2 .blog-start .blog-content p.blog-description,
.left-blog-list-style-2 .blog-start .blog-content p.blog-description,
.right-blog-list-style-2 .blog-start .blog-content p.blog-description{
    margin-top: 16px;
}
.full-blog-list-style-2 .blog-start .blog-content .more-blog,
.left-blog-list-style-2 .blog-start .blog-content .more-blog,
.right-blog-list-style-2 .blog-start .blog-content .more-blog{
    margin-top: 17px;
    display: flex;
    align-items: center;
    justify-content: space-between;
}
.full-blog-list-style-2 .blog-start .blog-content .more-blog a.read-link,
.left-blog-list-style-2 .blog-start .blog-content .more-blog a.read-link,
.right-blog-list-style-2 .blog-start .blog-content .more-blog a.read-link{
    color: #5fa800;
    font-size: 14px;
    font-weight: 600;
}
.full-blog-list-style-2 .blog-start .blog-content .more-blog a.read-link i,
.left-blog-list-style-2 .blog-start .blog-content .more-blog a.read-link i,
.right-blog-list-style-2 .blog-start .blog-content .more-blog a.read-link i{
    font-size: 12px;
}
.full-blog-list-style-2 .blog-start .blog-content .more-blog a.read-link i,
.full-blog-list-style-2 .blog-start .blog-content .more-blog a.read-link:hover i,
.left-blog-list-style-2 .blog-start .blog-content .more-blog a.read-link i,
.left-blog-list-style-2 .blog-start .blog-content .more-blog a.read-link:hover i,
.right-blog-list-style-2 .blog-start .blog-content .more-blog a.read-link i,
.right-blog-list-style-2 .blog-start .blog-content .more-blog a.read-link:hover i{
    -webkit-transition: all 0.3s ease-in-out 0s;
    -o-transition: all 0.3s ease-in-out 0s;
    transition: all 0.3s ease-in-out 0s;
}
.full-blog-list-style-2 .blog-start .blog-content .more-blog a.read-link:hover i,
.left-blog-list-style-2 .blog-start .blog-content .more-blog a.read-link:hover i,
.right-blog-list-style-2 .blog-start .blog-content .more-blog a.read-link:hover i{
    margin-left: 8px;
}
.full-blog-list-style-2 .blog-start .blog-content .more-blog span.blog-admin span.blog-editor,
.left-blog-list-style-2 .blog-start .blog-content .more-blog span.blog-admin span.blog-editor,
.right-blog-list-style-2 .blog-start .blog-content .more-blog span.blog-admin span.blog-editor{
    font-weight: 600;
}
/* left-right-full details blog css */
.style-2-full-blog-area .single-image{
    margin-bottom: 33px;
}
.style-2-right-blog-details .single-blog-content,
.style-2-left-blog-details .single-blog-content{
    margin-top: 33px;
}
.style-2-full-blog-area .single-blog-content .single-b-title h4,
.style-2-right-blog-details .single-blog-content .single-b-title h4,
.style-2-left-blog-details .single-blog-content .single-b-title h4{
    font-size: 16px;
}
.style-2-full-blog-area .single-blog-content .date-edit-comments,
.style-2-right-blog-details .single-blog-content .date-edit-comments,
.style-2-left-blog-details .single-blog-content .date-edit-comments{
    margin-top: 23px;
}
.style-2-full-blog-area .single-blog-content .date-edit-comments .blog-info-wrap,
.style-2-right-blog-details .single-blog-content .date-edit-comments .blog-info-wrap,
.style-2-left-blog-details .single-blog-content .date-edit-comments .blog-info-wrap{
    display: flex;
    flex-wrap: wrap;
    align-items: center;
    margin: -15px 0px 0px -30px;
}
.style-2-full-blog-area .single-blog-content .date-edit-comments .blog-info-wrap span.blog-data,
.style-2-right-blog-details .single-blog-content .date-edit-comments .blog-info-wrap span.blog-data,
.style-2-left-blog-details .single-blog-content .date-edit-comments .blog-info-wrap span.blog-data{
    margin: 15px 0px 0px 30px;
}
.style-2-full-blog-area .single-blog-content .date-edit-comments .blog-info-wrap span.date,
.style-2-right-blog-details .single-blog-content .date-edit-comments .blog-info-wrap span.date,
.style-2-left-blog-details .single-blog-content .date-edit-comments .blog-info-wrap span.date{
    margin-right: 30px;
    display: flex;
    align-items: center;
    line-height: 1;
}
.style-2-full-blog-area .single-blog-content .date-edit-comments .blog-info-wrap span.date span.blog-d-n-c,
.style-2-right-blog-details .single-blog-content .date-edit-comments .blog-info-wrap span.date span.blog-d-n-c,
.style-2-left-blog-details .single-blog-content .date-edit-comments .blog-info-wrap span.date span.blog-d-n-c{
    margin-left: 5px;
}
.style-2-full-blog-area .single-blog-content .date-edit-comments .blog-info-wrap span.blog-edit,
.style-2-right-blog-details .single-blog-content .date-edit-comments .blog-info-wrap span.blog-edit,
.style-2-left-blog-details .single-blog-content .date-edit-comments .blog-info-wrap span.blog-edit{
    margin-right: 30px;
    display: flex;
    align-items: center;
    line-height: 1;
}
.style-2-full-blog-area .single-blog-content .date-edit-comments .blog-info-wrap span.blog-edit span.blog-d-n-c,
.style-2-right-blog-details .single-blog-content .date-edit-comments .blog-info-wrap span.blog-edit span.blog-d-n-c,
.style-2-left-blog-details .single-blog-content .date-edit-comments .blog-info-wrap span.blog-edit span.blog-d-n-c{
    margin-left: 5px;
}
.style-2-full-blog-area .single-blog-content .date-edit-comments .blog-info-wrap span.comments,
.style-2-right-blog-details .single-blog-content .date-edit-comments .blog-info-wrap span.comments,
.style-2-left-blog-details .single-blog-content .date-edit-comments .blog-info-wrap span.comments{
    display: flex;
    align-items: center;
    line-height: 1;
}
.style-2-full-blog-area .single-blog-content .date-edit-comments .blog-info-wrap span.comments span.blog-d-n-c,
.style-2-right-blog-details .single-blog-content .date-edit-comments .blog-info-wrap span.comments span.blog-d-n-c,
.style-2-left-blog-details .single-blog-content .date-edit-comments .blog-info-wrap span.comments span.blog-d-n-c{
    margin-left: 5px;
}
.style-2-full-blog-area .single-blog-content .blog-description,
.style-2-right-blog-details .single-blog-content .blog-description,
.style-2-left-blog-details .single-blog-content .blog-description{
    margin-top: 23px;
}
.style-2-full-blog-area .single-blog-content .blog-description p,
.style-2-right-blog-details .single-blog-content .blog-description p,
.style-2-left-blog-details .single-blog-content .blog-description p{
    margin-top: 1px;
}
.style-2-full-blog-area .single-blog-content .blog-description .blog-image-description,
.style-2-right-blog-details .single-blog-content .blog-description .blog-image-description,
.style-2-left-blog-details .single-blog-content .blog-description .blog-image-description{
    margin-top: 22px;
}
.style-2-full-blog-area .single-blog-content .blog-description .blog-image-description img,
.style-2-right-blog-details .single-blog-content .blog-description .blog-image-description img,
.style-2-left-blog-details .single-blog-content .blog-description .blog-image-description img{
    float: left;
    margin-right: 30px;
}
.style-2-full-blog-area .single-blog-content .blog-description .blog-image-description p.bold-description,
.style-2-right-blog-details .single-blog-content .blog-description .blog-image-description p.bold-description,
.style-2-left-blog-details .single-blog-content .blog-description .blog-image-description p.bold-description{
    font-size: 15px;
    font-weight: 600;
}
.style-2-full-blog-area .single-blog-content .blog-description p.color-description,
.style-2-right-blog-details .single-blog-content .blog-description p.color-description,
.style-2-left-blog-details .single-blog-content .blog-description p.color-description{
    background-color: #f7f7f7;
    font-size: 16px;
    color: #5fa800;
    margin: 22px 0px;
    padding: 30px;
    border-left: 1px solid #eee;
}
.style-2-full-blog-area .single-blog-content .blog-info,
.style-2-right-blog-details .single-blog-content .blog-info,
.style-2-left-blog-details .single-blog-content .blog-info{
    padding: 30px;
    margin-top: 22px;
    background-color: #f7f7f7;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-direction: column;
}
.style-2-full-blog-area .single-blog-content .blog-info i.fa-quote-left,
.style-2-right-blog-details .single-blog-content .blog-info i.fa-quote-left,
.style-2-left-blog-details .single-blog-content .blog-info i.fa-quote-left{
    font-size: 30px;
    color: #5fa800;
}
.style-2-full-blog-area .single-blog-content .blog-info h6,
.style-2-right-blog-details .single-blog-content .blog-info h6,
.style-2-left-blog-details .single-blog-content .blog-info h6 {
    color: #5fa800;
    font-size: 16px;
    font-weight: 600;
    margin-top: 9px;
}
.style-2-full-blog-area .single-blog-content .b-link,
.style-2-right-blog-details .single-blog-content .b-link,
.style-2-left-blog-details .single-blog-content .b-link{
    margin-top: 30px;
    display: flex;
    align-items: center;
    justify-content: space-between;
}
.style-2-full-blog-area .single-blog-content .b-link a,
.style-2-right-blog-details .single-blog-content .b-link a,
.style-2-left-blog-details .single-blog-content .b-link a{
    background-color: #5fa800;
    padding: 5px 10px;
    color: #fff;
    border-radius: 4px;
}
.style-2-full-blog-area .single-blog-content .b-link a:hover,
.style-2-right-blog-details .single-blog-content .b-link a:hover,
.style-2-left-blog-details .single-blog-content .b-link a:hover{
    background-color: #000;
    color: #fff;
}
.style-2-full-blog-area .single-blog-content .blog-social,
.style-2-right-blog-details .single-blog-content .blog-social,
.style-2-left-blog-details .single-blog-content .blog-social{
    display: flex;
    align-items: center;
    justify-content: flex-start;
}
.style-2-full-blog-area .single-blog-content .blog-social a.facebook,
.style-2-full-blog-area .single-blog-content .blog-social a.twitter,
.style-2-full-blog-area .single-blog-content .blog-social a.insta,
.style-2-full-blog-area .single-blog-content .blog-social a.pinterest,
.style-2-right-blog-details .single-blog-content .blog-social a.facebook,
.style-2-right-blog-details .single-blog-content .blog-social a.twitter,
.style-2-right-blog-details .single-blog-content .blog-social a.insta,
.style-2-right-blog-details .single-blog-content .blog-social a.pinterest,
.style-2-left-blog-details .single-blog-content .blog-social a.facebook,
.style-2-left-blog-details .single-blog-content .blog-social a.twitter,
.style-2-left-blog-details .single-blog-content .blog-social a.insta,
.style-2-left-blog-details .single-blog-content .blog-social a.pinterest{
    width: 30px;
    height: 30px;
    margin-right: 7px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 4px;
}
.style-2-full-blog-area .single-blog-content .blog-social a.facebook,
.style-2-right-blog-details .single-blog-content .blog-social a.facebook,
.style-2-left-blog-details .single-blog-content .blog-social a.facebook{
    background-color: #3b5999;
    color: #fff;
}
.style-2-full-blog-area .single-blog-content .blog-social a.twitter,
.style-2-right-blog-details .single-blog-content .blog-social a.twitter,
.style-2-left-blog-details .single-blog-content .blog-social a.twitter{
    background-color: #55acee;
    color: #fff;
}
.style-2-full-blog-area .single-blog-content .blog-social a.insta,
.style-2-right-blog-details .single-blog-content .blog-social a.insta,
.style-2-left-blog-details .single-blog-content .blog-social a.insta{
    background-color: #dd4b39;
    color: #fff;
}
.style-2-full-blog-area .single-blog-content .blog-social a.pinterest,
.style-2-right-blog-details .single-blog-content .blog-social a.pinterest,
.style-2-left-blog-details .single-blog-content .blog-social a.pinterest{
    background-color: #bd081c;
    color: #fff;
}
.style-2-full-blog-area .single-blog-content .blog-social a:hover,
.style-2-right-blog-details .single-blog-content .blog-social a:hover,
.style-2-left-blog-details .single-blog-content .blog-social a:hover{
    background-color: #5fa800;
}
.style-2-full-blog-area .single-blog-content .blog-comments,
.style-2-right-blog-details .single-blog-content .blog-comments,
.style-2-left-blog-details .single-blog-content .blog-comments{
    margin-top: 23px;
}
.style-2-full-blog-area .single-blog-content .blog-comments h4,
.style-2-right-blog-details .single-blog-content .blog-comments h4,
.style-2-left-blog-details .single-blog-content .blog-comments h4{
    font-size: 18px;
}
.style-2-full-blog-area .single-blog-content .blog-comments h4 span,
.style-2-right-blog-details .single-blog-content .blog-comments h4 span,
.style-2-left-blog-details .single-blog-content .blog-comments h4 span{
    color: #5fa800;
}
.style-2-full-blog-area .single-blog-content .blog-comments .blog-comment-info,
.style-2-right-blog-details .single-blog-content .blog-comments .blog-comment-info,
.style-2-left-blog-details .single-blog-content .blog-comments .blog-comment-info {
    margin-top: 23px;
    padding-top: 30px;
    border-top: 1px solid #eee;
}
.style-2-full-blog-area .single-blog-content .blog-comments .blog-comment-info ul.comments-arae,
.style-2-right-blog-details .single-blog-content .blog-comments .blog-comment-info ul.comments-arae,
.style-2-left-blog-details .single-blog-content .blog-comments .blog-comment-info ul.comments-arae{
    margin-top: 24px;
    display: flex;
}
.style-2-full-blog-area .single-blog-content .blog-comments .blog-comment-info ul.comments-arae:first-of-type,
.style-2-right-blog-details .single-blog-content .blog-comments .blog-comment-info ul.comments-arae:first-of-type,
.style-2-left-blog-details .single-blog-content .blog-comments .blog-comment-info ul.comments-arae:first-of-type{
    margin-top: 0px;
}
.style-2-full-blog-area .single-blog-content .blog-comments .blog-comment-info ul.comments-arae.comment-reply,
.style-2-right-blog-details .single-blog-content .blog-comments .blog-comment-info ul.comments-arae.comment-reply,
.style-2-left-blog-details .single-blog-content .blog-comments .blog-comment-info ul.comments-arae.comment-reply{
    padding-left: 50px;
}
.style-2-full-blog-area .single-blog-content .blog-comments .blog-comment-info ul.comments-arae.all-reply,
.style-2-right-blog-details .single-blog-content .blog-comments .blog-comment-info ul.comments-arae.all-reply,
.style-2-left-blog-details .single-blog-content .blog-comments .blog-comment-info ul.comments-arae.all-reply {
    margin-top: 24px;
    padding-top: 30px;
    border-top: 1px solid #eee;
}
.style-2-full-blog-area .single-blog-content .blog-comments .blog-comment-info ul.comments-arae li.comments-man,
.style-2-right-blog-details .single-blog-content .blog-comments .blog-comment-info ul.comments-arae li.comments-man,
.style-2-left-blog-details .single-blog-content .blog-comments .blog-comment-info ul.comments-arae li.comments-man{
    width: 45px;
    height: 45px;
    background-color: #5fa800;
    color: #fff;
    margin-right: 15px;
    font-size: 15px;
    border-radius: 3px;
    display: flex;
    align-items: center;
    text-align: center;
    justify-content: center;
    font-weight: 600;
}
.style-2-full-blog-area .single-blog-content .blog-comments .blog-comment-info ul.comments-arae li.comments-content,
.style-2-right-blog-details .single-blog-content .blog-comments .blog-comment-info ul.comments-arae li.comments-content,
.style-2-left-blog-details .single-blog-content .blog-comments .blog-comment-info ul.comments-arae li.comments-content {
    width: calc(100% - 45px);
}
.style-2-full-blog-area .single-blog-content .blog-comments .blog-comment-info ul.comments-arae li.comments-content span.comments-result,
.style-2-right-blog-details .single-blog-content .blog-comments .blog-comment-info ul.comments-arae li.comments-content span.comments-result,
.style-2-left-blog-details .single-blog-content .blog-comments .blog-comment-info ul.comments-arae li.comments-content span.comments-result{
    display: block;
}
.style-2-full-blog-area .single-blog-content .blog-comments .blog-comment-info ul.comments-arae li.comments-content span.comment-name,
.style-2-right-blog-details .single-blog-content .blog-comments .blog-comment-info ul.comments-arae li.comments-content span.comment-name,
.style-2-left-blog-details .single-blog-content .blog-comments .blog-comment-info ul.comments-arae li.comments-content span.comment-name {
    margin-top: 5px;
    margin-bottom: 5px;
}
.style-2-full-blog-area .single-blog-content .blog-comments .blog-comment-info ul.comments-arae li.comments-content span.comment-name i,
.style-2-right-blog-details .single-blog-content .blog-comments .blog-comment-info ul.comments-arae li.comments-content span.comment-name i,
.style-2-left-blog-details .single-blog-content .blog-comments .blog-comment-info ul.comments-arae li.comments-content span.comment-name i {
    font-style: normal;
}
.style-2-full-blog-area .single-blog-content .blog-comments .blog-comment-info ul.comments-arae li.comments-content span.comments-result.c-date,
.style-2-right-blog-details .single-blog-content .blog-comments .blog-comment-info ul.comments-arae li.comments-content span.comments-result.c-date,
.style-2-left-blog-details .single-blog-content .blog-comments .blog-comment-info ul.comments-arae li.comments-content span.comments-result.c-date{
    font-weight: 600;
}
.style-2-full-blog-area .single-blog-content .blog-comments .blog-comment-info ul.comments-arae li.comments-content span.comments-result.c-date a.Reply,
.style-2-right-blog-details .single-blog-content .blog-comments .blog-comment-info ul.comments-arae li.comments-content span.comments-result.c-date a.Reply,
.style-2-left-blog-details .single-blog-content .blog-comments .blog-comment-info ul.comments-arae li.comments-content span.comments-result.c-date a.Reply {
    color: #5fa800;
    margin-left: 30px;
    font-weight: 500;
}
.style-2-full-blog-area .single-blog-content .blog-comments .blog-comment-info ul.comments-arae li.comments-content span span.comments-title,
.style-2-right-blog-details .single-blog-content .blog-comments .blog-comment-info ul.comments-arae li.comments-content span span.comments-title,
.style-2-left-blog-details .single-blog-content .blog-comments .blog-comment-info ul.comments-arae li.comments-content span span.comments-title{
    font-weight: 600;
    color: #5fa800;
}
.style-2-full-blog-area .single-blog-content .comments-form,
.style-2-right-blog-details .single-blog-content .comments-form,
.style-2-left-blog-details .single-blog-content .comments-form{
    margin-top: 24px;
    padding-top: 23px;
    border-top: 1px solid #eee;
}
.style-2-full-blog-area .single-blog-content .comments-form h4,
.style-2-right-blog-details .single-blog-content .comments-form h4,
.style-2-left-blog-details .single-blog-content .comments-form h4{
    font-size: 18px;
}
.style-2-full-blog-area .single-blog-content .comments-form form,
.style-2-right-blog-details .single-blog-content .comments-form form,
.style-2-left-blog-details .single-blog-content .comments-form form{
    margin-top: 18px;
}
.style-2-full-blog-area .single-blog-content .comments-form form label,
.style-2-right-blog-details .single-blog-content .comments-form form label,
.style-2-left-blog-details .single-blog-content .comments-form form label{
    margin-top: 15px;
}
.style-2-full-blog-area .single-blog-content .comments-form form label:first-child,
.style-2-right-blog-details .single-blog-content .comments-form form label:first-child,
.style-2-left-blog-details .single-blog-content .comments-form form label:first-child{
    margin-top: 0px;
}
.style-2-full-blog-area .single-blog-content .comments-form form input,
.style-2-right-blog-details .single-blog-content .comments-form form input,
.style-2-left-blog-details .single-blog-content .comments-form form input{
    width: 100%;
    padding: 10px 15px;
    margin-top: 10px;
    border: 1px solid #eee;
    border-radius: 3px;
}
.style-2-full-blog-area .single-blog-content .comments-form form input:focus,
.style-2-right-blog-details .single-blog-content .comments-form form input:focus,
.style-2-left-blog-details .single-blog-content .comments-form form input:focus{
    border-color: #5fa800;
}
.style-2-full-blog-area .single-blog-content .comments-form form textarea,
.style-2-right-blog-details .single-blog-content .comments-form form textarea,
.style-2-left-blog-details .single-blog-content .comments-form form textarea{
    width: 100%;
    min-height: 100px;
    padding: 10px 15px;
    margin-top: 10px;
    border: 1px solid #eee;
    border-radius: 3px;
    resize: unset;
}
.style-2-full-blog-area .single-blog-content .comments-form form textarea:focus,
.style-2-right-blog-details .single-blog-content .comments-form form textarea:focus,
.style-2-left-blog-details .single-blog-content .comments-form form textarea:focus{
    border-color: #5fa800;
}
.style-2-full-blog-area .single-blog-content .comments-form a.btn-style1,
.style-2-right-blog-details .single-blog-content .comments-form a.btn-style1,
.style-2-left-blog-details .single-blog-content .comments-form a.btn-style1{
    margin-top: 24px;
    background-color: #5fa800;
    border-color: #5fa800;
}
.style-2-full-blog-area .single-blog-content .comments-form a.btn-style1:hover,
.style-2-right-blog-details .single-blog-content .comments-form a.btn-style1:hover,
.style-2-left-blog-details .single-blog-content .comments-form a.btn-style1:hover{
    background-color: transparent;
    color: #000;
    border-color: #5fa800;
}
/* center blog css */
.cetner-blog-style-2{
    display: flex;
    flex-wrap: wrap;
}
.cetner-blog-style-2 .blog-start{
    width: 100%;
    margin-top: 30px;
}
.cetner-blog-style-2 .blog-start:nth-child(1){
    margin-top: 0px;
}
.cetner-blog-style-2 .blog-start .blog-image a{
    display: block;
}
.cetner-blog-style-2 .blog-start .blog-content{
    padding-top: 30px;
}
.cetner-blog-style-2 .blog-start .blog-content .blog-date-comment{
    display: flex;
    justify-content: space-between;
}
.cetner-blog-style-2 .blog-start .blog-content .blog-date-comment span.blog-date,
.cetner-blog-style-2 .blog-start .blog-content .blog-date-comment a{
    display: flex;
    align-items: center;
    line-height: 1;
}
.cetner-blog-style-2 .blog-start .blog-content .blog-date-comment span.blog-date i,
.cetner-blog-style-2 .blog-start .blog-content .blog-date-comment a i{
    margin-right: 5px;
}
.cetner-blog-style-2 .blog-start .blog-content .blog-title h6{
    font-size: 16px;
    margin-top: 20px;
    padding-top: 13px;
    border-top: 1px solid #ededed;
}
.cetner-blog-style-2 .blog-start .blog-content .blog-title h6 a{
    display: block;
    width: 100%;
    white-space: nowrap;
    text-overflow: ellipsis;
    overflow: hidden;
}
.cetner-blog-style-2 .blog-start .blog-content .blog-title h6 a:hover{
    color: #5fa800;
}
.cetner-blog-style-2 .blog-start .blog-content p.blog-description{
    margin-top: 16px;
}
.cetner-blog-style-2 .blog-start .blog-content .more-blog{
    margin-top: 17px;
    display: flex;
    align-items: center;
    justify-content: space-between;
}
.cetner-blog-style-2 .blog-start .blog-content .more-blog a.read-link{
    color: #5fa800;
    font-size: 14px;
    font-weight: 600;
    display: flex;
    align-items: center;
}
.cetner-blog-style-2 .blog-start .blog-content .more-blog a.read-link i{
    font-size: 12px;
    padding-left: 5px;
}
.cetner-blog-style-2 .blog-start .blog-content .more-blog a.read-link i,
.cetner-blog-style-2 .blog-start .blog-content .more-blog a.read-link:hover i{
    -webkit-transition: all 0.3s ease-in-out 0s;
    -o-transition: all 0.3s ease-in-out 0s;
    transition: all 0.3s ease-in-out 0s;
}
.cetner-blog-style-2 .blog-start .blog-content .more-blog a.read-link:hover i{
    margin-left: 8px;
}
.cetner-blog-style-2 .blog-start .blog-content .more-blog span.blog-admin span.blog-editor{
    font-weight: 600;
}
/* blog style-3 section title */
.section-title3 h2{
    font-size: 20px;
    text-align: center;
    margin-bottom: 20px;
    font-weight: bold;
}
.section-title3 h2 span{
    position: relative;
}
.section-title3 h2 span::before,
.section-title3 h2 span::after{
    background-color: #5fa800;
    content: "";
    position: absolute;
    bottom: 50%;
    width: 10px;
    height: 2px;
}
.section-title3 h2 span::before{
    left: -25px;
}
.section-title3 h2 span::after{
    right: -25px;
}
/* style-3 blog css */
.full-blog-style-3,
.blog-style-3-left-grid,
.blog-style-3-right-grid{
    display: flex;
    flex-wrap: wrap;
    margin-left: -30px;
    margin-top: -30px;
}
.full-blog-style-3 .blog-start,
.blog-style-3-left-grid .blog-start,
.blog-style-3-right-grid .blog-start{
    width: calc(33.33% - 30px);
    margin-left: 30px;
    margin-top: 30px;
}
.full-blog-style-3 .blog-start .blog-image,
.blog-style-3-left-grid .blog-start .blog-image,
.blog-style-3-right-grid .blog-start .blog-image{
    display: flex;
}
.full-blog-style-3 .blog-start .blog-image a::after,
.blog-style-3-left-grid .blog-start .blog-image a::after,
.blog-style-3-right-grid .blog-start .blog-image a::after{
    background-color: rgba(0, 0, 0, 0.5);
    content: "";
    position: absolute;
    top: 0px;
    bottom: 0px;
    right: 0px;
    left: 0px;
    opacity: 0;
    -webkit-transition: all 0.6s ease-in-out 0s;
    -o-transition: all 0.6s ease-in-out 0s;
    transition: all 0.6s ease-in-out 0s;
}
.full-blog-style-3 .blog-start:hover .blog-image a::after,
.blog-style-3-left-grid .blog-start:hover .blog-image a::after,
.blog-style-3-right-grid .blog-start:hover .blog-image a::after{
    opacity: 1;
    -webkit-transition: all 0.6s ease-in-out 0s;
    -o-transition: all 0.6s ease-in-out 0s;
    transition: all 0.6s ease-in-out 0s;
}
.full-blog-style-3 .blog-start .blog-image a,
.blog-style-3-left-grid .blog-start .blog-image a,
.blog-style-3-right-grid .blog-start .blog-image a{
    position: relative;
    overflow: hidden;
}
.full-blog-style-3 .blog-start .blog-image a img,
.blog-style-3-left-grid .blog-start .blog-image a img,
.blog-style-3-right-grid .blog-start .blog-image a img {
    backface-visibility: hidden;
}
.full-blog-style-3 .blog-start:hover .blog-image a img,
.blog-style-3-left-grid .blog-start:hover .blog-image a img,
.blog-style-3-right-grid .blog-start:hover .blog-image a img{
    -webkit-transform: scale(1.1);
    -o-transform: scale(1.1);
    transform: scale(1.1);
}
.full-blog-style-3 .blog-start .blog-image a img,
.full-blog-style-3 .blog-start:hover .blog-image a img,
.blog-style-3-left-grid .blog-start .blog-image a img,
.blog-style-3-left-grid .blog-start:hover .blog-image a img,
.blog-style-3-right-grid .blog-start .blog-image a img,
.blog-style-3-right-grid .blog-start:hover .blog-image a img{
    -webkit-transition: all 0.6s ease-in-out 0s;
    -o-transition: all 0.6s ease-in-out 0s;
    transition: all 0.6s ease-in-out 0s;
}
.full-blog-style-3 .blog-start .blog-content,
.blog-style-3-left-grid .blog-start .blog-content,
.blog-style-3-right-grid .blog-start .blog-content{
    padding-top: 30px;
}
.full-blog-style-3 .blog-start .blog-content .blog-date-comment,
.blog-style-3-left-grid .blog-start .blog-content .blog-date-comment,
.blog-style-3-right-grid .blog-start .blog-content .blog-date-comment{
    display: flex;
    justify-content: space-between;
}
.full-blog-style-3 .blog-start .blog-content .blog-date-comment a,
.blog-style-3-left-grid .blog-start .blog-content .blog-date-comment a,
.blog-style-3-right-grid .blog-start .blog-content .blog-date-comment a,
.full-blog-style-3 .blog-start .blog-content .blog-date-comment span.blog-date,
.blog-style-3-left-grid .blog-start .blog-content .blog-date-comment span.blog-date,
.blog-style-3-right-grid .blog-start .blog-content .blog-date-comment span.blog-date{
    display: flex;
    align-items: center;
    line-height: 1;
}
.full-blog-style-3 .blog-start .blog-content .blog-date-comment a:hover,
.blog-style-3-left-grid .blog-start .blog-content .blog-date-comment a:hover,
.blog-style-3-right-grid .blog-start .blog-content .blog-date-comment a:hover{
    color: #5fa800;
}
.full-blog-style-3 .blog-start .blog-content .blog-date-comment a i,
.blog-style-3-left-grid .blog-start .blog-content .blog-date-comment a i,
.blog-style-3-right-grid .blog-start .blog-content .blog-date-comment a i,
.full-blog-style-3 .blog-start .blog-content .blog-date-comment span.blog-date i,
.blog-style-3-left-grid .blog-start .blog-content .blog-date-comment span.blog-date i,
.blog-style-3-right-grid .blog-start .blog-content .blog-date-comment span.blog-date i{
    margin-right: 5px;
}
.full-blog-style-3 .blog-start .blog-content .blog-title h6,
.blog-style-3-left-grid .blog-start .blog-content .blog-title h6,
.blog-style-3-right-grid .blog-start .blog-content .blog-title h6{
    font-size: 16px;
    margin-top: 20px;
    padding-top: 13px;
    border-top: 1px solid #ededed;
}
.full-blog-style-3 .blog-start .blog-content .blog-title h6 a,
.blog-style-3-left-grid .blog-start .blog-content .blog-title h6 a,
.blog-style-3-right-grid .blog-start .blog-content .blog-title h6 a{
    display: block;
    width: 100%;
    white-space: nowrap;
    text-overflow: ellipsis;
    overflow: hidden;
}
.full-blog-style-3 .blog-start .blog-content .blog-title h6 a:hover,
.blog-style-3-left-grid .blog-start .blog-content .blog-title h6 a:hover,
.blog-style-3-right-grid .blog-start .blog-content .blog-title h6 a:hover{
    color: #5fa800;
}
.full-blog-style-3 .blog-start .blog-content p.blog-description,
.blog-style-3-left-grid .blog-start .blog-content p.blog-description,
.blog-style-3-right-grid .blog-start .blog-content p.blog-description{
    color: #999;
    margin-top: 16px;
    font-size: 14px;
}
.full-blog-style-3 .blog-start .blog-content .more-blog,
.blog-style-3-left-grid .blog-start .blog-content .more-blog,
.blog-style-3-right-grid .blog-start .blog-content .more-blog{
    margin-top: 17px;
    display: flex;
    align-items: center;
    justify-content: space-between;
}
.full-blog-style-3 .blog-start .blog-content .more-blog a.read-link,
.blog-style-3-left-grid .blog-start .blog-content .more-blog a.read-link,
.blog-style-3-right-grid .blog-start .blog-content .more-blog a.read-link{
    color: #5fa800;
    font-size: 14px;
}
.full-blog-style-3 .blog-start .blog-content .more-blog a.read-link i,
.blog-style-3-left-grid .blog-start .blog-content .more-blog a.read-link i,
.blog-style-3-right-grid .blog-start .blog-content .more-blog a.read-link i{
    font-size: 12px;
}
.full-blog-style-3 .blog-start .blog-content .more-blog a.read-link i,
.full-blog-style-3 .blog-start .blog-content .more-blog a.read-link:hover i,
.blog-style-3-left-grid .blog-start .blog-content .more-blog a.read-link i,
.blog-style-3-left-grid .blog-start .blog-content .more-blog a.read-link:hover i,
.blog-style-3-right-grid .blog-start .blog-content .more-blog a.read-link i,
.blog-style-3-right-grid .blog-start .blog-content .more-blog a.read-link:hover i{
    -webkit-transition: all 0.3s ease-in-out 0s;
    -o-transition: all 0.3s ease-in-out 0s;
    transition: all 0.3s ease-in-out 0s;
}
.full-blog-style-3 .blog-start .blog-content .more-blog a.read-link:hover i,
.blog-style-3-left-grid .blog-start .blog-content .more-blog a.read-link:hover i,
.blog-style-3-right-grid .blog-start .blog-content .more-blog a.read-link:hover i{
    margin-left: 8px;
}
.full-blog-style-3 .blog-start .blog-content .more-blog span.blog-admin span.blog-editor,
.blog-style-3-left-grid .blog-start .blog-content .more-blog span.blog-admin span.blog-editor,
.blog-style-3-right-grid .blog-start .blog-content .more-blog span.blog-admin span.blog-editor{
    font-weight: 600;
}
.all-page span.page-title{
    color: #333;
    display: block;
    text-align: center;
    margin-top: 30px;
    font-weight: 500;
}
/* full-left-right list blog css */
.full-blog-list-style-3,
.left-blog-list-style-3,
.right-blog-style-3{
    display: flex;
    flex-wrap: wrap;
}
.full-blog-list-style-3 .blog-start,
.left-blog-list-style-3 .blog-start,
.right-blog-style-3 .blog-start{
    width: 100%;
    margin-bottom: 20px;
    padding-bottom: 20px;
    border-bottom: 1px solid #eee;
    display: flex;
    align-items: center;
}
.full-blog-list-style-3 .blog-start .blog-image,
.left-blog-list-style-3 .blog-start .blog-image,
.right-blog-style-3 .blog-start .blog-image{
    display: flex;
    width: 50%;
}
.full-blog-list-style-3 .blog-start .blog-image a::after,
.left-blog-list-style-3 .blog-start .blog-image a::after,
.right-blog-style-3 .blog-start .blog-image a::after{
    background-color: rgba(0, 0, 0, 0.5);
    content: "";
    position: absolute;
    top: 0px;
    bottom: 0px;
    right: 0px;
    left: 0px;
    opacity: 0;
    -webkit-transition: all 0.6s ease-in-out 0s;
    -o-transition: all 0.6s ease-in-out 0s;
    transition: all 0.6s ease-in-out 0s;
}
.full-blog-list-style-3 .blog-start:hover .blog-image a::after,
.left-blog-list-style-3 .blog-start:hover .blog-image a::after,
.right-blog-style-3 .blog-start:hover .blog-image a::after{
    opacity: 1;
    -webkit-transition: all 0.6s ease-in-out 0s;
    -o-transition: all 0.6s ease-in-out 0s;
    transition: all 0.6s ease-in-out 0s;
}
.full-blog-list-style-3 .blog-start .blog-image a,
.left-blog-list-style-3 .blog-start .blog-image a,
.right-blog-style-3 .blog-start .blog-image a{
    position: relative;
    overflow: hidden;
}
.full-blog-list-style-3 .blog-start .blog-image a img,
.left-blog-list-style-3 .blog-start .blog-image a img,
.right-blog-style-3 .blog-start .blog-image a img {
    backface-visibility: hidden;
}
.full-blog-list-style-3 .blog-start:hover .blog-image a img,
.left-blog-list-style-3 .blog-start:hover .blog-image a img,
.right-blog-style-3 .blog-start:hover .blog-image a img{
    -webkit-transform: scale(1.1);
    -o-transform: scale(1.1);
    transform: scale(1.1);
}
.full-blog-list-style-3 .blog-start .blog-image a img,
.full-blog-list-style-3 .blog-start:hover .blog-image a img,
.left-blog-list-style-3 .blog-start .blog-image a img,
.left-blog-list-style-3 .blog-start:hover .blog-image a img,
.right-blog-style-3 .blog-start .blog-image a img,
.right-blog-style-3 .blog-start:hover .blog-image a img{
    -webkit-transition: all 0.6s ease-in-out 0s;
    -o-transition: all 0.6s ease-in-out 0s;
    transition: all 0.6s ease-in-out 0s;
}
.full-blog-list-style-3 .blog-start .blog-content,
.left-blog-list-style-3 .blog-start .blog-content,
.right-blog-style-3 .blog-start .blog-content{
    width: calc(50% - 30px);
    margin-left: 30px;
}
.full-blog-list-style-3 .blog-start .blog-content .blog-date-comment,
.left-blog-list-style-3 .blog-start .blog-content .blog-date-comment,
.right-blog-style-3 .blog-start .blog-content .blog-date-comment{
    display: flex;
    justify-content: space-between;
}
.full-blog-list-style-3 .blog-start .blog-content .blog-date-comment a,
.left-blog-list-style-3 .blog-start .blog-content .blog-date-comment a,
.right-blog-style-3 .blog-start .blog-content .blog-date-comment a,
.full-blog-list-style-3 .blog-start .blog-content .blog-date-comment span.blog-date,
.left-blog-list-style-3 .blog-start .blog-content .blog-date-comment span.blog-date,
.right-blog-style-3 .blog-start .blog-content .blog-date-comment span.blog-date{
    display: flex;
    align-items: center;
    line-height: 1;
}
.full-blog-list-style-3 .blog-start .blog-content .blog-date-comment a:hover,
.left-blog-list-style-3 .blog-start .blog-content .blog-date-comment a:hover,
.right-blog-style-3 .blog-start .blog-content .blog-date-comment a:hover{
    color: #5fa800;
}
.full-blog-list-style-3 .blog-start .blog-content .blog-date-comment a i,
.left-blog-list-style-3 .blog-start .blog-content .blog-date-comment a i,
.right-blog-style-3 .blog-start .blog-content .blog-date-comment a i,
.full-blog-list-style-3 .blog-start .blog-content .blog-date-comment span.blog-date i,
.left-blog-list-style-3 .blog-start .blog-content .blog-date-comment span.blog-date i,
.right-blog-style-3 .blog-start .blog-content .blog-date-comment span.blog-date i{
    margin-right: 5px;
}
.full-blog-list-style-3 .blog-start .blog-content .blog-title h6,
.left-blog-list-style-3 .blog-start .blog-content .blog-title h6,
.right-blog-style-3 .blog-start .blog-content .blog-title h6{
    font-size: 16px;
    margin-top: 30px;
    padding-top: 24px;
    border-top: 1px solid #ededed;
}
.full-blog-list-style-3 .blog-start .blog-content .blog-title h6 a,
.left-blog-list-style-3 .blog-start .blog-content .blog-title h6 a,
.right-blog-style-3 .blog-start .blog-content .blog-title h6 a{
    display: block;
    width: 100%;
    white-space: nowrap;
    text-overflow: ellipsis;
    overflow: hidden;
}
.full-blog-list-style-3 .blog-start .blog-content .blog-title h6 a:hover,
.left-blog-list-style-3 .blog-start .blog-content .blog-title h6 a:hover,
.right-blog-style-3 .blog-start .blog-content .blog-title h6 a:hover{
    color: #5fa800;
}
.full-blog-list-style-3 .blog-start .blog-content p.blog-description,
.left-blog-list-style-3 .blog-start .blog-content p.blog-description,
.right-blog-style-3 .blog-start .blog-content p.blog-description{
    color: #999;
    margin-top: 17px;
    font-size: 14px;
}
.full-blog-list-style-3 .blog-start .blog-content .more-blog,
.left-blog-list-style-3 .blog-start .blog-content .more-blog,
.right-blog-style-3 .blog-start .blog-content .more-blog{
    margin-top: 17px;
    display: flex;
    align-items: center;
    justify-content: space-between;
}
.full-blog-list-style-3 .blog-start .blog-content .more-blog a.read-link,
.left-blog-list-style-3 .blog-start .blog-content .more-blog a.read-link,
.right-blog-style-3 .blog-start .blog-content .more-blog a.read-link{
    color: #5fa800;
    font-size: 14px;
    display: flex;
    align-items: center;
}
.full-blog-list-style-3 .blog-start .blog-content .more-blog a.read-link i,
.left-blog-list-style-3 .blog-start .blog-content .more-blog a.read-link i,
.right-blog-style-3 .blog-start .blog-content .more-blog a.read-link i{
    font-size: 12px;
    padding-left: 5px;
}
.full-blog-list-style-3 .blog-start .blog-content .more-blog a.read-link i,
.full-blog-list-style-3 .blog-start .blog-content .more-blog a.read-link:hover i,
.left-blog-list-style-3 .blog-start .blog-content .more-blog a.read-link i,
.left-blog-list-style-3 .blog-start .blog-content .more-blog a.read-link:hover i,
.right-blog-style-3 .blog-start .blog-content .more-blog a.read-link i,
.right-blog-style-3 .blog-start .blog-content .more-blog a.read-link:hover i{
    -webkit-transition: all 0.3s ease-in-out 0s;
    -o-transition: all 0.3s ease-in-out 0s;
    transition: all 0.3s ease-in-out 0s;
}
.full-blog-list-style-3 .blog-start .blog-content .more-blog a.read-link:hover i,
.left-blog-list-style-3 .blog-start .blog-content .more-blog a.read-link:hover i,
.right-blog-style-3 .blog-start .blog-content .more-blog a.read-link:hover i{
    margin-left: 8px;
}
.full-blog-list-style-3 .blog-start .blog-content .more-blog span.blog-admin span.blog-editor,
.left-blog-list-style-3 .blog-start .blog-content .more-blog span.blog-admin span.blog-editor,
.right-blog-style-3 .blog-start .blog-content .more-blog span.blog-admin span.blog-editor{
    font-weight: 600;
}
/* style 3 full-left-right blog details css */
/* carousel dots css */
.blog-page .full-blog-details .single-image-carousel,
.left-blog-details .single-image-carousel,
.right-blog-details .single-image-carousel{
    position: relative;
}
.blog-page .full-blog-details .single-image-carousel .owl-dots,
.left-blog-details .single-image-carousel .owl-dots,
.right-blog-details .single-image-carousel .owl-dots{
    position: absolute;
    bottom: 10px;
    right: 0px;
    left: 0px;
    margin-top: 0px;
}
.blog-page .full-blog-details .single-image-carousel .owl-dots button.owl-dot span,
.left-blog-details .single-image-carousel .owl-dots button.owl-dot span,
.right-blog-details .single-image-carousel .owl-dots button.owl-dot span{
    background-color: #5fa800;
    width: 7px;
    height: 7px;
}
.blog-page .full-blog-details .single-image-carousel .owl-dots button.owl-dot :hover,
.blog-page .full-blog-details .single-image-carousel .owl-dots button.owl-dot.active span,
.left-blog-details .single-image-carousel .owl-dots button.owl-dot :hover,
.left-blog-details .single-image-carousel .owl-dots button.owl-dot.active span,
.right-blog-details .single-image-carousel .owl-dots button.owl-dot :hover,
.right-blog-details .single-image-carousel .owl-dots button.owl-dot.active span{
    background-color: #000;
}
.full-blog-details .single-image-carousel .items,
.left-blog-details .single-image-carousel .items,
.right-blog-details .single-image-carousel .items{
    display: flex;
}
.full-blog-details .single-image-carousel a::after,
.left-blog-details .single-image-carousel a::after,
.right-blog-details .single-image-carousel a::after{
    background-color: rgba(0, 0, 0, 0.5);
    content: "";
    position: absolute;
    top: 0px;
    bottom: 0px;
    right: 0px;
    left: 0px;
    opacity: 0;
    -webkit-transition: all 0.6s ease-in-out 0s;
    -o-transition: all 0.6s ease-in-out 0s;
    transition: all 0.6s ease-in-out 0s;
}
.full-blog-details .single-image-carousel:hover a::after,
.left-blog-details .single-image-carousel:hover a::after,
.right-blog-details .single-image-carousel:hover a::after{
    opacity: 1;
    -webkit-transition: all 0.6s ease-in-out 0s;
    -o-transition: all 0.6s ease-in-out 0s;
    transition: all 0.6s ease-in-out 0s;
}
.full-blog-details .single-image-carousel a,
.left-blog-details .single-image-carousel a,
.right-blog-details .single-image-carousel a{
    position: relative;
    overflow: hidden;
}
.full-blog-details .single-image-carousel:hover a img,
.left-blog-details .single-image-carousel:hover a img,
.right-blog-details .single-image-carousel:hover a img{
    -webkit-transform: scale(1.1);
    -o-transform: scale(1.1);
    transform: scale(1.1);
}
.full-blog-details .single-image-carousel a img,
.full-blog-details .single-image-carousel:hover a img,
.left-blog-details .single-image-carousel a img,
.left-blog-details .single-image-carousel:hover a img,
.right-blog-details .single-image-carousel a img,
.right-blog-details .single-image-carousel:hover a img{
    -webkit-transition: all 0.6s ease-in-out 0s;
    -o-transition: all 0.6s ease-in-out 0s;
    transition: all 0.6s ease-in-out 0s;
}
.full-blog-details .single-blog-content,
.left-blog-details .single-blog-content,
.right-blog-details .single-blog-content{
    margin-top: 33px;
}
.full-blog-details .single-blog-content .single-b-title h4,
.left-blog-details .single-blog-content .single-b-title h4,
.right-blog-details .single-blog-content .single-b-title h4{
    font-size: 16px;
}
.full-blog-details .single-blog-content .date-edit-comments,
.left-blog-details .single-blog-content .date-edit-comments,
.right-blog-details .single-blog-content .date-edit-comments {
    margin-top: 23px;
}
.full-blog-details .single-blog-content .date-edit-comments .blog-info-wrap,
.left-blog-details .single-blog-content .date-edit-comments .blog-info-wrap,
.right-blog-details .single-blog-content .date-edit-comments .blog-info-wrap{
    display: flex;
    flex-wrap: wrap;
    align-items: center;
    margin: -15px 0px 0px -30px;
}
.full-blog-details .single-blog-content .date-edit-comments .blog-info-wrap span.blog-data,
.left-blog-details .single-blog-content .date-edit-comments .blog-info-wrap span.blog-data,
.right-blog-details .single-blog-content .date-edit-comments .blog-info-wrap span.blog-data {
    margin: 15px 0px 0px 30px;
}
.full-blog-details .single-blog-content .date-edit-comments .blog-info-wrap span.date,
.left-blog-details .single-blog-content .date-edit-comments .blog-info-wrap span.date,
.right-blog-details .single-blog-content .date-edit-comments .blog-info-wrap span.date{
    display: flex;
    align-items: center;
    line-height: 1;
}
.full-blog-details .single-blog-content .date-edit-comments .blog-info-wrap span.date span.blog-d-n-c,
.left-blog-details .single-blog-content .date-edit-comments .blog-info-wrap span.date span.blog-d-n-c,
.right-blog-details .single-blog-content .date-edit-comments .blog-info-wrap span.date span.blog-d-n-c{
    margin-left: 5px;
}
.full-blog-details .single-blog-content .date-edit-comments .blog-info-wrap span.blog-edit,
.left-blog-details .single-blog-content .date-edit-comments .blog-info-wrap span.blog-edit,
.right-blog-details .single-blog-content .date-edit-comments .blog-info-wrap span.blog-edit{
    display: flex;
    align-items: center;
    line-height: 1;
}
.full-blog-details .single-blog-content .date-edit-comments .blog-info-wrap span.blog-edit span.blog-d-n-c,
.left-blog-details .single-blog-content .date-edit-comments .blog-info-wrap span.blog-edit span.blog-d-n-c,
.right-blog-details .single-blog-content .date-edit-comments .blog-info-wrap span.blog-edit span.blog-d-n-c{
    margin-left: 5px;
}
.full-blog-details .single-blog-content .date-edit-comments .blog-info-wrap span.comments,
.left-blog-details .single-blog-content .date-edit-comments .blog-info-wrap span.comments,
.right-blog-details .single-blog-content .date-edit-comments .blog-info-wrap span.comments{
    display: flex;
    align-items: center;
    line-height: 1;
}
.full-blog-details .single-blog-content .date-edit-comments .blog-info-wrap span.comments span.blog-d-n-c,
.left-blog-details .single-blog-content .date-edit-comments .blog-info-wrap span.comments span.blog-d-n-c,
.right-blog-details .single-blog-content .date-edit-comments .blog-info-wrap span.comments span.blog-d-n-c{
    margin-left: 5px;
}
.full-blog-details .single-blog-content .blog-description,
.left-blog-details .single-blog-content .blog-description,
.right-blog-details .single-blog-content .blog-description{
    margin-top: 23px;
}
.full-blog-details .single-blog-content .blog-description p,
.left-blog-details .single-blog-content .blog-description p,
.right-blog-details .single-blog-content .blog-description p{
    margin-top: 1px;
}
.full-blog-details .single-blog-content .blog-description .blog-image-description,
.left-blog-details .single-blog-content .blog-description .blog-image-description,
.right-blog-details .single-blog-content .blog-description .blog-image-description{
    margin-top: 22px;
}
.full-blog-details .single-blog-content .blog-description .blog-image-description img,
.left-blog-details .single-blog-content .blog-description .blog-image-description img,
.right-blog-details .single-blog-content .blog-description .blog-image-description img{
    float: left;
    margin-right: 30px;
}
.full-blog-details .single-blog-content .blog-description .blog-image-description p.bold-description,
.left-blog-details .single-blog-content .blog-description .blog-image-description p.bold-description,
.right-blog-details .single-blog-content .blog-description .blog-image-description p.bold-description{
    font-size: 15px;
    font-weight: 600;
}
.full-blog-details .single-blog-content .blog-description p.color-description,
.left-blog-details .single-blog-content .blog-description p.color-description,
.right-blog-details .single-blog-content .blog-description p.color-description{
    background-color: #f7f7f7;
    font-size: 16px;
    color: #5fa800;
    margin: 22px 0px;
    padding: 30px;
    border-left: 1px solid #eee;
}
.full-blog-details .single-blog-content .blog-info,
.left-blog-details .single-blog-content .blog-info,
.right-blog-details .single-blog-content .blog-info{
    padding: 30px;
    margin-top: 22px;
    background-color: #f7f7f7;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-direction: column;
}
.full-blog-details .single-blog-content .blog-info i.fa-quote-left,
.left-blog-details .single-blog-content .blog-info i.fa-quote-left,
.right-blog-details .single-blog-content .blog-info i.fa-quote-left{
    font-size: 30px;
    color: #5fa800;
}
.full-blog-details .single-blog-content .blog-info h6,
.left-blog-details .single-blog-content .blog-info h6,
.right-blog-details .single-blog-content .blog-info h6 {
    color: #5fa800;
    font-size: 16px;
    margin-top: 9px;
    font-weight: 600;
}
.full-blog-details .single-blog-content .blog-comments h4,
.left-blog-details .single-blog-content .blog-comments h4,
.right-blog-details .single-blog-content .blog-comments h4{
    font-size: 18px;
}
.full-blog-details .single-blog-content .blog-comments h4 span,
.left-blog-details .single-blog-content .blog-comments h4 span,
.right-blog-details .single-blog-content .blog-comments h4 span{
    color: #5fa800;
}
.full-blog-details .single-blog-content .blog-comments .blog-comment-info,
.left-blog-details .single-blog-content .blog-comments .blog-comment-info,
.right-blog-details .single-blog-content .blog-comments .blog-comment-info {
    margin-top: 22px;
    padding-top: 30px;
    border-top: 1px solid #eee;
}
.full-blog-details .single-blog-content .blog-comments .blog-comment-info ul.comments-arae,
.left-blog-details .single-blog-content .blog-comments .blog-comment-info ul.comments-arae,
.right-blog-details .single-blog-content .blog-comments .blog-comment-info ul.comments-arae {
    display: flex;
    margin-top: 24px;
}
.full-blog-details .single-blog-content .blog-comments .blog-comment-info ul.comments-arae.comment-reply,
.left-blog-details .single-blog-content .blog-comments .blog-comment-info ul.comments-arae.comment-reply,
.right-blog-details .single-blog-content .blog-comments .blog-comment-info ul.comments-arae.comment-reply {
    padding-left: 50px;
}
.full-blog-details .single-blog-content .blog-comments .blog-comment-info ul.comments-arae.all-reply,
.left-blog-details .single-blog-content .blog-comments .blog-comment-info ul.comments-arae.all-reply,
.right-blog-details .single-blog-content .blog-comments .blog-comment-info ul.comments-arae.all-reply {
    margin-top: 24px;
    padding-top: 30px;
    border-top: 1px solid #eee;
}
.full-blog-details .single-blog-content .blog-comments .blog-comment-info ul.comments-arae:first-of-type,
.left-blog-details .single-blog-content .blog-comments .blog-comment-info ul.comments-arae:first-of-type,
.right-blog-details .single-blog-content .blog-comments .blog-comment-info ul.comments-arae:first-of-type {
    margin-top: 0px;
}
.full-blog-details .single-blog-content .blog-comments .blog-comment-info ul.comments-arae li.comments-man,
.left-blog-details .single-blog-content .blog-comments .blog-comment-info ul.comments-arae li.comments-man,
.right-blog-details .single-blog-content .blog-comments .blog-comment-info ul.comments-arae li.comments-man{
    width: 45px;
    height: 45px;
    background-color: #5fa800;
    color: #fff;
    margin-right: 15px;
    font-size: 15px;
    border-radius: 3px;
    display: flex;
    align-items: center;
    text-align: center;
    justify-content: center;
    font-weight: 600;
}
.full-blog-details .single-blog-content .blog-comments .blog-comment-info ul.comments-arae li.comments-content,
.left-blog-details .single-blog-content .blog-comments .blog-comment-info ul.comments-arae li.comments-content,
.right-blog-details .single-blog-content .blog-comments .blog-comment-info ul.comments-arae li.comments-content {
    width: calc(100% - 45px);
}
.full-blog-details .single-blog-content .blog-comments .blog-comment-info ul.comments-arae li.comments-content span.comments-result,
.left-blog-details .single-blog-content .blog-comments .blog-comment-info ul.comments-arae li.comments-content span.comments-result,
.right-blog-details .single-blog-content .blog-comments .blog-comment-info ul.comments-arae li.comments-content span.comments-result{
    display: block;
}
.full-blog-details .single-blog-content .blog-comments .blog-comment-info ul.comments-arae li.comments-content span.comment-name,
.left-blog-details .single-blog-content .blog-comments .blog-comment-info ul.comments-arae li.comments-content span.comment-name,
.right-blog-details .single-blog-content .blog-comments .blog-comment-info ul.comments-arae li.comments-content span.comment-name{
    margin: 5px 0px;
}
.full-blog-details .single-blog-content .blog-comments .blog-comment-info ul.comments-arae li.comments-content span.comment-name i,
.left-blog-details .single-blog-content .blog-comments .blog-comment-info ul.comments-arae li.comments-content span.comment-name i,
.right-blog-details .single-blog-content .blog-comments .blog-comment-info ul.comments-arae li.comments-content span.comment-name i {
    font-style: normal;
}
.full-blog-details .single-blog-content .blog-comments .blog-comment-info ul.comments-arae li.comments-content span.comments-result.c-date,
.left-blog-details .single-blog-content .blog-comments .blog-comment-info ul.comments-arae li.comments-content span.comments-result.c-date,
.right-blog-details .single-blog-content .blog-comments .blog-comment-info ul.comments-arae li.comments-content span.comments-result.c-date{
    font-weight: 600;
}
.full-blog-details .single-blog-content .blog-comments .blog-comment-info ul.comments-arae li.comments-content span.comments-result.c-date a.Reply,
.left-blog-details .single-blog-content .blog-comments .blog-comment-info ul.comments-arae li.comments-content span.comments-result.c-date a.Reply,
.right-blog-details .single-blog-content .blog-comments .blog-comment-info ul.comments-arae li.comments-content span.comments-result.c-date a.Reply{
    margin-left: 30px;
    color: #5fa800;
    font-weight: 500;
}
.full-blog-details .single-blog-content .blog-comments .blog-comment-info ul.comments-arae li.comments-content span span.comments-title,
.left-blog-details .single-blog-content .blog-comments .blog-comment-info ul.comments-arae li.comments-content span span.comments-title,
.right-blog-details .single-blog-content .blog-comments .blog-comment-info ul.comments-arae li.comments-content span span.comments-title{
    font-weight: 600;
    color: #5fa800;
}
.full-blog-details .single-blog-content .b-link,
.left-blog-details .single-blog-content .b-link,
.right-blog-details .single-blog-content .b-link{
    margin-top: 30px;
}
.full-blog-details .single-blog-content .b-link a,
.left-blog-details .single-blog-content .b-link a,
.right-blog-details .single-blog-content .b-link a{
    background-color: #5fa800;
    padding: 5px 10px;
    color: #fff;
    border-radius: 4px;
}
.full-blog-details .single-blog-content .b-link a:hover,
.left-blog-details .single-blog-content .b-link a:hover,
.right-blog-details .single-blog-content .b-link a:hover{
    background-color: #000;
    color: #fff;
}
.full-blog-details .single-blog-content .blog-social,
.left-blog-details .single-blog-content .blog-social,
.right-blog-details .single-blog-content .blog-social{
    margin-top: 30px;
    display: flex;
    align-items: center;
    justify-content: flex-start;
}
.full-blog-details .single-blog-content .blog-social a.facebook,
.full-blog-details .single-blog-content .blog-social a.twitter,
.full-blog-details .single-blog-content .blog-social a.insta,
.full-blog-details .single-blog-content .blog-social a.pinterest,
.left-blog-details .single-blog-content .blog-social a.facebook,
.left-blog-details .single-blog-content .blog-social a.twitter,
.left-blog-details .single-blog-content .blog-social a.insta,
.left-blog-details .single-blog-content .blog-social a.pinterest,
.right-blog-details .single-blog-content .blog-social a.facebook,
.right-blog-details .single-blog-content .blog-social a.twitter,
.right-blog-details .single-blog-content .blog-social a.insta,
.right-blog-details .single-blog-content .blog-social a.pinterest{
    width: 30px;
    height: 30px;
    margin-right: 7px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50px;
}
.full-blog-details .single-blog-content .blog-social a.facebook,
.left-blog-details .single-blog-content .blog-social a.facebook,
.right-blog-details .single-blog-content .blog-social a.facebook{
    background-color: #3b5999;
    color: #fff;
}
.full-blog-details .single-blog-content .blog-social a.twitter,
.left-blog-details .single-blog-content .blog-social a.twitter,
.right-blog-details .single-blog-content .blog-social a.twitter{
    background-color: #55acee;
    color: #fff;
}
.full-blog-details .single-blog-content .blog-social a.insta,
.left-blog-details .single-blog-content .blog-social a.insta,
.right-blog-details .single-blog-content .blog-social a.insta{
    background-color: #dd4b39;
    color: #fff;
}
.full-blog-details .single-blog-content .blog-social a.pinterest,
.left-blog-details .single-blog-content .blog-social a.pinterest,
.right-blog-details .single-blog-content .blog-social a.pinterest{
    background-color: #bd081c;
    color: #fff;
}
.full-blog-details .single-blog-content .blog-social a:hover,
.left-blog-details .single-blog-content .blog-social a:hover,
.right-blog-details .single-blog-content .blog-social a:hover{
    background-color: #5fa800;
}
.full-blog-details .single-blog-content .blog-comments,
.left-blog-details .single-blog-content .blog-comments,
.right-blog-details .single-blog-content .blog-comments{
    margin-top: 23px;
}
.full-blog-details .single-blog-content .blog-comments h4,
.left-blog-details .single-blog-content .blog-comments h4,
.right-blog-details .single-blog-content .blog-comments h4{
    font-size: 20px;
}
.full-blog-details .single-blog-content .blog-comments h4 span,
.left-blog-details .single-blog-content .blog-comments h4 span,
.right-blog-details .single-blog-content .blog-comments h4 span{
    color: #5fa800;
}
.full-blog-details .single-blog-content .comments-form,
.left-blog-details .single-blog-content .comments-form,
.right-blog-details .single-blog-content .comments-form{
    margin-top: 20px;
}
.full-blog-details .single-blog-content .comments-form h4,
.left-blog-details .single-blog-content .comments-form h4,
.right-blog-details .single-blog-content .comments-form h4{
    font-size: 18px;
}
.full-blog-details .single-blog-content .comments-form form label,
.left-blog-details .single-blog-content .comments-form form label,
.right-blog-details .single-blog-content .comments-form form label{
    margin-top: 15px;
    margin-bottom: 5px;
}
.full-blog-details .single-blog-content .comments-form form input,
.left-blog-details .single-blog-content .comments-form form input,
.right-blog-details .single-blog-content .comments-form form input{
    width: 100%;
    padding: 10px 15px;
    border: 1px solid #eee;
    border-radius: 3px;
}
.full-blog-details .single-blog-content .comments-form form input:focus,
.left-blog-details .single-blog-content .comments-form form input:focus,
.right-blog-details .single-blog-content .comments-form form input:focus{
    border-color: #5fa800;
}
.full-blog-details .single-blog-content .comments-form form textarea,
.left-blog-details .single-blog-content .comments-form form textarea,
.right-blog-details .single-blog-content .comments-form form textarea{
    width: 100%;
    min-height: 100px;
    padding: 10px 15px;
    border: 1px solid #eee;
    border-radius: 3px;
    resize: unset;
}
.full-blog-details .single-blog-content .comments-form form textarea:focus,
.left-blog-details .single-blog-content .comments-form form textarea:focus,
.right-blog-details .single-blog-content .comments-form form textarea:focus{
    border-color: #5fa800;
}
.full-blog-details .single-blog-content .comments-form a.btn-style1,
.left-blog-details .single-blog-content .comments-form a.btn-style1,
.right-blog-details .single-blog-content .comments-form a.btn-style1{
    margin-top: 15px;
}
/* Blog css */
.details-blog-carousel .blog-start .blog-image{
    display: flex;
}
.details-blog-carousel .blog-start .blog-image a::after{
    background-color: rgba(0, 0, 0, 0.5);
    content: "";
    position: absolute;
    top: 0px;
    bottom: 0px;
    right: 0px;
    left: 0px;
    opacity: 0;
    -webkit-transition: all 0.6s ease-in-out 0s;
    -o-transition: all 0.6s ease-in-out 0s;
    transition: all 0.6s ease-in-out 0s;
}
.details-blog-carousel .blog-start:hover .blog-image a::after{
    opacity: 1;
    -webkit-transition: all 0.6s ease-in-out 0s;
    -o-transition: all 0.6s ease-in-out 0s;
    transition: all 0.6s ease-in-out 0s;
}
.details-blog-carousel .blog-start .blog-image a{
    position: relative;
    overflow: hidden;
}
.details-blog-carousel .blog-start .blog-image a img {
    backface-visibility: hidden;
}
.details-blog-carousel .blog-start:hover .blog-image a img{
    -webkit-transform: scale(1.1);
    -o-transform: scale(1.1);
    transform: scale(1.1);
}
.details-blog-carousel .blog-start .blog-image a img,
.details-blog-carousel .blog-start:hover .blog-image a img{
    -webkit-transition: all 0.6s ease-in-out 0s;
    -o-transition: all 0.6s ease-in-out 0s;
    transition: all 0.6s ease-in-out 0s;
}
.details-blog-carousel .blog-start .blog-content{
    padding-top: 27px;
}
.details-blog-carousel .blog-start .blog-content .blog-date-comment{
    display: flex;
    justify-content: space-between;
}
.details-blog-carousel .blog-start .blog-content .blog-date-comment a{
    display: flex;
    align-items: center;
}
.details-blog-carousel .blog-start .blog-content .blog-date-comment a:hover{
    color: #5fa800;
}
.details-blog-carousel .blog-start .blog-content .blog-date-comment a i{
    margin-right: 5px;
}
.details-blog-carousel .blog-start .blog-content .blog-title h6{
    font-size: 16px;
    margin-top: 15px;
    padding-top: 13px;
    border-top: 1px solid #ededed;
}
.details-blog-carousel .blog-start .blog-content .blog-title h6 a{
    display: block;
    width: 100%;
    white-space: nowrap;
    text-overflow: ellipsis;
    overflow: hidden;
}
.details-blog-carousel .blog-start .blog-content .blog-title h6 a:hover{
    color: #5fa800;
}
.details-blog-carousel .blog-start .blog-content p.blog-description{
    color: #999;
    margin-top: 16px;
}
.details-blog-carousel .blog-start .blog-content .more-blog{
    margin-top: 15px;
    display: flex;
    align-items: center;
    justify-content: space-between;
}
.details-blog-carousel .blog-start .blog-content .more-blog a.read-link{
    color: #5fa800;
    font-size: 16px;
}
.details-blog-carousel .blog-start .blog-content .more-blog a.read-link i{
    font-size: 12px;
}
.details-blog-carousel .blog-start .blog-content .more-blog a.read-link i,
.details-blog-carousel .blog-start .blog-content .more-blog a.read-link:hover i{
    -webkit-transition: all 0.3s ease-in-out 0s;
    -o-transition: all 0.3s ease-in-out 0s;
    transition: all 0.3s ease-in-out 0s;
}
.details-blog-carousel .blog-start .blog-content .more-blog a.read-link:hover i{
    margin-left: 8px;
}
.details-blog-carousel .blog-start .blog-content .more-blog span.blog-admin span.blog-editor{
    font-weight: 600;
}
/* center blog css */
.cetner-blog-style-3{
    display: flex;
    flex-wrap: wrap;
}
.cetner-blog-style-3 .blog-start{
    width: 100%;
    margin-top: 30px;
}
.cetner-blog-style-3 .blog-start:nth-child(1){
    margin-top: 0px;
}
.cetner-blog-style-3 .blog-start .blog-image{
    display: flex;
}
.cetner-blog-style-3 .blog-start .blog-image a::after{
    background-color: rgba(0, 0, 0, 0.5);
    content: "";
    position: absolute;
    top: 0px;
    bottom: 0px;
    right: 0px;
    left: 0px;
    opacity: 0;
    -webkit-transition: all 0.6s ease-in-out 0s;
    -o-transition: all 0.6s ease-in-out 0s;
    transition: all 0.6s ease-in-out 0s;
}
.cetner-blog-style-3 .blog-start:hover .blog-image a::after{
    opacity: 1;
    -webkit-transition: all 0.6s ease-in-out 0s;
    -o-transition: all 0.6s ease-in-out 0s;
    transition: all 0.6s ease-in-out 0s;
}
.cetner-blog-style-3 .blog-start .blog-image a{
    position: relative;
    overflow: hidden;
}
.cetner-blog-style-3 .blog-start:hover .blog-image a img{
    -webkit-transform: scale(1.1);
    -o-transform: scale(1.1);
    transform: scale(1.1);
}
.cetner-blog-style-3 .blog-start .blog-image a img,
.cetner-blog-style-3 .blog-start:hover .blog-image a img{
    -webkit-transition: all 0.6s ease-in-out 0s;
    -o-transition: all 0.6s ease-in-out 0s;
    transition: all 0.6s ease-in-out 0s;
}
.cetner-blog-style-3 .blog-start .blog-content{
    padding-top: 30px;
}
.cetner-blog-style-3 .blog-start .blog-content .blog-date-comment{
    display: flex;
    justify-content: space-between;
}
.cetner-blog-style-3 .blog-start .blog-content .blog-date-comment span.blog-date,
.cetner-blog-style-3 .blog-start .blog-content .blog-date-comment a{
    display: flex;
    align-items: center;
    line-height: 1;
}
.cetner-blog-style-3 .blog-start .blog-content .blog-date-comment a:hover{
    color: #5fa800;
}
.cetner-blog-style-3 .blog-start .blog-content .blog-date-comment span.blog-date i,
.cetner-blog-style-3 .blog-start .blog-content .blog-date-comment a i{
    margin-right: 5px;
}
.cetner-blog-style-3 .blog-start .blog-content .blog-title h6{
    font-size: 16px;
    margin-top: 20px;
    padding-top: 14px;
    border-top: 1px solid #ededed;
}
.cetner-blog-style-3 .blog-start .blog-content .blog-title h6 a{
    display: block;
    width: 100%;
    white-space: nowrap;
    text-overflow: ellipsis;
    overflow: hidden;
}
.cetner-blog-style-3 .blog-start .blog-content .blog-title h6 a:hover{
    color: #5fa800;
}
.cetner-blog-style-3 .blog-start .blog-content p.blog-description{
    color: #999;
    margin-top: 17px;
    font-size: 14px;
}
.cetner-blog-style-3 .blog-start .blog-content .more-blog{
    margin-top: 17px;
    display: flex;
    align-items: center;
    justify-content: space-between;
}
.cetner-blog-style-3 .blog-start .blog-content .more-blog a.read-link{
    color: #5fa800;
    font-size: 14px;
    display: flex;
    align-items: center;
}
.cetner-blog-style-3 .blog-start .blog-content .more-blog a.read-link i{
    font-size: 12px;
    padding-left: 5px;
}
.cetner-blog-style-3 .blog-start .blog-content .more-blog a.read-link i,
.cetner-blog-style-3 .blog-start .blog-content .more-blog a.read-link:hover i{
    -webkit-transition: all 0.3s ease-in-out 0s;
    -o-transition: all 0.3s ease-in-out 0s;
    transition: all 0.3s ease-in-out 0s;
}
.cetner-blog-style-3 .blog-start .blog-content .more-blog a.read-link:hover i{
    margin-left: 8px;
}
.cetner-blog-style-3 .blog-start .blog-content .more-blog span.blog-admin span.blog-editor{
    font-weight: 600;
}
/* right column css */
.center-right-area .right-column-start h4{
    font-size: 16px;
    margin-bottom: 32px;
    line-height: 1;
}
.center-right-area .right-column-start .archive-link h5{
    display: inline;
    font-size: 14px;
    padding: 5px 15px;
    margin-bottom: 22px;
    line-height: 1;
    font-weight: 400;
    border-radius: 5px;
}
.center-right-area.style-7 .right-column-start .archive-link h5 {
    background-color: #cd7752;
    color: #fff;
}
.center-right-area .right-column-start .archive-link h5 {
    background-color: #73841b;
    color: #fff;
}
.center-right-area.style-3 .right-column-start .archive-link h5 {
    background-color: #5fa800;
    color: #fff;
}
.right-area.style-2 .right-column-start .archive-link h5 {
    background-color: #5fa800;
    color: #fff;
}
.center-right-area .right-column-start .archive-link ul {
    margin-top: 29px;
}
.center-right-area .right-column-start .archive-link ul li{
    margin-top: 9px;
}
.center-right-area .right-column-start .archive-link ul li a i {
    font-size: 12px;
}
.center-right-area .right-column-start .archive-link ul li a:hover{
    color: #5fa800;
}
.center-right-area .b-Reply{
    margin-top: 22px;
}
.center-right-area .b-Reply h4{
    margin-bottom: 27px;
    font-size: 16px;
    
    line-height: 1;
}
.center-right-area .b-Reply form input{
    width: 100%;
    padding: 8px 15px;
    margin-top: 16px;
}
.center-right-area .b-Reply form input:first-child{
    margin-top: 0px;
}
.center-right-area .b-Reply form a.Reply-link{
    background-color: #5fa800;
    color: #fff;
    width: 100%;
    padding: 8px 15px;
    margin-top: 30px;
    text-align: center;
    font-weight: 500;
    border: 2px solid #5fa800;
}
.center-right-area .b-Reply form a.Reply-link:hover{
    background-color: transparent;
    color: #000;
    border-color: #5fa800;
}
.center-right-area .r-image{
    padding: 10px;
    margin-top: 20px;
    border: 1px solid #eee;
}
/* blog style-5 right column css */
.right-area .right-column-start .archive-link ul li a:hover{
    color: #ec6504;
}
/* blog style-5 pagination css */
.all-page .page-number a:after{
    background-color: #ec6504;
    content: "";
    position: absolute;
    bottom: 0px;
    left: 1px;
    right: 0px;
    width: 4px;
    height: 4px;
    border-radius: 100%;
    opacity: 0;
    visibility: hidden;
}
.all-page .page-number a:hover:after,
.all-page .page-number a.active:after{
    opacity: 1;
    visibility: visible;
}
.all-page .page-number a:hover,
.all-page .page-number a.active{
    color: #ec6504;
}
/* blog style-5 center blog right column css */
.right-area .b-Reply form a.Reply-link{
    background-color: #ec6504;
    color: #fff;
    width: 100%;
    padding: 8px 15px;
    margin-top: 30px;
    text-align: center;
    font-weight: 500;
    border: 2px solid #ec6504;
}
.right-area .b-Reply form a.Reply-link:hover{
    background-color: transparent;
    color: #000;
    border-color: #ec6504;
}
/* blog style-5 full-left-right grid blog css */
.full-blog-style-5,
.left-blog-style-5,
.right-blog-style-5{
    display: flex;
    flex-wrap: wrap;
    margin-top: -30px;
    margin-left: -30px;
}
.full-blog-style-5 .blog-start,
.left-blog-style-5 .blog-start,
.right-blog-style-5 .blog-start{
    width: calc(33.33% - 30px);
    margin-left: 30px;
    margin-top: 30px;
}
.full-blog-style-5 .blog-start .blog-image a,
.left-blog-style-5 .blog-start .blog-image a,
.right-blog-style-5 .blog-start .blog-image a{
    display: block;
}
.full-blog-style-5 .blog-start .blog-content .blog-date-comment,
.left-blog-style-5 .blog-start .blog-content .blog-date-comment,
.right-blog-style-5 .blog-start .blog-content .blog-date-comment{
    background-color: #ec6504;
    color: #fff;
    padding: 5px 10px;
    display: flex;
    justify-content: space-between;
}
.full-blog-style-5 .blog-start .blog-content .blog-date-comment a,
.left-blog-style-5 .blog-start .blog-content .blog-date-comment a,
.right-blog-style-5 .blog-start .blog-content .blog-date-comment a{
    color: #fff;
    display: flex;
    align-items: center;
}
.full-blog-style-5 .blog-start .blog-content .blog-date-comment a i,
.left-blog-style-5 .blog-start .blog-content .blog-date-comment a i,
.right-blog-style-5 .blog-start .blog-content .blog-date-comment a i{
    margin-right: 5px;
}
.full-blog-style-5 .blog-start .blog-content .blog-title h6,
.left-blog-style-5 .blog-start .blog-content .blog-title h6,
.right-blog-style-5 .blog-start .blog-content .blog-title h6{
    font-size: 16px;
    margin-top: 23px;
}
.full-blog-style-5 .blog-start .blog-content .blog-title h6 a,
.left-blog-style-5 .blog-start .blog-content .blog-title h6 a,
.right-blog-style-5 .blog-start .blog-content .blog-title h6 a{
    display: block;
    width: 100%;
    white-space: nowrap;
    text-overflow: ellipsis;
    overflow: hidden;
}
.full-blog-style-5 .blog-start .blog-content p.blog-description,
.left-blog-style-5 .blog-start .blog-content p.blog-description,
.right-blog-style-5 .blog-start .blog-content p.blog-description{
    margin-top: 16px;
}
.full-blog-style-5 .blog-start .blog-content .more-blog,
.left-blog-style-5 .blog-start .blog-content .more-blog,
.right-blog-style-5 .blog-start .blog-content .more-blog{
    margin-top: 17px;
    display: flex;
    align-items: center;
    justify-content: space-between;
}
.full-blog-style-5 .blog-start .blog-content .more-blog a.read-link,
.left-blog-style-5 .blog-start .blog-content .more-blog a.read-link,
.right-blog-style-5 .blog-start .blog-content .more-blog a.read-link{
    color: #ec6504;
    font-size: 14px;
    font-weight: 600;
}
.full-blog-style-5 .blog-start .blog-content .more-blog a.read-link i,
.left-blog-style-5 .blog-start .blog-content .more-blog a.read-link i,
.right-blog-style-5 .blog-start .blog-content .more-blog a.read-link i{
    font-size: 12px;
}
.full-blog-style-5 .blog-start .blog-content .more-blog a.read-link i,
.full-blog-style-5 .blog-start .blog-content .more-blog a.read-link:hover i,
.left-blog-style-5 .blog-start .blog-content .more-blog a.read-link i,
.left-blog-style-5 .blog-start .blog-content .more-blog a.read-link:hover i,
.right-blog-style-5 .blog-start .blog-content .more-blog a.read-link i,
.right-blog-style-5 .blog-start .blog-content .more-blog a.read-link:hover i{
    -webkit-transition: all 0.3s ease-in-out 0s;
    -o-transition: all 0.3s ease-in-out 0s;
    transition: all 0.3s ease-in-out 0s;
}
.full-blog-style-5 .blog-start .blog-content .more-blog a.read-link:hover i,
.left-blog-style-5 .blog-start .blog-content .more-blog a.read-link:hover i,
.right-blog-style-5 .blog-start .blog-content .more-blog a.read-link:hover i{
    margin-left: 8px;
}
.full-blog-style-5 .blog-start .blog-content .more-blog span.blog-admin span.blog-editor,
.left-blog-style-5 .blog-start .blog-content .more-blog span.blog-admin span.blog-editor,
.right-blog-style-5 .blog-start .blog-content .more-blog span.blog-admin span.blog-editor{
    font-weight: 600;
}
/* full-left-right list blog css */
.full-blog-list-style-5,
.left-blog-list-style-5,
.right-blog-list-style-5{
    display: flex;
    flex-wrap: wrap;
}
.full-blog-list-style-5 .blog-start,
.left-blog-list-style-5 .blog-start,
.right-blog-list-style-5 .blog-start{
    width: 100%;
    margin-bottom: 20px;
    padding-bottom: 20px;
    border-bottom: 1px solid #eee;
    display: flex;
    align-items: center;
}
.full-blog-list-style-5 .blog-start .blog-image{
    width: 35%;
}
.left-blog-list-style-5 .blog-start .blog-image,
.right-blog-list-style-5 .blog-start .blog-image{
    width: 48%;
}
.full-blog-list-style-5 .blog-start .blog-image a,
.left-blog-list-style-5 .blog-start .blog-image a,
.right-blog-list-style-5 .blog-start .blog-image a{
    display: block;
}
.full-blog-list-style-5 .blog-start .blog-content{
    width: calc(65% - 30px);
    margin-left: 30px;
}
.left-blog-list-style-5 .blog-start .blog-content,
.right-blog-list-style-5 .blog-start .blog-content{
    width: calc(52% - 30px);
    margin-left: 30px;
}
.full-blog-list-style-5 .blog-start .blog-content .blog-date-comment,
.left-blog-list-style-5 .blog-start .blog-content .blog-date-comment,
.right-blog-list-style-5 .blog-start .blog-content .blog-date-comment{
    color: #222;
    display: flex;
    justify-content: space-between;
}
.full-blog-list-style-5 .blog-start .blog-content .blog-date-comment a,
.left-blog-list-style-5 .blog-start .blog-content .blog-date-comment a,
.right-blog-list-style-5 .blog-start .blog-content .blog-date-comment a,
.full-blog-list-style-5 .blog-start .blog-content .blog-date-comment span.blog-date,
.left-blog-list-style-5 .blog-start .blog-content .blog-date-comment span.blog-date,
.right-blog-list-style-5 .blog-start .blog-content .blog-date-comment span.blog-date{
    display: flex;
    align-items: center;
    line-height: 1;
}
.full-blog-list-style-5 .blog-start .blog-content .blog-date-comment a i,
.left-blog-list-style-5 .blog-start .blog-content .blog-date-comment a i,
.right-blog-list-style-5 .blog-start .blog-content .blog-date-comment a i,
.full-blog-list-style-5 .blog-start .blog-content .blog-date-comment span.blog-date i,
.left-blog-list-style-5 .blog-start .blog-content .blog-date-comment span.blog-date i,
.right-blog-list-style-5 .blog-start .blog-content .blog-date-comment span.blog-date i{
    margin-right: 5px;
}
.full-blog-list-style-5 .blog-start .blog-content .blog-title h6,
.left-blog-list-style-5 .blog-start .blog-content .blog-title h6,
.right-blog-list-style-5 .blog-start .blog-content .blog-title h6{
    font-size: 16px;
    margin-top: 23px;
}
.full-blog-list-style-5 .blog-start .blog-content .blog-title h6 a,
.left-blog-list-style-5 .blog-start .blog-content .blog-title h6 a,
.right-blog-list-style-5 .blog-start .blog-content .blog-title h6 a{
    display: block;
    width: 100%;
    white-space: nowrap;
    text-overflow: ellipsis;
    overflow: hidden;
}
.full-blog-list-style-5 .blog-start .blog-content p.blog-description,
.left-blog-list-style-5 .blog-start .blog-content p.blog-description,
.right-blog-list-style-5 .blog-start .blog-content p.blog-description{
    margin-top: 16px;
}
.full-blog-list-style-5 .blog-start .blog-content .more-blog,
.left-blog-list-style-5 .blog-start .blog-content .more-blog,
.right-blog-list-style-5 .blog-start .blog-content .more-blog{
    margin-top: 17px;
    display: flex;
    align-items: center;
    justify-content: space-between;
}
.full-blog-list-style-5 .blog-start .blog-content .more-blog a.read-link,
.left-blog-list-style-5 .blog-start .blog-content .more-blog a.read-link,
.right-blog-list-style-5 .blog-start .blog-content .more-blog a.read-link{
    color: #ec6504;
    font-size: 14px;
    font-weight: 600;
}
.full-blog-list-style-5 .blog-start .blog-content .more-blog a.read-link i,
.left-blog-list-style-5 .blog-start .blog-content .more-blog a.read-link i,
.right-blog-list-style-5 .blog-start .blog-content .more-blog a.read-link i{
    font-size: 12px;
}
.full-blog-list-style-5 .blog-start .blog-content .more-blog a.read-link i,
.full-blog-list-style-5 .blog-start .blog-content .more-blog a.read-link:hover i,
.left-blog-list-style-5 .blog-start .blog-content .more-blog a.read-link i,
.left-blog-list-style-5 .blog-start .blog-content .more-blog a.read-link:hover i,
.right-blog-list-style-5 .blog-start .blog-content .more-blog a.read-link i,
.right-blog-list-style-5 .blog-start .blog-content .more-blog a.read-link:hover i{
    -webkit-transition: all 0.3s ease-in-out 0s;
    -o-transition: all 0.3s ease-in-out 0s;
    transition: all 0.3s ease-in-out 0s;
}
.full-blog-list-style-5 .blog-start .blog-content .more-blog a.read-link:hover i,
.left-blog-list-style-5 .blog-start .blog-content .more-blog a.read-link:hover i,
.right-blog-list-style-5 .blog-start .blog-content .more-blog a.read-link:hover i{
    margin-left: 8px;
}
.full-blog-list-style-5 .blog-start .blog-content .more-blog span.blog-admin span.blog-editor,
.left-blog-list-style-5 .blog-start .blog-content .more-blog span.blog-admin span.blog-editor,
.right-blog-list-style-5 .blog-start .blog-content .more-blog span.blog-admin span.blog-editor{
    font-weight: 600;
}
/* style-5 full-left-right details blog css */
.single-image-5{
    margin-bottom: 33px;
}
.single-image-5 a img.image-xs{
    display: none;
}
.style-5-b-details .single-image{
    margin-bottom: 33px;
}
.style-5-left-blog-details .single-blog-content .full-image,
.style-5-right-blog-details .single-blog-content .full-image{
    display: none;
}
.blog-style-5-details .single-blog-content .single-b-title h4,
.style-5-left-blog-details .single-blog-content .single-b-title h4,
.style-5-right-blog-details .single-blog-content .single-b-title h4{
    font-size: 16px;
}
.blog-style-5-details .single-blog-content .date-edit-comments,
.style-5-left-blog-details .single-blog-content .date-edit-comments,
.style-5-right-blog-details .single-blog-content .date-edit-comments {
    margin-top: 23px;
}
.blog-style-5-details .single-blog-content .date-edit-comments .blog-info-wrap,
.style-5-left-blog-details .single-blog-content .date-edit-comments .blog-info-wrap,
.style-5-right-blog-details .single-blog-content .date-edit-comments .blog-info-wrap {
    display: flex;
    flex-wrap: wrap;
    align-items: center;
    margin: -15px 0px 0px -30px;
}
.blog-style-5-details .single-blog-content .date-edit-comments .blog-info-wrap span.blog-data,
.style-5-left-blog-details .single-blog-content .date-edit-comments .blog-info-wrap span.blog-data,
.style-5-right-blog-details .single-blog-content .date-edit-comments .blog-info-wrap span.blog-data {
    margin: 15px 0px 0px 30px;
}
.blog-style-5-details .single-blog-content .date-edit-comments .blog-info-wrap span.date,
.style-5-left-blog-details .single-blog-content .date-edit-comments .blog-info-wrap span.date,
.style-5-right-blog-details .single-blog-content .date-edit-comments .blog-info-wrap span.date{
    display: flex;
    align-items: center;
    line-height: 1;
}
.blog-style-5-details .single-blog-content .date-edit-comments .blog-info-wrap span.date span.blog-d-n-c,
.style-5-left-blog-details .single-blog-content .date-edit-comments .blog-info-wrap span.date span.blog-d-n-c,
.style-5-right-blog-details .single-blog-content .date-edit-comments .blog-info-wrap span.date span.blog-d-n-c{
    margin-left: 5px;
}
.blog-style-5-details .single-blog-content .date-edit-comments .blog-info-wrap span.blog-edit,
.style-5-left-blog-details .single-blog-content .date-edit-comments .blog-info-wrap span.blog-edit,
.style-5-right-blog-details .single-blog-content .date-edit-comments .blog-info-wrap span.blog-edit{
    display: flex;
    align-items: center;
    line-height: 1;
}
.blog-style-5-details .single-blog-content .date-edit-comments .blog-info-wrap span.blog-edit span.blog-d-n-c,
.style-5-left-blog-details .single-blog-content .date-edit-comments .blog-info-wrap span.blog-edit span.blog-d-n-c,
.style-5-right-blog-details .single-blog-content .date-edit-comments .blog-info-wrap span.blog-edit span.blog-d-n-c{
    margin-left: 5px;
}
.blog-style-5-details .single-blog-content .date-edit-comments .blog-info-wrap span.comments,
.style-5-left-blog-details .single-blog-content .date-edit-comments .blog-info-wrap span.comments,
.style-5-right-blog-details .single-blog-content .date-edit-comments .blog-info-wrap span.comments{
    display: flex;
    align-items: center;
    line-height: 1;
}
.blog-style-5-details .single-blog-content .date-edit-comments .blog-info-wrap span.comments span.blog-d-n-c,
.style-5-left-blog-details .single-blog-content .date-edit-comments .blog-info-wrap span.comments span.blog-d-n-c,
.style-5-right-blog-details .single-blog-content .date-edit-comments .blog-info-wrap span.comments span.blog-d-n-c{
    margin-left: 5px;
}   
.blog-style-5-details .single-blog-content .blog-description,
.style-5-left-blog-details .single-blog-content .blog-description,
.style-5-right-blog-details .single-blog-content .blog-description{
    margin-top: 23px;
}
.blog-style-5-details .single-blog-content .blog-description p,
.style-5-left-blog-details .single-blog-content .blog-description p,
.style-5-right-blog-details .single-blog-content .blog-description p{
    margin-top: 5px;
}
.blog-style-5-details .single-blog-content .blog-description .blog-image-description,
.style-5-left-blog-details .single-blog-content .blog-description .blog-image-description,
.style-5-right-blog-details .single-blog-content .blog-description .blog-image-description{
    margin-top: 22px;
    margin-bottom: 23px;
    display: flex;
    flex-wrap: wrap;
}
.blog-style-5-details .single-blog-content .blog-description .blog-image-description img.b-image-1,
.style-5-left-blog-details .single-blog-content .blog-description .blog-image-description img.b-image-1,
.style-5-right-blog-details .single-blog-content .blog-description .blog-image-description img.b-image-1{
    width: calc(50% - 30px);
    margin-right: 30px;
}
.blog-style-5-details .single-blog-content .blog-description .blog-image-description img.b-image-2,
.style-5-left-blog-details .single-blog-content .blog-description .blog-image-description img.b-image-2,
.style-5-right-blog-details .single-blog-content .blog-description .blog-image-description img.b-image-2{
    width: 50%;
}
.blog-style-5-details .single-blog-content .blog-description p.color-description,
.style-5-left-blog-details .single-blog-content .blog-description p.color-description,
.style-5-right-blog-details .single-blog-content .blog-description p.color-description{
    font-size: 16px;
    color: #ec6504;
    margin: 22px 0px;
    padding: 30px;
    border-left: 1px solid #ddd;
    background-color: #f7f7f7;
}
.blog-style-5-details .single-blog-content .blog-info,
.style-5-left-blog-details .single-blog-content .blog-info,
.style-5-right-blog-details .single-blog-content .blog-info{
    padding: 30px;
    margin-top: 22px;
    background-color: #f7f7f7;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-direction: column;
}
.blog-style-5-details .single-blog-content .blog-info i.fa-quote-left,
.style-5-left-blog-details .single-blog-content .blog-info i.fa-quote-left,
.style-5-right-blog-details .single-blog-content .blog-info i.fa-quote-left{
    font-size: 30px;
    color: #ec6504;
}
.blog-style-5-details .single-blog-content .blog-info h6,
.style-5-left-blog-details .single-blog-content .blog-info h6,
.style-5-right-blog-details .single-blog-content .blog-info h6{
    color: #ec6504;
    font-size: 16px;
    margin-top: 9px;
    font-weight: 600;
}
.blog-style-5-details .single-blog-content .b-link,
.style-5-left-blog-details .single-blog-content .b-link,
.style-5-right-blog-details .single-blog-content .b-link{
    margin-top: 30px;
}
.blog-style-5-details .single-blog-content .b-link a,
.style-5-left-blog-details .single-blog-content .b-link a,
.style-5-right-blog-details .single-blog-content .b-link a{
    background-color: #ec6504;
    padding: 5px 10px;
    color: #fff;
    border-radius: 4px;
}
.blog-style-5-details .single-blog-content .b-link a:hover,
.style-5-left-blog-details .single-blog-content .b-link a:hover,
.style-5-right-blog-details .single-blog-content .b-link a:hover{
    background-color: #000;
    color: #fff;
}
.blog-style-5-details .single-blog-content .blog-comments,
.style-5-left-blog-details .single-blog-content .blog-comments,
.style-5-right-blog-details .single-blog-content .blog-comments{
    margin-top: 28px;
}
.blog-style-5-details .single-blog-content .blog-comments h4,
.style-5-left-blog-details .single-blog-content .blog-comments h4,
.style-5-right-blog-details .single-blog-content .blog-comments h4{
    font-size: 18px;
    line-height: 1;
}
.blog-style-5-details .single-blog-content .blog-comments h4 span,
.style-5-left-blog-details .single-blog-content .blog-comments h4 span,
.style-5-right-blog-details .single-blog-content .blog-comments h4 span{
    color: #ec6504;
}
.blog-style-5-details .single-blog-content .blog-comments .blog-comment-info,
.style-5-left-blog-details .single-blog-content .blog-comments .blog-comment-info,
.style-5-right-blog-details .single-blog-content .blog-comments .blog-comment-info {
    margin-top: 27px;
    padding-top: 30px;
    border-top: 1px solid #eee;
}
.blog-style-5-details .single-blog-content .blog-comments .blog-comment-info ul.comments-arae,
.style-5-left-blog-details .single-blog-content .blog-comments .blog-comment-info ul.comments-arae,
.style-5-right-blog-details .single-blog-content .blog-comments .blog-comment-info ul.comments-arae {
    margin-top: 24px;
    display: flex;
}
.blog-style-5-details .single-blog-content .blog-comments .blog-comment-info ul.comments-arae:first-of-type,
.style-5-left-blog-details .single-blog-content .blog-comments .blog-comment-info ul.comments-arae:first-of-type,
.style-5-right-blog-details .single-blog-content .blog-comments .blog-comment-info ul.comments-arae:first-of-type {
    margin-top: 0px;
}
.blog-style-5-details .single-blog-content .blog-comments .blog-comment-info ul.comments-arae.comment-reply,
.style-5-left-blog-details .single-blog-content .blog-comments .blog-comment-info ul.comments-arae.comment-reply,
.style-5-right-blog-details .single-blog-content .blog-comments .blog-comment-info ul.comments-arae.comment-reply {
    padding-left: 50px;
}
.blog-style-5-details .single-blog-content .blog-comments .blog-comment-info ul.comments-arae.all-reply,
.style-5-left-blog-details .single-blog-content .blog-comments .blog-comment-info ul.comments-arae.all-reply,
.style-5-right-blog-details .single-blog-content .blog-comments .blog-comment-info ul.comments-arae.all-reply {
    margin-top: 24px;
    padding-top: 30px;
    border-top: 1px solid #eee;
}
.blog-style-5-details .single-blog-content .blog-comments .blog-comment-info ul.comments-arae li.comments-man,
.style-5-left-blog-details .single-blog-content .blog-comments .blog-comment-info ul.comments-arae li.comments-man,
.style-5-right-blog-details .single-blog-content .blog-comments .blog-comment-info ul.comments-arae li.comments-man{
    width: 45px;
    height: 45px;
    background-color: #ec6504;
    color: #fff;
    margin-right: 15px;
    font-size: 15px;
    border-radius: 3px;
    display: flex;
    align-items: center;
    text-align: center;
    justify-content: center;
    font-weight: 600;
}
.blog-style-5-details .single-blog-content .blog-comments .blog-comment-info ul.comments-arae li.comments-content,
.style-5-left-blog-details .single-blog-content .blog-comments .blog-comment-info ul.comments-arae li.comments-content,
.style-5-right-blog-details .single-blog-content .blog-comments .blog-comment-info ul.comments-arae li.comments-content{
    width: calc(100% - 45px);
}
.blog-style-5-details .single-blog-content .blog-comments .blog-comment-info ul.comments-arae li.comments-content span.comments-result,
.style-5-left-blog-details .single-blog-content .blog-comments .blog-comment-info ul.comments-arae li.comments-content span.comments-result,
.style-5-right-blog-details .single-blog-content .blog-comments .blog-comment-info ul.comments-arae li.comments-content span.comments-result{
    display: block;
}
.blog-style-5-details .single-blog-content .blog-comments .blog-comment-info ul.comments-arae li.comments-content span.comment-name,
.style-5-left-blog-details .single-blog-content .blog-comments .blog-comment-info ul.comments-arae li.comments-content span.comment-name,
.style-5-right-blog-details .single-blog-content .blog-comments .blog-comment-info ul.comments-arae li.comments-content span.comment-name {
    margin-top: 5px;
}
.blog-style-5-details .single-blog-content .blog-comments .blog-comment-info ul.comments-arae li.comments-content span.comment-name i,
.style-5-left-blog-details .single-blog-content .blog-comments .blog-comment-info ul.comments-arae li.comments-content span.comment-name i,
.style-5-right-blog-details .single-blog-content .blog-comments .blog-comment-info ul.comments-arae li.comments-content span.comment-name i{
    font-style: normal;
}
.blog-style-5-details .single-blog-content .blog-comments .blog-comment-info ul.comments-arae li.comments-content span.comments-result.c-date,
.style-5-left-blog-details .single-blog-content .blog-comments .blog-comment-info ul.comments-arae li.comments-content span.comments-result.c-date,
.style-5-right-blog-details .single-blog-content .blog-comments .blog-comment-info ul.comments-arae li.comments-content span.comments-result.c-date{
    margin-top: 6px;
    margin-bottom: 0px;
    font-weight: 600;
}
.blog-style-5-details .single-blog-content .blog-comments .blog-comment-info ul.comments-arae li.comments-content span span.comments-title,
.style-5-left-blog-details .single-blog-content .blog-comments .blog-comment-info ul.comments-arae li.comments-content span span.comments-title,
.style-5-right-blog-details .single-blog-content .blog-comments .blog-comment-info ul.comments-arae li.comments-content span span.comments-title{
    font-weight: 600;
    color: #ec6504;
}
.blog-style-5-details .single-blog-content .blog-comments .blog-comment-info ul.comments-arae li.Reply-btn,
.style-5-left-blog-details .single-blog-content .blog-comments .blog-comment-info ul.comments-arae li.Reply-btn,
.style-5-right-blog-details .single-blog-content .blog-comments .blog-comment-info ul.comments-arae li.Reply-btn{
    width: 120px;
}
.blog-style-5-details .single-blog-content .blog-comments .blog-comment-info ul.comments-arae li.comments-content span.comments-result.c-date a.Reply,
.style-5-left-blog-details .single-blog-content .blog-comments .blog-comment-info ul.comments-arae li.comments-content span.comments-result.c-date a.Reply,
.style-5-right-blog-details .single-blog-content .blog-comments .blog-comment-info ul.comments-arae li.comments-content span.comments-result.c-date a.Reply {
    color: #ec6504;
    margin-left: 30px;
    font-weight: 500;
}
/* style-5 center blog css */
.cetner-blog-style-5{
    display: flex;
    flex-wrap: wrap;
}
.cetner-blog-style-5 .blog-start{
    width: 100%;
    margin-top: 30px;
}
.cetner-blog-style-5 .blog-start:nth-child(1){
    margin-top: 0px;
}
.cetner-blog-style-5 .blog-start .blog-image a{
    display: block;
}
.cetner-blog-style-5 .blog-start .blog-content .blog-date-comment{
    background-color: #ec6504;
    color: #fff;
    padding: 5px 10px;
    display: flex;
    justify-content: space-between;
}
.cetner-blog-style-5 .blog-start .blog-content .blog-date-comment a{
    color: #fff;
    display: flex;
    align-items: center;
}
.cetner-blog-style-5 .blog-start .blog-content .blog-date-comment a i{
    margin-right: 5px;
}
.cetner-blog-style-5 .blog-start .blog-content .blog-title h6{
    font-size: 16px;
    margin-top: 23px;
}
.cetner-blog-style-5 .blog-start .blog-content .blog-title h6 a{
    display: block;
    width: 100%;
    white-space: nowrap;
    text-overflow: ellipsis;
    overflow: hidden;
}
.cetner-blog-style-5 .blog-start .blog-content p.blog-description{
    margin-top: 16px;
}
.cetner-blog-style-5 .blog-start .blog-content .more-blog{
    margin-top: 17px;
    display: flex;
    align-items: center;
    justify-content: space-between;
}
.cetner-blog-style-5 .blog-start .blog-content .more-blog a.read-link{
    color: #ec6504;
    font-size: 14px;
    font-weight: 600;
    display: flex;
    align-items: center;
}
.cetner-blog-style-5 .blog-start .blog-content .more-blog a.read-link i {
    font-size: 12px;
    padding-left: 5px;
}
.cetner-blog-style-5 .blog-start .blog-content .more-blog a.read-link i,
.cetner-blog-style-5 .blog-start .blog-content .more-blog a.read-link:hover i{
    -webkit-transition: all 0.3s ease-in-out 0s;
    -o-transition: all 0.3s ease-in-out 0s;
    transition: all 0.3s ease-in-out 0s;
}
.cetner-blog-style-5 .blog-start .blog-content .more-blog a.read-link:hover i{
    margin-left: 8px;
}
.cetner-blog-style-5 .blog-start .blog-content .more-blog span.blog-admin span.blog-editor{
    font-weight: 600;
}
/* style-6 left-right column css */
.right-area .right-column-start .archive-link ul li a:hover{
    color: #73841b;
}
/* style-6 center right column css */
.center-right-area .b-Reply form a.Reply-link{
    background-color: #73841b;
    color: #fff;
    width: 100%;
    padding: 8px 15px;
    margin-top: 30px;
    text-align: center;
    font-weight: 500;
    border: 2px solid #73841b;
}
.center-right-area .b-Reply form a.Reply-link:hover{
    background-color: transparent;
    color: #000;
    border-color: #73841b;
}
/* pagination css */
.all-page .page-number a:after{
    background-color: #73841b;
    content: "";
    position: absolute;
    bottom: 0px;
    left: 1px;
    right: 0px;
    width: 4px;
    height: 4px;
    border-radius: 100%;
    opacity: 0;
    visibility: hidden;
}
.all-page .page-number a:hover:after,
.all-page .page-number a.active:after{
    opacity: 1;
    visibility: visible;
}
.all-page .page-number a:hover,
.all-page .page-number a.active{
    color: #73841b;
}
/* style-6 full-left-right 3 grid blog css */
.blog-style-6-3-grid,
.blog-style-6-left-3-grid,
.blog-style-6-right-3-grid{
    display: flex;
    flex-wrap: wrap;
    margin-top: -30px;
    margin-left: -30px;
}
.blog-style-6-3-grid .blog-start,
.blog-style-6-left-3-grid .blog-start,
.blog-style-6-right-3-grid .blog-start{
    width: calc(33.33% - 30px);
    margin-left: 30px;
    margin-top: 30px;
}
.blog-style-6-3-grid .blog-start .blog-image,
.blog-style-6-left-3-grid .blog-start .blog-image,
.blog-style-6-right-3-grid .blog-start .blog-image{
    position: relative;
    display: flex;
}
.blog-style-6-3-grid .blog-start .blog-image a,
.blog-style-6-left-3-grid .blog-start .blog-image a,
.blog-style-6-right-3-grid .blog-start .blog-image a{
    position: relative;
    overflow: hidden;
}
.blog-style-6-3-grid .blog-start:hover .blog-image a img,
.blog-style-6-left-3-grid .blog-start:hover .blog-image a img,
.blog-style-6-right-3-grid .blog-start:hover .blog-image a img{
    -webkit-transform: scale(1.06);
    -o-transform: scale(1.06);
    transform: scale(1.06);
}
.blog-style-6-3-grid .blog-start .blog-image a img,
.blog-style-6-3-grid .blog-start:hover .blog-image a img,
.blog-style-6-left-3-grid .blog-start .blog-image a img,
.blog-style-6-left-3-grid .blog-start:hover .blog-image a img,
.blog-style-6-right-3-grid .blog-start .blog-image a img,
.blog-style-6-right-3-grid .blog-start:hover .blog-image a img{
    -webkit-transition: all 0.3s ease-in-out 0s;
    -o-transition: all 0.3s ease-in-out 0s;
    transition: all 0.3s ease-in-out 0s;
}
.blog-style-6-3-grid .blog-start .blog-image .image-link,
.blog-style-6-left-3-grid .blog-start .blog-image .image-link,
.blog-style-6-right-3-grid .blog-start .blog-image .image-link{
    position: absolute;
    bottom: 0px;
    left: 6px;
}
.blog-style-6-3-grid .blog-start .blog-image .image-link a,
.blog-style-6-left-3-grid .blog-start .blog-image .image-link a,
.blog-style-6-right-3-grid .blog-start .blog-image .image-link a{
    background-color: #73841b;
    color: #fff;
    padding: 2px 6px;
    font-size: 12px;
    border-radius: 3px;
    text-transform: uppercase;
}
.blog-style-6-3-grid .blog-start .blog-image .image-link a:hover,
.blog-style-6-left-3-grid .blog-start .blog-image .image-link a:hover,
.blog-style-6-right-3-grid .blog-start .blog-image .image-link a:hover{
    background-color: #000;
}
.blog-style-6-3-grid .blog-start .blog-content .blog-title h6,
.blog-style-6-left-3-grid .blog-start .blog-content .blog-title h6,
.blog-style-6-right-3-grid .blog-start .blog-content .blog-title h6{
    font-size: 16px;
    padding-top: 23px;
}
.blog-style-6-3-grid .blog-start .blog-content .blog-title h6 a,
.blog-style-6-left-3-grid .blog-start .blog-content .blog-title h6 a,
.blog-style-6-right-3-grid .blog-start .blog-content .blog-title h6 a{
    display: block;
    width: 100%;
    white-space: nowrap;
    text-overflow: ellipsis;
    overflow: hidden;
}
.blog-style-6-3-grid .blog-start .blog-content .blog-title h6 a:hover,
.blog-style-6-left-3-grid .blog-start .blog-content .blog-title h6 a:hover,
.blog-style-6-right-3-grid .blog-start .blog-content .blog-title h6 a:hover{
    color: #73841b;
}
.blog-style-6-3-grid .blog-start .blog-content p.blog-description,
.blog-style-6-left-3-grid .blog-start .blog-content p.blog-description,
.blog-style-6-right-3-grid .blog-start .blog-content p.blog-description{
    color: #999;
    margin-top: 16px;
}
.blog-style-6-3-grid .blog-start .blog-content .more-blog,
.blog-style-6-left-3-grid .blog-start .blog-content .more-blog,
.blog-style-6-right-3-grid .blog-start .blog-content .more-blog{
    margin-top: 17px;
    display: flex;
    align-items: center;
    justify-content: space-between;
}
.blog-style-6-3-grid .blog-start .blog-content .more-blog a.read-link,
.blog-style-6-left-3-grid .blog-start .blog-content .more-blog a.read-link,
.blog-style-6-right-3-grid .blog-start .blog-content .more-blog a.read-link{
    color: #73841b;
    font-size: 14px;
}
.blog-style-6-right-3-grid .blog-start .blog-content .more-blog a.read-link i,
.blog-style-6-left-3-grid .blog-start .blog-content .more-blog a.read-link i,
.blog-style-6-3-grid .blog-start .blog-content .more-blog a.read-link i{
    font-size: 12px;
}
.blog-style-6-3-grid .blog-start .blog-content .more-blog a.read-link i,
.blog-style-6-3-grid .blog-start .blog-content .more-blog a.read-link:hover i,
.blog-style-6-left-3-grid .blog-start .blog-content .more-blog a.read-link i,
.blog-style-6-left-3-grid .blog-start .blog-content .more-blog a.read-link:hover i,
.blog-style-6-right-3-grid .blog-start .blog-content .more-blog a.read-link i,
.blog-style-6-right-3-grid .blog-start .blog-content .more-blog a.read-link:hover i{
    -webkit-transition: all 0.3s ease-in-out 0s;
    -o-transition: all 0.3s ease-in-out 0s;
    transition: all 0.3s ease-in-out 0s;
}
.blog-style-6-3-grid .blog-start .blog-content .more-blog a.read-link:hover i,
.blog-style-6-left-3-grid .blog-start .blog-content .more-blog a.read-link:hover i,
.blog-style-6-right-3-grid .blog-start .blog-content .more-blog a.read-link:hover i{
    margin-left: 8px;
}
/* style-6 full-left-right list blog css */
.full-blog-list-style-6,
.left-blog-list-style-6,
.right-blog-list-style-6{
    display: flex;
    flex-wrap: wrap;
}
.full-blog-list-style-6 .blog-start,
.left-blog-list-style-6 .blog-start,
.right-blog-list-style-6 .blog-start{
    width: 100%;
    margin-bottom: 20px;
    padding-bottom: 20px;
    display: flex;
    align-items: center;
    border-bottom: 1px solid #eee;
}
.full-blog-list-style-6 .blog-start .blog-image,
.left-blog-list-style-6 .blog-start .blog-image,
.right-blog-list-style-6 .blog-start .blog-image{
    position: relative;
    display: flex;
    width: 50%;
}
.full-blog-list-style-6 .blog-start .blog-image a,
.left-blog-list-style-6 .blog-start .blog-image a,
.right-blog-list-style-6 .blog-start .blog-image a{
    position: relative;
    overflow: hidden;
}
.full-blog-list-style-6 .blog-start:hover .blog-image a img,
.left-blog-list-style-6 .blog-start:hover .blog-image a img,
.right-blog-list-style-6 .blog-start:hover .blog-image a img{
    -webkit-transform: scale(1.06);
    -o-transform: scale(1.06);
    transform: scale(1.06);
}
.full-blog-list-style-6 .blog-start .blog-image a img,
.full-blog-list-style-6 .blog-start:hover .blog-image a img,
.left-blog-list-style-6 .blog-start .blog-image a img,
.left-blog-list-style-6 .blog-start:hover .blog-image a img,
.right-blog-list-style-6 .blog-start .blog-image a img,
.right-blog-list-style-6 .blog-start:hover .blog-image a img{
    -webkit-transition: all 0.3s ease-in-out 0s;
    -o-transition: all 0.3s ease-in-out 0s;
    transition: all 0.3s ease-in-out 0s;
}
.full-blog-list-style-6 .blog-start .blog-image .image-link,
.left-blog-list-style-6 .blog-start .blog-image .image-link,
.right-blog-list-style-6 .blog-start .blog-image .image-link{
    position: absolute;
    bottom: 0px;
    left: 6px;
}
.full-blog-list-style-6 .blog-start .blog-image .image-link a,
.left-blog-list-style-6 .blog-start .blog-image .image-link a,
.right-blog-list-style-6 .blog-start .blog-image .image-link a{
    background-color: #73841b;
    color: #fff;
    padding: 4px 8px;
    font-size: 12px;
    border-radius: 3px;
    text-transform: uppercase;
}
.full-blog-list-style-6 .blog-start .blog-image .image-link a:hover,
.left-blog-list-style-6 .blog-start .blog-image .image-link a:hover,
.right-blog-list-style-6 .blog-start .blog-image .image-link a:hover{
    background-color: #000;
}
.full-blog-list-style-6 .blog-start .blog-content,
.left-blog-list-style-6 .blog-start .blog-content,
.right-blog-list-style-6 .blog-start .blog-content{
    width: calc(50% - 30px);
    margin-left: 30px;
}
.full-blog-list-style-6 .blog-start .blog-content .blog-title h6,
.left-blog-list-style-6 .blog-start .blog-content .blog-title h6,
.right-blog-list-style-6 .blog-start .blog-content .blog-title h6{
    font-size: 16px;
}
.full-blog-list-style-6 .blog-start .blog-content .blog-title h6 a,
.left-blog-list-style-6 .blog-start .blog-content .blog-title h6 a,
.right-blog-list-style-6 .blog-start .blog-content .blog-title h6 a{
    display: block;
    width: 100%;
    white-space: nowrap;
    text-overflow: ellipsis;
    overflow: hidden;
}
.full-blog-list-style-6 .blog-start .blog-content .blog-title h6 a:hover,
.left-blog-list-style-6 .blog-start .blog-content .blog-title h6 a:hover,
.right-blog-list-style-6 .blog-start .blog-content .blog-title h6 a:hover{
    color: #73841b;
}
.full-blog-list-style-6 .blog-start .blog-content p.blog-description,
.left-blog-list-style-6 .blog-start .blog-content p.blog-description,
.right-blog-list-style-6 .blog-start .blog-content p.blog-description{
    color: #999;
    margin-top: 16px;
    font-size: 14px;
}
.full-blog-list-style-6 .blog-start .blog-content .more-blog,
.left-blog-list-style-6 .blog-start .blog-content .more-blog,
.right-blog-list-style-6 .blog-start .blog-content .more-blog{
    margin-top: 17px;
    display: flex;
    align-items: center;
    justify-content: space-between;
}
.full-blog-list-style-6 .blog-start .blog-content .more-blog a.read-link,
.left-blog-list-style-6 .blog-start .blog-content .more-blog a.read-link,
.right-blog-list-style-6 .blog-start .blog-content .more-blog a.read-link{
    color: #73841b;
    font-size: 14px;
}
.full-blog-list-style-6 .blog-start .blog-content .more-blog a.read-link i,
.left-blog-list-style-6 .blog-start .blog-content .more-blog a.read-link i,
.right-blog-list-style-6 .blog-start .blog-content .more-blog a.read-link i{
    font-size: 12px;
}
.full-blog-list-style-6 .blog-start .blog-content .more-blog a.read-link i,
.full-blog-list-style-6 .blog-start .blog-content .more-blog a.read-link:hover i,
.left-blog-list-style-6 .blog-start .blog-content .more-blog a.read-link i,
.left-blog-list-style-6 .blog-start .blog-content .more-blog a.read-link:hover i,
.right-blog-list-style-6 .blog-start .blog-content .more-blog a.read-link i,
.right-blog-list-style-6 .blog-start .blog-content .more-blog a.read-link:hover i{
    -webkit-transition: all 0.3s ease-in-out 0s;
    -o-transition: all 0.3s ease-in-out 0s;
    transition: all 0.3s ease-in-out 0s;
}
.full-blog-list-style-6 .blog-start .blog-content .more-blog a.read-link:hover i,
.left-blog-list-style-6 .blog-start .blog-content .more-blog a.read-link:hover i,
.right-blog-list-style-6 .blog-start .blog-content .more-blog a.read-link:hover i{
    margin-left: 8px;
}
/* full-left-right blog details css */
.full-blog-details-style-6 .single-image,
.right-blog-details-style-6 .single-image,
.left-blog-details-style-6 .single-image{
    width: 100%;
    line-height: 0;
}
.full-blog-details-style-6 .single-image iframe,
.right-blog-details-style-6 .single-image iframe,
.left-blog-details-style-6 .single-image iframe{
    width: 100%;
}
.full-blog-details-style-6 .single-blog-content,
.left-blog-details-style-6 .single-blog-content,
.right-blog-details-style-6 .single-blog-content{
    margin-top: 33px;
}
.full-blog-details-style-6 .single-blog-content .single-b-title h4,
.left-blog-details-style-6 .single-blog-content .single-b-title h4,
.right-blog-details-style-6 .single-blog-content .single-b-title h4{
    font-size: 16px;
}
.full-blog-details-style-6 .single-blog-content .date-edit-comments,
.left-blog-details-style-6 .single-blog-content .date-edit-comments,
.right-blog-details-style-6 .single-blog-content .date-edit-comments{
    margin-top: 23px;
}
.full-blog-details-style-6 .single-blog-content .date-edit-comments .blog-info-wrap,
.left-blog-details-style-6 .single-blog-content .date-edit-comments .blog-info-wrap,
.right-blog-details-style-6 .single-blog-content .date-edit-comments .blog-info-wrap {
    display: flex;
    flex-wrap: wrap;
    align-items: center;
    margin: -15px 0px 0px -30px;
}
.full-blog-details-style-6 .single-blog-content .date-edit-comments .blog-info-wrap span.blog-data,
.left-blog-details-style-6 .single-blog-content .date-edit-comments .blog-info-wrap span.blog-data,
.right-blog-details-style-6 .single-blog-content .date-edit-comments .blog-info-wrap span.blog-data {
    margin: 15px 0px 0px 30px;
}
.full-blog-details-style-6 .single-blog-content .date-edit-comments span.date,
.left-blog-details-style-6 .single-blog-content .date-edit-comments span.date,
.right-blog-details-style-6 .single-blog-content .date-edit-comments span.date{
    margin-right: 25px;
    display: flex;
    align-items: center;
    line-height: 1;
}
.full-blog-details-style-6 .single-blog-content .date-edit-comments span.date span.blog-d-n-c,
.left-blog-details-style-6 .single-blog-content .date-edit-comments span.date span.blog-d-n-c,
.right-blog-details-style-6 .single-blog-content .date-edit-comments span.date span.blog-d-n-c{
    margin-left: 5px;
}
.full-blog-details-style-6 .single-blog-content .date-edit-comments span.blog-edit,
.left-blog-details-style-6 .single-blog-content .date-edit-comments span.blog-edit,
.right-blog-details-style-6 .single-blog-content .date-edit-comments span.blog-edit{
    margin-right: 25px;
    display: flex;
    align-items: center;
    line-height: 1;
}
.full-blog-details-style-6 .single-blog-content .date-edit-comments span.blog-edit span.blog-d-n-c,
.left-blog-details-style-6 .single-blog-content .date-edit-comments span.blog-edit span.blog-d-n-c,
.right-blog-details-style-6 .single-blog-content .date-edit-comments span.blog-edit span.blog-d-n-c{
    margin-left: 5px;
}
.full-blog-details-style-6 .single-blog-content .date-edit-comments span.comments,
.left-blog-details-style-6 .single-blog-content .date-edit-comments span.comments,
.right-blog-details-style-6 .single-blog-content .date-edit-comments span.comments{
    display: flex;
    align-items: center;
    line-height: 1;
}
.full-blog-details-style-6 .single-blog-content .date-edit-comments span.comments span.blog-d-n-c,
.left-blog-details-style-6 .single-blog-content .date-edit-comments span.comments span.blog-d-n-c,
.right-blog-details-style-6 .single-blog-content .date-edit-comments span.comments span.blog-d-n-c{
    margin-left: 5px;
}
.full-blog-details-style-6 .single-blog-content .blog-description,
.left-blog-details-style-6 .single-blog-content .blog-description,
.right-blog-details-style-6 .single-blog-content .blog-description{
    margin-top: 24px;
}
.full-blog-details-style-6 .single-blog-content .blog-description p,
.left-blog-details-style-6 .single-blog-content .blog-description p,
.right-blog-details-style-6 .single-blog-content .blog-description p{
    margin-top: 4px;
    line-height: 22px;
}
.full-blog-details-style-6 .single-blog-content .blog-description .blog-image-description,
.left-blog-details-style-6 .single-blog-content .blog-description .blog-image-description,
.right-blog-details-style-6 .single-blog-content .blog-description .blog-image-description{
    margin-top: 24px;
}
.full-blog-details-style-6 .single-blog-content .blog-description .blog-image-description img,
.left-blog-details-style-6 .single-blog-content .blog-description .blog-image-description img,
.right-blog-details-style-6 .single-blog-content .blog-description .blog-image-description img{
    float: left;
    margin-right: 30px;
}
.full-blog-details-style-6 .single-blog-content .blog-description .blog-image-description p.bold-description,
.left-blog-details-style-6 .single-blog-content .blog-description .blog-image-description p.bold-description,
.right-blog-details-style-6 .single-blog-content .blog-description .blog-image-description p.bold-description{
    font-size: 14px;
    font-weight: 600;
}
.full-blog-details-style-6 .single-blog-content .blog-description p.color-description,
.left-blog-details-style-6 .single-blog-content .blog-description p.color-description,
.right-blog-details-style-6 .single-blog-content .blog-description p.color-description{
    background-color: #f7f7f7;
    font-size: 16px;
    color: #73841b;
    margin: 24px 0px;
    padding: 30px;
    border-left: 1px solid #ddd;
}
.full-blog-details-style-6 .single-blog-content .blog-info,
.left-blog-details-style-6 .single-blog-content .blog-info,
.right-blog-details-style-6 .single-blog-content .blog-info{
    padding: 30px;
    margin-top: 24px;
    background-color: #f7f7f7;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-direction: column;
}
.full-blog-details-style-6 .single-blog-content .blog-info i.fa-quote-left,
.left-blog-details-style-6 .single-blog-content .blog-info i.fa-quote-left,
.right-blog-details-style-6 .single-blog-content .blog-info i.fa-quote-left{
    font-size: 30px;
    color: #73841b;
}
.full-blog-details-style-6 .single-blog-content .blog-info h6,
.left-blog-details-style-6 .single-blog-content .blog-info h6,
.right-blog-details-style-6 .single-blog-content .blog-info h6{
    color: #73841b;
    font-size: 16px;
    margin-top: 9px;
    font-weight: 600;
}
.full-blog-details-style-6 .single-blog-content .last-video,
.right-blog-details-style-6 .single-blog-content .last-video,
.left-blog-details-style-6 .single-blog-content .last-video{
    margin-top: 30px;
    margin-right: -30px;
    display: flex;
    align-items: center;
    justify-content: space-between;
}
.full-blog-details-style-6 .single-blog-content .last-video .video,
.right-blog-details-style-6 .single-blog-content .last-video .video,
.left-blog-details-style-6 .single-blog-content .last-video .video{
    width: 100%;
    margin-right: 30px;
    line-height: 0;
}
.full-blog-details-style-6 .single-blog-content .last-video .video iframe,
.right-blog-details-style-6 .single-blog-content .last-video .video iframe,
.left-blog-details-style-6 .single-blog-content .last-video .video iframe{
    width: 100%;
}
.full-blog-details-style-6 .single-blog-content .b-link,
.left-blog-details-style-6 .single-blog-content .b-link,
.right-blog-details-style-6 .single-blog-content .b-link{
    margin-top: 30px;
}
.full-blog-details-style-6 .single-blog-content .b-link a,
.left-blog-details-style-6 .single-blog-content .b-link a,
.right-blog-details-style-6 .single-blog-content .b-link a{
    background-color: #73841b;
    padding: 5px 10px;
    color: #fff;
    border: 2px solid #73841b;
    border-radius: 4px;
}
.full-blog-details-style-6 .single-blog-content .b-link a:hover,
.left-blog-details-style-6 .single-blog-content .b-link a:hover,
.right-blog-details-style-6 .single-blog-content .b-link a:hover{
    background-color: transparent;
    color: #000;
    border-color: #73841b;
}
.full-blog-details-style-6 .single-blog-content .blog-social,
.left-blog-details-style-6 .single-blog-content .blog-social,
.right-blog-details-style-6 .single-blog-content .blog-social{
    margin-top: 30px;
    display: flex;
    align-items: center;
    justify-content: flex-start;
}
.full-blog-details-style-6 .single-blog-content .blog-social a.facebook,
.full-blog-details-style-6 .single-blog-content .blog-social a.twitter,
.full-blog-details-style-6 .single-blog-content .blog-social a.insta,
.full-blog-details-style-6 .single-blog-content .blog-social a.pinterest,
.left-blog-details-style-6 .single-blog-content .blog-social a.facebook,
.left-blog-details-style-6 .single-blog-content .blog-social a.twitter,
.left-blog-details-style-6 .single-blog-content .blog-social a.insta,
.left-blog-details-style-6 .single-blog-content .blog-social a.pinterest,
.right-blog-details-style-6 .single-blog-content .blog-social a.facebook,
.right-blog-details-style-6 .single-blog-content .blog-social a.twitter,
.right-blog-details-style-6 .single-blog-content .blog-social a.insta,
.right-blog-details-style-6 .single-blog-content .blog-social a.pinterest{
    width: 30px;
    height: 30px;
    margin-right: 7px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50px;
}
.full-blog-details-style-6 .single-blog-content .blog-social a,
.left-blog-details-style-6 .single-blog-content .blog-social a,
.right-blog-details-style-6 .single-blog-content .blog-social a{
    background-color: #f7f7f7;
    color: #222;
}
.full-blog-details-style-6 .single-blog-content .blog-social a:hover,
.left-blog-details-style-6 .single-blog-content .blog-social a:hover,
.right-blog-details-style-6 .single-blog-content .blog-social a:hover{
    background-color: #73841b;
    color: #fff;
}
.full-blog-details-style-6 .single-blog-content .blog-comments,
.left-blog-details-style-6 .single-blog-content .blog-comments,
.right-blog-details-style-6 .single-blog-content .blog-comments{
    margin-top: 25px;
}
.full-blog-details-style-6 .single-blog-content .blog-comments h4,
.left-blog-details-style-6 .single-blog-content .blog-comments h4,
.right-blog-details-style-6 .single-blog-content .blog-comments h4{
    font-size: 20px;
}
.full-blog-details-style-6 .single-blog-content .blog-comments h4 span,
.left-blog-details-style-6 .single-blog-content .blog-comments h4 span,
.right-blog-details-style-6 .single-blog-content .blog-comments h4 span{
    color: #73841b;
}
.full-blog-details-style-6 .single-blog-content .blog-comments .blog-comment-info,
.left-blog-details-style-6 .single-blog-content .blog-comments .blog-comment-info,
.right-blog-details-style-6 .single-blog-content .blog-comments .blog-comment-info {
    margin-top: 23px;
    padding-top: 30px;
    border-top: 1px solid #eee;
}
.full-blog-details-style-6 .single-blog-content .blog-comments .blog-comment-info ul.comments-arae,
.left-blog-details-style-6 .single-blog-content .blog-comments .blog-comment-info ul.comments-arae,
.right-blog-details-style-6 .single-blog-content .blog-comments .blog-comment-info ul.comments-arae {
    display: flex;
    margin-top: 24px;
}
.full-blog-details-style-6 .single-blog-content .blog-comments .blog-comment-info ul.comments-arae:first-child,
.left-blog-details-style-6 .single-blog-content .blog-comments .blog-comment-info ul.comments-arae:first-child,
.right-blog-details-style-6 .single-blog-content .blog-comments .blog-comment-info ul.comments-arae:first-child {
    margin-top: 0px;
}
.full-blog-details-style-6 .single-blog-content .blog-comments .blog-comment-info ul.comments-arae.comment-reply,
.left-blog-details-style-6 .single-blog-content .blog-comments .blog-comment-info ul.comments-arae.comment-reply,
.right-blog-details-style-6 .single-blog-content .blog-comments .blog-comment-info ul.comments-arae.comment-reply {
    padding-left: 50px;
}
.full-blog-details-style-6 .single-blog-content .blog-comments .blog-comment-info ul.comments-arae.all-reply,
.left-blog-details-style-6 .single-blog-content .blog-comments .blog-comment-info ul.comments-arae.all-reply,
.right-blog-details-style-6 .single-blog-content .blog-comments .blog-comment-info ul.comments-arae.all-reply {
    margin-top: 24px;
    padding-top: 30px;
    border-top: 1px solid #eee;
}
.full-blog-details-style-6 .single-blog-content .blog-comments .blog-comment-info ul.comments-arae li.comments-man,
.left-blog-details-style-6 .single-blog-content .blog-comments .blog-comment-info ul.comments-arae li.comments-man,
.right-blog-details-style-6 .single-blog-content .blog-comments .blog-comment-info ul.comments-arae li.comments-man{
    width: 45px;
    height: 45px;
    background-color: #73841b;
    color: #fff;
    margin-right: 15px;
    font-size: 15px;
    border-radius: 3px;
    display: flex;
    align-items: center;
    text-align: center;
    justify-content: center;
    font-weight: 600;
}
.full-blog-details-style-6 .single-blog-content .blog-comments .blog-comment-info ul.comments-arae li.comments-content,
.left-blog-details-style-6 .single-blog-content .blog-comments .blog-comment-info ul.comments-arae li.comments-content,
.right-blog-details-style-6 .single-blog-content .blog-comments .blog-comment-info ul.comments-arae li.comments-content {
    width: calc(100% - 45px);
}
.full-blog-details-style-6 .single-blog-content .blog-comments .blog-comment-info ul.comments-arae li.comments-content span.comments-result,
.left-blog-details-style-6 .single-blog-content .blog-comments .blog-comment-info ul.comments-arae li.comments-content span.comments-result,
.right-blog-details-style-6 .single-blog-content .blog-comments .blog-comment-info ul.comments-arae li.comments-content span.comments-result{
    display: block;
}
.full-blog-details-style-6 .single-blog-content .blog-comments .blog-comment-info ul.comments-arae li.comments-content span.comment-name,
.left-blog-details-style-6 .single-blog-content .blog-comments .blog-comment-info ul.comments-arae li.comments-content span.comment-name,
.right-blog-details-style-6 .single-blog-content .blog-comments .blog-comment-info ul.comments-arae li.comments-content span.comment-name{
    margin: 5px 0px;
}
.full-blog-details-style-6 .single-blog-content .blog-comments .blog-comment-info ul.comments-arae li.comments-content span.comment-name i,
.left-blog-details-style-6 .single-blog-content .blog-comments .blog-comment-info ul.comments-arae li.comments-content span.comment-name i,
.right-blog-details-style-6 .single-blog-content .blog-comments .blog-comment-info ul.comments-arae li.comments-content span.comment-name i{
    font-style: normal;
}
.full-blog-details-style-6 .single-blog-content .blog-comments .blog-comment-info ul.comments-arae li.comments-content span.comments-result.c-date,
.left-blog-details-style-6 .single-blog-content .blog-comments .blog-comment-info ul.comments-arae li.comments-content span.comments-result.c-date,
.right-blog-details-style-6 .single-blog-content .blog-comments .blog-comment-info ul.comments-arae li.comments-content span.comments-result.c-date{
    font-weight: 600;
}
.full-blog-details-style-6 .single-blog-content .blog-comments .blog-comment-info ul.comments-arae li.comments-content span.comments-result.c-date a.Reply,
.left-blog-details-style-6 .single-blog-content .blog-comments .blog-comment-info ul.comments-arae li.comments-content span.comments-result.c-date a.Reply,
.right-blog-details-style-6 .single-blog-content .blog-comments .blog-comment-info ul.comments-arae li.comments-content span.comments-result.c-date a.Reply {
    color: #73841b;
    margin-left: 30px;
    font-weight: 500;
}
.full-blog-details-style-6 .single-blog-content .blog-comments .blog-comment-info ul.comments-arae li.comments-content span span.comments-title,
.left-blog-details-style-6 .single-blog-content .blog-comments .blog-comment-info ul.comments-arae li.comments-content span span.comments-title,
.right-blog-details-style-6 .single-blog-content .blog-comments .blog-comment-info ul.comments-arae li.comments-content span span.comments-title{
    font-weight: 600;
    color: #73841b;
}
.full-blog-details-style-6 .single-blog-content .comments-form,
.left-blog-details-style-6 .single-blog-content .comments-form,
.right-blog-details-style-6 .single-blog-content .comments-form{
    margin-top: 20px;
}
.full-blog-details-style-6 .single-blog-content .comments-form h4,
.left-blog-details-style-6 .single-blog-content .comments-form h4,
.right-blog-details-style-6 .single-blog-content .comments-form h4{
    font-size: 18px;
}
.full-blog-details-style-6 .single-blog-content .comments-form form label,
.left-blog-details-style-6 .single-blog-content .comments-form form label,
.right-blog-details-style-6 .single-blog-content .comments-form form label{
    margin-top: 15px;
    margin-bottom: 5px;
}
.full-blog-details-style-6 .single-blog-content .comments-form form input,
.left-blog-details-style-6 .single-blog-content .comments-form form input,
.right-blog-details-style-6 .single-blog-content .comments-form form input{
    width: 100%;
    padding: 10px 15px;
    border: 1px solid #eee;
    border-radius: 3px;
}
.full-blog-details-style-6 .single-blog-content .comments-form form input:focus,
.left-blog-details-style-6 .single-blog-content .comments-form form input:focus,
.right-blog-details-style-6 .single-blog-content .comments-form form input:focus{
    border-color: #73841b;
}
.full-blog-details-style-6 .single-blog-content .comments-form form textarea,
.left-blog-details-style-6 .single-blog-content .comments-form form textarea,
.right-blog-details-style-6 .single-blog-content .comments-form form textarea{
    width: 100%;
    min-height: 100px;
    padding: 10px 15px;
    border: 1px solid #eee;
    border-radius: 3px;
    resize: unset;
}
.full-blog-details-style-6 .single-blog-content .comments-form form textarea:focus,
.left-blog-details-style-6 .single-blog-content .comments-form form textarea:focus,
.right-blog-details-style-6 .single-blog-content .comments-form form textarea:focus{
    border-color: #73841b;
}
.full-blog-details-style-6 .single-blog-content .comments-form a.btn-style1,
.left-blog-details-style-6 .single-blog-content .comments-form a.btn-style1,
.right-blog-details-style-6 .single-blog-content .comments-form a.btn-style1{
    margin-top: 15px;
}
.full-blog-details-style-6 .single-blog-content .blog-comments,
.left-blog-details-style-6 .single-blog-content .blog-comments,
.right-blog-details-style-6 .single-blog-content .blog-comments{
    margin-top: 23px;
}
.full-blog-details-style-6 .single-blog-content .blog-comments h4,
.left-blog-details-style-6 .single-blog-content .blog-comments h4,
.right-blog-details-style-6 .single-blog-content .blog-comments h4{
    font-size: 18px;
}
.full-blog-details-style-6 .single-blog-content .blog-comments h4 span,
.left-blog-details-style-6 .single-blog-content .blog-comments h4 span,
.right-blog-details-style-6 .single-blog-content .blog-comments h4 span{
    color: #73841b;
}
/* Center blog css */
.cetner-blog-style-6{
    display: flex;
    flex-wrap: wrap;
}
.cetner-blog-style-6 .blog-start{
    width: 100%;
    margin-top: 30px;
}
.cetner-blog-style-6 .blog-start:nth-child(1){
    margin-top: 0px;
}
.cetner-blog-style-6 .blog-start .blog-image{
    position: relative;
    display: flex;
}
.cetner-blog-style-6 .blog-start .blog-image a{
    position: relative;
    overflow: hidden;
}
.cetner-blog-style-6 .blog-start:hover .blog-image a img{
    -webkit-transform: scale(1.06);
    -o-transform: scale(1.06);
    transform: scale(1.06);
}
.cetner-blog-style-6 .blog-start .blog-image a img,
.cetner-blog-style-6 .blog-start:hover .blog-image a img{
    -webkit-transition: all 0.3s ease-in-out 0s;
    -o-transition: all 0.3s ease-in-out 0s;
    transition: all 0.3s ease-in-out 0s;
}
.cetner-blog-style-6 .blog-start .blog-image .image-link{
    position: absolute;
    bottom: 10px;
    left: 15px;
}
.cetner-blog-style-6 .blog-start .blog-image .image-link a{
    background-color: #73841b;
    color: #fff;
    padding: 3px 8px;
    font-size: 14px;
    border-radius: 3px;
    text-transform: uppercase;
}
.cetner-blog-style-6 .blog-start .blog-image .image-link a:hover{
    background-color: #000;
}
.cetner-blog-style-6 .blog-start .blog-content {
    padding-top: 23px;
}
.cetner-blog-style-6 .blog-start .blog-content .blog-title h6{
    font-size: 16px;
}
.cetner-blog-style-6 .blog-start .blog-content .blog-title h6 a{
    display: block;
    width: 100%;
    white-space: nowrap;
    text-overflow: ellipsis;
    overflow: hidden;
}
.cetner-blog-style-6 .blog-start .blog-content .blog-title h6 a:hover{
    color: #73841b;
}
.cetner-blog-style-6 .blog-start .blog-content p.blog-description{
    color: #999;
    margin-top: 16px;
    font-size: 14px;
}
.cetner-blog-style-6 .blog-start .blog-content .more-blog{
    margin-top: 17px;
    display: flex;
    align-items: center;
    justify-content: space-between;
}
.cetner-blog-style-6 .blog-start .blog-content .more-blog a.read-link{
    color: #73841b;
    font-size: 14px;
    display: flex;
    align-items: center;
}
.cetner-blog-style-6 .blog-start .blog-content .more-blog a.read-link i{
    font-size: 12px;
    padding-left: 5px;
}
.cetner-blog-style-6 .blog-start .blog-content .more-blog a.read-link i,
.cetner-blog-style-6 .blog-start .blog-content .more-blog a.read-link:hover i{
    -webkit-transition: all 0.3s ease-in-out 0s;
    -o-transition: all 0.3s ease-in-out 0s;
    transition: all 0.3s ease-in-out 0s;
}
.cetner-blog-style-6 .blog-start .blog-content .more-blog a.read-link:hover i{
    margin-left: 8px;
}
/* style-7 right column css */
.right-area .b-Reply form a.Reply-link{
    background-color: #5fa800;
    color: #fff;
    width: 100%;
    padding: 8px 15px;
    margin-top: 30px;
    text-align: center;
    font-weight: 500;
    border: 2px solid #5fa800;
}
.right-area .b-Reply form a.Reply-link:hover{
    background-color: transparent;
    color: #000;
    border-color: #5fa800;
}
.center-right-area .right-column-start .archive-link ul li a:hover{
    color: #cd7752;
}
.left-column .left-blog .blog-item .l-blog-caption h4 a:hover{
    color: #cd7752;
}
/* style-7 center blog right column css */
.center-right-area .b-Reply form a.Reply-link{
    background-color: #cd7752;
    color: #fff;
    width: 100%;
    padding: 8px 15px;
    margin-top: 30px;
    text-align: center;
    font-weight: 500;
    border: 2px solid #cd7752;
}
.center-right-area .b-Reply form a.Reply-link:hover{
    background-color: transparent;
    color: #000;
    border-color: #cd7752;
}
/* style-7 full-left-right grid blog css */
.blog-style-7-3-grid,
.blog-style-7-left-grid-blog,
.blog-style-7-right-grid-blog{
    display: flex;
    flex-wrap: wrap;
    margin-top: -30px;
    margin-left: -30px;
}
.blog-style-7-3-grid .blog-start,
.blog-style-7-left-grid-blog .blog-start,
.blog-style-7-right-grid-blog .blog-start{
    width: calc(33.33% - 30px);
    margin-left: 30px;
    margin-top: 30px;
}
.blog-style-7-3-grid .blog-start .blog-image,
.blog-style-7-left-grid-blog .blog-start .blog-image,
.blog-style-7-right-grid-blog .blog-start .blog-image{
    position: relative;
    display: flex;
}
.blog-style-7-3-grid .blog-start .blog-image a,
.blog-style-7-left-grid-blog .blog-start .blog-image a,
.blog-style-7-right-grid-blog .blog-start .blog-image a{
    position: relative;
    overflow: hidden;
}
.blog-style-7-3-grid .blog-start .blog-image a img,
.blog-style-7-left-grid-blog .blog-start .blog-image a img,
.blog-style-7-right-grid-blog .blog-start .blog-image a img{
    height: 255px;
    object-fit: cover;
}
.blog-style-7-3-grid .blog-start:hover .blog-image a img,
.blog-style-7-left-grid-blog .blog-start:hover .blog-image a img,
.blog-style-7-right-grid-blog .blog-start:hover .blog-image a img{
    -webkit-transform: scale(1.1);
    -o-transform: scale(1.1);
    transform: scale(1.1);
}
.blog-style-7-3-grid .blog-start .blog-image a img,
.blog-style-7-3-grid .blog-start:hover .blog-image a img,
.blog-style-7-left-grid-blog .blog-start .blog-image a img,
.blog-style-7-left-grid-blog .blog-start:hover .blog-image a img,
.blog-style-7-right-grid-blog .blog-start .blog-image a img,
.blog-style-7-right-grid-blog .blog-start:hover .blog-image a img{
    -webkit-transition: all 0.3s ease-in-out 0s;
    -o-transition: all 0.3s ease-in-out 0s;
    transition: all 0.3s ease-in-out 0s;
}
.blog-style-7-3-grid .blog-start .blog-content .blog-title h6,
.blog-style-7-left-grid-blog .blog-start .blog-content .blog-title h6,
.blog-style-7-right-grid-blog .blog-start .blog-content .blog-title h6{
    font-size: 16px;
    padding-top: 23px;
}
.blog-style-7-3-grid .blog-start .blog-content .blog-title h6 a,
.blog-style-7-left-grid-blog .blog-start .blog-content .blog-title h6 a,
.blog-style-7-right-grid-blog .blog-start .blog-content .blog-title h6 a{
    display: block;
    width: 100%;
    white-space: nowrap;
    text-overflow: ellipsis;
    overflow: hidden;
}
.blog-style-7-3-grid .blog-start .blog-content .blog-title h6 a:hover,
.blog-style-7-left-grid-blog .blog-start .blog-content .blog-title h6 a:hover,
.blog-style-7-right-grid-blog .blog-start .blog-content .blog-title h6 a:hover{
    color: #cd7752;
}
.blog-style-7-3-grid .blog-start .blog-content p.blog-description,
.blog-style-7-left-grid-blog .blog-start .blog-content p.blog-description,
.blog-style-7-right-grid-blog .blog-start .blog-content p.blog-description{
    color: #999;
    margin-top: 16px;
}
.blog-style-7-3-grid .blog-start .blog-content .more-blog,
.blog-style-7-left-grid-blog .blog-start .blog-content .more-blog,
.blog-style-7-right-grid-blog .blog-start .blog-content .more-blog{
    margin-top: 17px;
    display: flex;
    align-items: center;
    justify-content: space-between;
}
.blog-style-7-3-grid .blog-start .blog-content .more-blog a.read-link,
.blog-style-7-left-grid-blog .blog-start .blog-content .more-blog a.read-link,
.blog-style-7-right-grid-blog .blog-start .blog-content .more-blog a.read-link{
    color: #cd7752;
    font-size: 14px;
    display: flex;
    align-items: center;
}
.blog-style-7-3-grid .blog-start .blog-content .more-blog a.read-link i,
.blog-style-7-left-grid-blog .blog-start .blog-content .more-blog a.read-link i,
.blog-style-7-right-grid-blog .blog-start .blog-content .more-blog a.read-link i{
    font-size: 12px;
    padding-left: 5px;
}
.blog-style-7-3-grid .blog-start .blog-content .more-blog a.read-link i,
.blog-style-7-3-grid .blog-start .blog-content .more-blog a.read-link:hover i,
.blog-style-7-left-grid-blog .blog-start .blog-content .more-blog a.read-link i,
.blog-style-7-left-grid-blog .blog-start .blog-content .more-blog a.read-link:hover i,
.blog-style-7-right-grid-blog .blog-start .blog-content .more-blog a.read-link i,
.blog-style-7-right-grid-blog .blog-start .blog-content .more-blog a.read-link:hover i{
    -webkit-transition: all 0.3s ease-in-out 0s;
    -o-transition: all 0.3s ease-in-out 0s;
    transition: all 0.3s ease-in-out 0s;
}
.blog-style-7-3-grid .blog-start .blog-content .more-blog a.read-link:hover i,
.blog-style-7-left-grid-blog .blog-start .blog-content .more-blog a.read-link:hover i,
.blog-style-7-right-grid-blog .blog-start .blog-content .more-blog a.read-link:hover i{
    margin-left: 8px;
}
/* pagination css */
.all-page .page-number a:after{
    content: "";
    position: absolute;
    bottom: 0px;
    left: 1px;
    right: 0px;
    width: 4px;
    height: 4px;
    border-radius: 100%;
    opacity: 0;
    visibility: hidden;
}
.all-page .page-number.style-1 a:after,
.all-page .page-number.style-5 a:after{
    background-color: #ec6504;
}
.all-page .page-number.style-2 a:after,
.all-page .page-number.style-3 a:after{
    background-color: #5fa800;
}
.all-page .page-number.style-6 a:after{
    background-color: #73841b;
}
.all-page .page-number.style-7 a:after{
    background-color: #cd7752;
}
.all-page .page-number a:hover:after,
.all-page .page-number a.active:after{
    opacity: 1;
    visibility: visible;
}
.all-page .page-number.style-1 a:hover,
.all-page .page-number.style-1 a.active,
.all-page .page-number.style-5 a:hover,
.all-page .page-number.style-5 a.active{
    color: #ec6504;
}
.all-page .page-number.style-2 a:hover,
.all-page .page-number.style-2 a.active,
.all-page .page-number.style-3 a:hover,
.all-page .page-number.style-3 a.active{
    color: #5fa800;
}
.all-page .page-number.style-7 a:hover,
.all-page .page-number.style-7 a.active{
    color: #cd7752;
}
.all-page .page-number a:last-child:after{
    display: none;
}
/* style-7 full-left-right list blog css */
.full-blog-list-style-7 .blog-start,
.blog-style-7-left-list-blog .blog-start,
.blog-style-7-right-list-blog .blog-start{
    width: 100%;
    margin-bottom: 20px;
    padding-bottom: 20px;
    display: flex;
    align-items: center;
    border-bottom: 1px solid #eee;
}
.full-blog-list-style-7 .blog-start .blog-image{
    width: 42%;
}
.blog-style-7-left-list-blog .blog-start .blog-image,
.blog-style-7-right-list-blog .blog-start .blog-image{
    width: 50%;
}
.full-blog-list-style-7 .blog-start .blog-image a,
.blog-style-7-left-list-blog .blog-start .blog-image a,
.blog-style-7-right-list-blog .blog-start .blog-image a{
    position: relative;
    overflow: hidden;
    display: block;
}
.full-blog-list-style-7 .blog-start .blog-image a img,
.blog-style-7-left-list-blog .blog-start .blog-image a img,
.blog-style-7-right-list-blog .blog-start .blog-image a img{
    width: 100%;
    height: 255px;
    object-fit: cover;
}
.full-blog-list-style-7 .blog-start:hover .blog-image a img,
.blog-style-7-left-list-blog .blog-start:hover .blog-image a img,
.blog-style-7-right-list-blog .blog-start:hover .blog-image a img{
    -webkit-transform: scale(1.1);
    -o-transform: scale(1.1);
    transform: scale(1.1);
}
.full-blog-list-style-7 .blog-start .blog-image a img,
.full-blog-list-style-7 .blog-start:hover .blog-image a img,
.blog-style-7-left-list-blog .blog-start .blog-image a img,
.blog-style-7-left-list-blog .blog-start:hover .blog-image a img,
.blog-style-7-right-list-blog .blog-start .blog-image a img,
.blog-style-7-right-list-blog .blog-start:hover .blog-image a img{
    -webkit-transition: all 0.3s ease-in-out 0s;
    -o-transition: all 0.3s ease-in-out 0s;
    transition: all 0.3s ease-in-out 0s;
}
.full-blog-list-style-7 .blog-start .blog-content{
    width: calc(58% - 30px);
    margin-left: 30px;
}
.blog-style-7-left-list-blog .blog-start .blog-content,
.blog-style-7-right-list-blog .blog-start .blog-content{
    width: calc(50% - 30px);
    margin-left: 30px;
}
.full-blog-list-style-7 .blog-start .blog-content .blog-title h6,
.blog-style-7-left-list-blog .blog-start .blog-content .blog-title h6,
.blog-style-7-right-grid-blog .blog-start .blog-content .blog-title h6{
    font-size: 16px;
}
.full-blog-list-style-7 .blog-start .blog-content .blog-title h6 a,
.blog-style-7-left-list-blog .blog-start .blog-content .blog-title h6 a,
.blog-style-7-right-list-blog .blog-start .blog-content .blog-title h6 a{
    display: block;
    width: 100%;
    white-space: nowrap;
    text-overflow: ellipsis;
    overflow: hidden;
}
.full-blog-list-style-7 .blog-start .blog-content .blog-title h6 a:hover,
.blog-style-7-left-list-blog .blog-start .blog-content .blog-title h6 a:hover,
.blog-style-7-right-list-blog .blog-start .blog-content .blog-title h6 a:hover{
    color: #cd7752;
}
.full-blog-list-style-7 .blog-start .blog-content p.blog-description,
.blog-style-7-left-list-blog .blog-start .blog-content p.blog-description,
.blog-style-7-right-list-blog .blog-start .blog-content p.blog-description{
    color: #999;
    margin-top: 16px;
}
.full-blog-list-style-7 .blog-start .blog-content .more-blog,
.blog-style-7-left-list-blog .blog-start .blog-content .more-blog,
.blog-style-7-right-list-blog .blog-start .blog-content .more-blog{
    margin-top: 17px;
    display: flex;
    align-items: center;
    justify-content: space-between;
}
.full-blog-list-style-7 .blog-start .blog-content .more-blog a.read-link,
.blog-style-7-left-list-blog .blog-start .blog-content .more-blog a.read-link,
.blog-style-7-right-list-blog .blog-start .blog-content .more-blog a.read-link{
    color: #cd7752;
    font-size: 14px;
}
.full-blog-list-style-7 .blog-start .blog-content .more-blog a.read-link i,
.blog-style-7-left-list-blog .blog-start .blog-content .more-blog a.read-link i,
.blog-style-7-right-list-blog .blog-start .blog-content .more-blog a.read-link i{
    font-size: 12px;
}
.full-blog-list-style-7 .blog-start .blog-content .more-blog a.read-link i,
.full-blog-list-style-7 .blog-start .blog-content .more-blog a.read-link:hover i,
.blog-style-7-left-list-blog .blog-start .blog-content .more-blog a.read-link i,
.blog-style-7-left-list-blog .blog-start .blog-content .more-blog a.read-link:hover i,
.blog-style-7-right-list-blog .blog-start .blog-content .more-blog a.read-link i,
.blog-style-7-right-list-blog .blog-start .blog-content .more-blog a.read-link:hover i{
    -webkit-transition: all 0.3s ease-in-out 0s;
    -o-transition: all 0.3s ease-in-out 0s;
    transition: all 0.3s ease-in-out 0s;
}
.full-blog-list-style-7 .blog-start .blog-content .more-blog a.read-link:hover i,
.blog-style-7-left-list-blog .blog-start .blog-content .more-blog a.read-link:hover i,
.blog-style-7-right-list-blog .blog-start .blog-content .more-blog a.read-link:hover i{
    margin-left: 8px;
}
/* style-7 full-left-right blog details css */
.full-blog-details-style-7 .single-blog-content,
.left-blog-details-style7 .single-blog-content,
.right-blog-details-style7 .single-blog-content{
    margin-top: 33px;
}
.full-blog-details-style-7 .single-blog-content .single-b-title h4,
.left-blog-details-style7 .single-blog-content .single-b-title h4,
.right-blog-details-style7 .single-blog-content .single-b-title h4{
    font-size: 16px;
}
.full-blog-details-style-7 .single-blog-content .date-edit-comments,
.left-blog-details-style7 .single-blog-content .date-edit-comments,
.right-blog-details-style7 .single-blog-content .date-edit-comments {
    margin-top: 23px;
}
.full-blog-details-style-7 .single-blog-content .date-edit-comments .blog-info-wrap,
.left-blog-details-style7 .single-blog-content .date-edit-comments .blog-info-wrap,
.right-blog-details-style7 .single-blog-content .date-edit-comments .blog-info-wrap {
    display: flex;
    flex-wrap: wrap;
    align-items: center;
    margin: -15px 0px 0px -30px;
}
.full-blog-details-style-7 .single-blog-content .date-edit-comments .blog-info-wrap span.blog-data,
.left-blog-details-style7 .single-blog-content .date-edit-comments .blog-info-wrap span.blog-data,
.right-blog-details-style7 .single-blog-content .date-edit-comments .blog-info-wrap span.blog-data  {
    margin: 15px 0px 0px 30px;
    display: flex;
    align-items: center;
    line-height: 1;
}
.full-blog-details-style-7 .single-blog-content .date-edit-comments .blog-info-wrap span.blog-data i,
.left-blog-details-style7 .single-blog-content .date-edit-comments .blog-info-wrap span.blog-data i,
.right-blog-details-style7 .single-blog-content .date-edit-comments .blog-info-wrap span.blog-data i {
    margin-right: 5px;
}
.full-blog-details-style-7 .single-blog-content .blog-description,
.left-blog-details-style7 .single-blog-content .blog-description,
.right-blog-details-style7 .single-blog-content .blog-description{
    margin-top: 23px;
}
.full-blog-details-style-7 .single-blog-content .blog-description p,
.left-blog-details-style7 .single-blog-content .blog-description p,
.right-blog-details-style7 .single-blog-content .blog-description p{
    margin-top: 5px;
}
.full-blog-details-style-7 .single-blog-content .blog-description .blog-image-description,
.left-blog-details-style7 .single-blog-content .blog-description .blog-image-description,
.right-blog-details-style7 .single-blog-content .blog-description .blog-image-description{
    margin-top: 7px;
}
.full-blog-details-style-7 .single-blog-content .blog-description .blog-image-description img,
.left-blog-details-style7 .single-blog-content .blog-description .blog-image-description img,
.right-blog-details-style7 .single-blog-content .blog-description .blog-image-description img{
    float: left;
    margin-right: 15px;
}
.full-blog-details-style-7 .single-blog-content .blog-description .blog-image-description p.bold-description,
.left-blog-details-style7 .single-blog-content .blog-description .blog-image-description p.bold-description,
.right-blog-details-style7 .single-blog-content .blog-description .blog-image-description p.bold-description{
    font-size: 15px;
    font-weight: 700;
}
.full-blog-details-style-7 .single-blog-content .blog-description p.color-description,
.left-blog-details-style7 .single-blog-content .blog-description p.color-description,
.right-blog-details-style7 .single-blog-content .blog-description p.color-description{
    background-color: #f7f7f7;
    font-size: 16px;
    color: #cd7752;
    margin: 22px 0px;
    padding: 30px;
    border-left: 1px solid #ddd;
}
.full-blog-details-style-7 .single-blog-content .blog-img,
.left-blog-details-style7 .single-blog-content .blog-img,
.right-blog-details-style7 .single-blog-content .blog-img{
    margin-left: -30px;
    margin-top: 22px;
    display: flex;
}
.full-blog-details-style-7 .single-blog-content .blog-img .b-image1,
.left-blog-details-style7 .single-blog-content .blog-img .b-image1,
.right-blog-details-style7 .single-blog-content .blog-img .b-image1{
    width: calc(50% - 30px);
    margin-left: 30px;
    line-height: 0;
}
.full-blog-details-style-7 .single-blog-content .blog-img .b-image1 a,
.left-blog-details-style7 .single-blog-content .blog-img .b-image1 a,
.right-blog-details-style7 .single-blog-content .blog-img .b-image1 a{
    position: relative;
    overflow: hidden;
}
.full-blog-details-style-7 .single-blog-content .blog-img .b-image1 a img,
.left-blog-details-style7 .single-blog-content .blog-img .b-image1 a img,
.right-blog-details-style7 .single-blog-content .blog-img .b-image1 a img{
    -webkit-transition: all 0.3s ease-in-out 0s;
    -o-transition: all 0.3s ease-in-out 0s;
    transition: all 0.3s ease-in-out 0s;
}
.full-blog-details-style-7 .single-blog-content .blog-img .b-image1 a img:hover,
.left-blog-details-style7 .single-blog-content .blog-img .b-image1 a img:hover,
.right-blog-details-style7 .single-blog-content .blog-img .b-image1 a img:hover{
    transform: scale(1.1);
    -webkit-transition: all 0.3s ease-in-out 0s;
    -o-transition: all 0.3s ease-in-out 0s;
    transition: all 0.3s ease-in-out 0s;
}
.full-blog-details-style-7 .single-blog-content .blog-info,
.left-blog-details-style7 .single-blog-content .blog-info,
.right-blog-details-style7 .single-blog-content .blog-info{
    padding: 30px;
    margin-top: 30px;
    background-color: #f7f7f7;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-direction: column;
}
.full-blog-details-style-7 .single-blog-content .blog-info i.fa-quote-left,
.left-blog-details-style7 .single-blog-content .blog-info i.fa-quote-left,
.right-blog-details-style7 .single-blog-content .blog-info i.fa-quote-left{
    font-size: 30px;
    color: #cd7752;
}
.full-blog-details-style-7 .single-blog-content .blog-info h6,
.left-blog-details-style7 .single-blog-content .blog-info h6,
.right-blog-details-style7 .single-blog-content .blog-info h6{
    color: #cd7752;
    font-size: 16px;
    margin-top: 9px;
    font-weight: 600;
}
.full-blog-details-style-7 .single-blog-content .b-link,
.left-blog-details-style7 .single-blog-content .b-link,
.right-blog-details-style7 .single-blog-content .b-link{
    margin-top: 30px;
}
.full-blog-details-style-7 .single-blog-content .b-link a,
.left-blog-details-style7 .single-blog-content .b-link a,
.right-blog-details-style7 .single-blog-content .b-link a{
    background-color: #cd7752;
    padding: 5px 10px;
    color: #fff;
    border: 2px solid #cd7752;
    border-radius: 4px;
}
.full-blog-details-style-7 .single-blog-content .b-link a:hover,
.left-blog-details-style7 .single-blog-content .b-link a:hover,
.right-blog-details-style7 .single-blog-content .b-link a:hover{
    background-color: transparent;
    color: #000;
    border-color: #cd7752;
}
.full-blog-details-style-7 .single-blog-content .blog-social,
.left-blog-details-style7 .single-blog-content .blog-social,
.right-blog-details-style7 .single-blog-content .blog-social{
    margin-top: 30px;
    display: flex;
    align-items: center;
    justify-content: flex-start;
}
.full-blog-details-style-7 .single-blog-content .blog-social a.facebook,
.full-blog-details-style-7 .single-blog-content .blog-social a.twitter,
.full-blog-details-style-7 .single-blog-content .blog-social a.insta,
.full-blog-details-style-7 .single-blog-content .blog-social a.pinterest,
.left-blog-details-style7 .single-blog-content .blog-social a.facebook,
.left-blog-details-style7 .single-blog-content .blog-social a.twitter,
.left-blog-details-style7 .single-blog-content .blog-social a.insta,
.left-blog-details-style7 .single-blog-content .blog-social a.pinterest,
.right-blog-details-style7 .single-blog-content .blog-social a.facebook,
.right-blog-details-style7 .single-blog-content .blog-social a.twitter,
.right-blog-details-style7 .single-blog-content .blog-social a.insta,
.right-blog-details-style7 .single-blog-content .blog-social a.pinterest{
    width: 30px;
    height: 30px;
    margin-right: 7px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50px;
}
.full-blog-details-style-7 .single-blog-content .blog-social a,
.left-blog-details-style7 .single-blog-content .blog-social a,
.right-blog-details-style7 .single-blog-content .blog-social a{
    background-color: #cd7752;
    color: #222;
}
.full-blog-details-style-7 .single-blog-content .blog-social a:hover,
.left-blog-details-style7 .single-blog-content .blog-social a:hover,
.right-blog-details-style7 .single-blog-content .blog-social a:hover{
    background-color: #cd7752;
    color: #fff;
}
.full-blog-details-style-7 .single-blog-content .blog-comments,
.left-blog-details-style7 .single-blog-content .blog-comments,
.right-blog-details-style7 .single-blog-content .blog-comments{
    margin-top: 23px;
}
.full-blog-details-style-7 .single-blog-content .blog-comments h4,
.left-blog-details-style7 .single-blog-content .blog-comments h4,
.right-blog-details-style7 .single-blog-content .blog-comments h4{
    font-size: 18px;
}
.full-blog-details-style-7 .single-blog-content .blog-comments h4 span,
.left-blog-details-style7 .single-blog-content .blog-comments h4 span,
.right-blog-details-style7 .single-blog-content .blog-comments h4 span{
    color: #cd7752;
}
.full-blog-details-style-7 .single-blog-content .blog-comments .blog-comment-info,
.left-blog-details-style7 .single-blog-content .blog-comments .blog-comment-info,
.right-blog-details-style7 .single-blog-content .blog-comments .blog-comment-info {
    margin-top: 23px;
    padding-top: 30px;
    border-top: 1px solid #eee;
}
.full-blog-details-style-7 .single-blog-content .blog-comments .blog-comment-info ul.comments-arae,
.left-blog-details-style7 .single-blog-content .blog-comments .blog-comment-info ul.comments-arae,
.right-blog-details-style7 .single-blog-content .blog-comments .blog-comment-info ul.comments-arae {
    margin-top: 24px;
    display: flex;
}
.full-blog-details-style-7 .single-blog-content .blog-comments .blog-comment-info ul.comments-arae:first-child,
.left-blog-details-style7 .single-blog-content .blog-comments .blog-comment-info ul.comments-arae:first-child,
.right-blog-details-style7 .single-blog-content .blog-comments .blog-comment-info ul.comments-arae:first-child {
    margin-top: 0px;
}
.full-blog-details-style-7 .single-blog-content .blog-comments .blog-comment-info ul.comments-arae.comment-reply,
.left-blog-details-style7 .single-blog-content .blog-comments .blog-comment-info ul.comments-arae.comment-reply,
.right-blog-details-style7 .single-blog-content .blog-comments .blog-comment-info ul.comments-arae.comment-reply {
    padding-left: 50px;
}
.full-blog-details-style-7 .single-blog-content .blog-comments .blog-comment-info ul.comments-arae.all-reply,
.left-blog-details-style7 .single-blog-content .blog-comments .blog-comment-info ul.comments-arae.all-reply,
.right-blog-details-style7 .single-blog-content .blog-comments .blog-comment-info ul.comments-arae.all-reply {
    margin-top: 24px;
    padding-top: 30px;
    border-top: 1px solid #eee;
}
.full-blog-details-style-7 .single-blog-content .blog-comments .blog-comment-info ul.comments-arae li.comments-man,
.left-blog-details-style7 .single-blog-content .blog-comments .blog-comment-info ul.comments-arae li.comments-man,
.right-blog-details-style7 .single-blog-content .blog-comments .blog-comment-info ul.comments-arae li.comments-man{
    width: 45px;
    height: 45px;
    background-color: #cd7752;
    color: #fff;
    margin-right: 15px;
    font-size: 15px;
    border-radius: 3px;
    display: flex;
    align-items: center;
    text-align: center;
    justify-content: center;
    font-weight: 600;
}
.full-blog-details-style-7 .single-blog-content .blog-comments .blog-comment-info ul.comments-arae li.comments-content,
.left-blog-details-style7 .single-blog-content .blog-comments .blog-comment-info ul.comments-arae li.comments-content,
.right-blog-details-style7 .single-blog-content .blog-comments .blog-comment-info ul.comments-arae li.comments-content {
    width: calc(100% - 45px);
}
.full-blog-details-style-7 .single-blog-content .blog-comments .blog-comment-info ul.comments-arae li.comments-content span.comments-result,
.left-blog-details-style7 .single-blog-content .blog-comments .blog-comment-info ul.comments-arae li.comments-content span.comments-result,
.right-blog-details-style7 .single-blog-content .blog-comments .blog-comment-info ul.comments-arae li.comments-content span.comments-result{
    display: block;
}
.full-blog-details-style-7 .single-blog-content .blog-comments .blog-comment-info ul.comments-arae li.comments-content span.comment-name,
.left-blog-details-style7 .single-blog-content .blog-comments .blog-comment-info ul.comments-arae li.comments-content span.comment-name,
.right-blog-details-style7 .single-blog-content .blog-comments .blog-comment-info ul.comments-arae li.comments-content span.comment-name{
    margin: 5px 0px;
}
.full-blog-details-style-7 .single-blog-content .blog-comments .blog-comment-info ul.comments-arae li.comments-content span.comment-name i,
.left-blog-details-style7 .single-blog-content .blog-comments .blog-comment-info ul.comments-arae li.comments-content span.comment-name i,
.right-blog-details-style7 .single-blog-content .blog-comments .blog-comment-info ul.comments-arae li.comments-content span.comment-name i {
    font-style: normal;
}
.full-blog-details-style-7 .single-blog-content .blog-comments .blog-comment-info ul.comments-arae li.comments-content span.comments-result.c-date,
.left-blog-details-style7 .single-blog-content .blog-comments .blog-comment-info ul.comments-arae li.comments-content span.comments-result.c-date,
.right-blog-details-style7 .single-blog-content .blog-comments .blog-comment-info ul.comments-arae li.comments-content span.comments-result.c-date{
    font-weight: 600;
}
.full-blog-details-style-7 .single-blog-content .blog-comments .blog-comment-info ul.comments-arae li.comments-content span.comments-result.c-date a.Reply,
.left-blog-details-style7 .single-blog-content .blog-comments .blog-comment-info ul.comments-arae li.comments-content span.comments-result.c-date a.Reply,
.right-blog-details-style7 .single-blog-content .blog-comments .blog-comment-info ul.comments-arae li.comments-content span.comments-result.c-date a.Reply{
    color: #cd7752;
    margin-left: 30px;
    font-weight: 500;
}
.full-blog-details-style-7 .single-blog-content .blog-comments .blog-comment-info ul.comments-arae li.comments-content span span.comments-title,
.left-blog-details-style7 .single-blog-content .blog-comments .blog-comment-info ul.comments-arae li.comments-content span span.comments-title,
.right-blog-details-style7 .single-blog-content .blog-comments .blog-comment-info ul.comments-arae li.comments-content span span.comments-title{
    font-weight: 600;
    color: #cd7752;
}
/* style-7 center blog css */
.center-blog-style-7{
    display: flex;
    flex-wrap: wrap;
}
.center-blog-style-7 .blog-start{
    width: 100%;
    margin-top: 30px;
}
.center-blog-style-7 .blog-start:nth-child(1){
    margin-top: 0px;
}
.center-blog-style-7 .blog-start .blog-image{
    position: relative;
    display: flex;
}
.center-blog-style-7 .blog-start .blog-image a{
    position: relative;
    overflow: hidden;
}
.center-blog-style-7 .blog-start:hover .blog-image a img{
    -webkit-transform: scale(1.1);
    -o-transform: scale(1.1);
    transform: scale(1.1);
}
.center-blog-style-7 .blog-start .blog-image a img,
.center-blog-style-7 .blog-start:hover .blog-image a img{
    -webkit-transition: all 0.3s ease-in-out 0s;
    -o-transition: all 0.3s ease-in-out 0s;
    transition: all 0.3s ease-in-out 0s;
}
.center-blog-style-7 .blog-start .blog-content {
    padding-top: 23px;
}
.center-blog-style-7 .blog-start .blog-content .blog-title h6{
    font-size: 16px;
}
.center-blog-style-7 .blog-start .blog-content .blog-title h6 a{
    display: block;
    width: 100%;
    white-space: nowrap;
    text-overflow: ellipsis;
    overflow: hidden;
}
.center-blog-style-7 .blog-start .blog-content .blog-title h6 a:hover{
    color: #cd7752;
}
.center-blog-style-7 .blog-start .blog-content p.blog-description{
    color: #999;
    margin-top: 16px;
}
.center-blog-style-7 .blog-start .blog-content .more-blog{
    margin-top: 17px;
    display: flex;
    align-items: center;
    justify-content: space-between;
}
.center-blog-style-7 .blog-start .blog-content .more-blog a.read-link{
    color: #cd7752;
    font-size: 14px;
    display: flex;
    align-items: center;
}
.center-blog-style-7 .blog-start .blog-content .more-blog a.read-link i{
    font-size: 12px;
    padding-left: 5px;
}
.center-blog-style-7 .blog-start .blog-content .more-blog a.read-link i,
.center-blog-style-7 .blog-start .blog-content .more-blog a.read-link:hover i{
    -webkit-transition: all 0.3s ease-in-out 0s;
    -o-transition: all 0.3s ease-in-out 0s;
    transition: all 0.3s ease-in-out 0s;
}
.center-blog-style-7 .blog-start .blog-content .more-blog a.read-link:hover i{
    margin-left: 8px;
}
/* order history page css */
.order-histry-area .order-history {
    display: flex;
    flex-wrap: wrap;
    align-items: flex-start;
    margin-left: -30px;
}
.order-histry-area .order-history .profile {
    width: calc(35% - 30px);
    margin-left: 30px;
    border: 1px solid #eee;
    border-radius: 5px;
}
.order-histry-area .order-history .profile .order-pro {
    padding: 30px;
    display: flex;
    align-items: center;
}
.order-histry-area .order-history .profile .order-pro .pro-img img {
    border: 3px solid #eee;
    border-radius: 100%;
}
.order-histry-area .order-history .profile .order-pro .order-name {
    margin-left: 15px;
}
.order-histry-area .order-history .profile .order-pro .order-name h4 {
    font-size: 18px;
    color: #333;
    font-weight: 600;
    line-height: 1;
}
.order-histry-area .order-history .profile .order-pro .order-name span {
    font-size: 13px;
    color: #9da9b9;
    margin-top: 12px;
}
.order-histry-area .order-history .profile .order-his-page ul.profile-ul li.profile-li a {
    position: relative;
    width: 100%;
    padding: 15px 30px;
    font-size: 14px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    
    border-top: 1px solid #ddd;
    -webkit-transition: all 0.3s ease-in-out 0s;
    -o-transition: all 0.3s ease-in-out 0s;
    transition: all 0.3s ease-in-out 0s;
}
.order-histry-area .order-history .profile .order-his-page ul.profile-ul li.profile-li a:after {
    background-color: #ec6504;
    content: '';
    position: absolute;
    bottom: 50%;
    transform: translateY(50%);
    left: 18px;
    width: 2px;
    height: 15px;
}
.order-histry-area .order-history .profile .order-his-page ul.profile-ul li.profile-li a.active:after {
    background-color: #fff;
}
.order-histry-area .order-history .profile .order-his-page ul.profile-ul li.profile-li a:hover {
    background-color: #f5f5f5;
    color: #333;
    border-radius: 5px;
    -webkit-transition: all 0.3s ease-in-out 0s;
    -o-transition: all 0.3s ease-in-out 0s;
    transition: all 0.3s ease-in-out 0s;
}
.order-histry-area .order-history .profile .order-his-page ul.profile-ul li.profile-li a.active {
    background-color: #ec6504;
    color: #fff;
    border-color: #ec6504;
    -webkit-transition: all 0.3s ease-in-out 0s;
    -o-transition: all 0.3s ease-in-out 0s;
    transition: all 0.3s ease-in-out 0s;
}
.order-histry-area .order-history .profile .order-his-page ul.profile-ul li.profile-li a span.pro-count {
    background-color: #ec6504;
    width: 25px;
    height: 25px;
    font-size: 12px;
    line-height: 1;
    color: #fff;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50px;
}
.order-histry-area .order-history .profile .order-his-page ul.profile-ul li.profile-li a.active span.pro-count {
    background-color: #fff;
    color: #333;
}
.order-histry-area .order-history .order-info {
    width: calc(65% - 30px);
    margin-left: 30px;
}
.order-histry-area .order-history .order-info .table {
    border-top: 1px solid #eee;
}
.order-histry-area .order-history .order-info .table thead {
    border-top: 1px solid #eee;
    border-bottom: 1px solid #eee;
}
.order-histry-area .order-history .order-info .table thead tr th {
    padding-top: 11px;
    padding-bottom: 10px;
    border-bottom: 1px solid #eee;
}
.order-histry-area .order-history .order-info .table tbody tr td {
    padding-top: 11px;
    padding-bottom: 10px;
}
.order-histry-area .order-history .order-info .table tbody tr td.canceled {
    color: #ff5252;
}
.order-histry-area .order-history .order-info .table tbody tr td.process {
    color: #50c6e9;
}
.order-histry-area .order-history .order-info .table tbody tr td.delayed {
    color: #ffb74f;
}
.order-histry-area .order-history .order-info .table tbody tr td.delivered {
    color: #43d9a3;
}
/* profile page css */
.order-histry-area .order-history .profile-form {
    width: calc(65% - 30px);
    margin-left: 30px;
}
.order-histry-area .order-history .profile-form form ul.pro-input-label {
    display: flex;
    flex-wrap: wrap;
    align-items: center;
    margin-left: -30px;
    margin-top: 15px;
}
.order-histry-area .order-history .profile-form form ul.pro-input-label:first-child {
    margin-top: 0px;
}
.order-histry-area .order-history .profile-form form ul.pro-input-label li {
    width: calc(50% - 30px);
    margin-left: 30px;
}
.order-histry-area .order-history .profile-form form ul.pro-input-label li label {
    font-size: 14px;
}
.order-histry-area .order-history .profile-form form ul.pro-input-label li input {
    width: 100%;
    border: 1px solid #eee;
    margin-top: 10px;
    border-radius: 5px;
}
.order-histry-area .order-history .profile-form form ul.pro-submit {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding-top: 30px;
    margin-top: 30px;
    border-top: 1px solid #eee;
}
/* pro address css */
.order-histry-area .order-history .profile-address {
    width: calc(65% - 30px);
    margin-left: 30px;
}
.order-histry-area .order-history .profile-address form .pro-add-title h4 {
    font-size: 22px;
    color: #333;
    font-weight: 500;
    padding-bottom: 12px;
    border-bottom: 1px solid #eee;
    line-height: 1;   
}
.order-histry-area .order-history .profile-address form ul.add-label-input {
    margin-left: -30px;
    margin-top: 15px;
    display: flex;
    align-items: center;
}
.order-histry-area .order-history .profile-address form ul.add-label-input li {
    width: calc(50% - 30px);
    margin-left: 30px;
}
.order-histry-area .order-history .profile-address form ul.add-label-input li  label {
    font-size: 14px;
    color: #333;
}
.order-histry-area .order-history .profile-address form ul.add-label-input li  input {
    width: 100%;
    margin-top: 10px;
    border: 1px solid #eee;
    border-radius: 5px;
}
.order-histry-area .order-history .profile-address form ul.add-label-input li  select {
    width: 100%;
    margin-top: 10px;
    border: 1px solid #eee;
    border-radius: 5px;
}
.order-histry-area .order-history .profile-address form ul.pro-submit {
    margin-top: 30px;
    padding-top: 30px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    border-top: 1px solid #eee;
}
.order-histry-area .order-history .profile-address form ul.pro-submit li {
    display: flex;
    align-items: center;
}
.order-histry-area .order-history .profile-address form ul.pro-submit li input {
    margin-right: 5px;
}
.order-histry-area .order-history .profile-form form ul.pro-submit li {
    display: flex;
    align-items: center;
    line-height: 1;
}
.order-histry-area .order-history .profile-form form ul.pro-submit li input {
    margin-right: 5px;
}
/* profile wishlist page css */
.order-histry-area .order-history .profile-wishlist {
    width: calc(65% - 30px);
    margin-left: 30px;
}
/* tickets page css */
.order-histry-area .order-history .profile-tickets {
    width: calc(65% - 30px);
    margin-left: 30px;
}
.order-histry-area .order-history .profile-tickets .table {
    border-top: 1px solid #eee;
}
.order-histry-area .order-history .profile-tickets .table thead tr th {
    padding-top: 11px;
    padding-bottom: 10px;
    border-bottom: 1px solid #eee;
}
.order-histry-area .order-history .profile-tickets .table tbody tr td {
    padding-top: 11px;
    padding-bottom: 10px;
}
.order-histry-area .order-history .profile-tickets .table tbody tr td.canceled {
    color: #ff5252;
}
.order-histry-area .order-history .profile-tickets .table tbody tr td.process {
    color: #50c6e9;
}
.order-histry-area .order-history .profile-tickets .table tbody tr td.delayed {
    color: #ffb74f;
}
.order-histry-area .order-history .profile-tickets .table tbody tr td.delivered {
    color: #43d9a3;
}

/*------------------------------------------------------- New Added start -------------------------------------------------------*/
/* left column css */
.left-right-column{
    display: flex;
}
.left-right-column .left-column{
    width: 20%;
    padding-right: 30px;
}
.left-right-column .right-column{
    width: 80%;
}
/* title */
.left-section-title h4{
    padding-bottom: 10px;
    font-size: 18px;
    border-bottom: 1px solid #e2e2e2;
}
.left-section-title h4 span{
    color: #cd7752;
}
/* special products css */
a.responsive-collapse{
    color: #000;
    font-size: 18px;
    font-weight: 600;
    display: none;
}
a.responsive-collapse span{
    color: #cd7752;
    margin-left: 5px;
    margin-right: auto;
}
.special-product .collapse,
.tred-product .collapse{
    display: block;
}
/* special and trending pro css */
.special-pro .h-t-pro,
.trening-left-pro .h-t-pro{
    display: flex;
    align-items: center;
    margin-top: 10px;
}
.special-pro .h-t-pro .tred-pro,
.trening-left-pro .h-t-pro .tred-pro{
    width: 33%;
}
.special-pro .h-t-pro .caption,
.trening-left-pro .h-t-pro .caption{
    width: 67%;
    padding-top: 0px;
    padding-left: 15px;
    text-align: left;
}
/* left banner css */
.left-banner .l-banner{
    position: relative;
}
.left-banner .l-banner a{
    position: relative;
    overflow: hidden;
    z-index: 2;
}
.left-banner .l-banner:hover a img{
    -webkit-transform: scale(1.1);
    -o-transform: scale(1.1);
    transform: scale(1.1);
    -webkit-transition: all 0.3s ease-in-out 0s;
    -o-transition: all 0.3s ease-in-out 0s;
    transition: all 0.3s ease-in-out 0s;
}
.left-banner .l-banner a img{
    -webkit-transition: all 0.3s ease-in-out 0s;
    -o-transition: all 0.3s ease-in-out 0s;
    transition: all 0.3s ease-in-out 0s;
}
.left-banner .l-banner .left-banner-content{
    position: absolute;
    top: 30px;
    left: 20px;
    z-index: 2;
}
.left-banner .l-banner .left-banner-content span.banner-head{
    color: #999;
    line-height: 1;
}
.left-banner .l-banner .left-banner-content h2{
    margin-top: 3px;
    font-size: 20px;
}
.left-banner .l-banner .left-banner-content a{
    margin-top: 11px;
}
/* big sale css */
.left-deal-bg{
    background-repeat: no-repeat;
    background-size: cover;
    background-position: center;
    position: relative;
}
.left-deal-bg::after{
    background-color: #fff;
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    opacity: 0.7;
}
.left-deal-bg .left-deal-c{
    position: relative;
    padding: 30px;
    text-align: center;
    z-index: 1;
}
.left-deal-bg .left-deal-c h2{
    font-size: 22px;
    font-weight: 500;
}
.left-deal-bg .left-deal-c h2 span{
    color: #cd7752;
    font-size: 26px;
    font-weight: 700;
}
.left-deal-bg .left-deal-c h4{
    text-transform: uppercase;
    letter-spacing: 5px;
}
.left-deal-bg .left-deal-c a{
    font-size: 16px;
    font-weight: 500;
    text-transform: uppercase;
}
/* brand css */
.home3-brand{
    margin-top: 20px;
}
/* left section padding */
.left-section-t-padding{
    padding-top: 30px;
}
.left-section-b-padding{
    padding-bottom: 30px;
}
.left-section-tb-padding{
    padding-top: 30px;
    padding-bottom: 30px;
}
/*------------------------------------------------------- New Added end -------------------------------------------------------*/

/*------------------------------------------------------- Comingsoon start -------------------------------------------------------*/
.coming-soon-area {
    position: relative;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    background-size: cover;
    background-position: center;
    background-repeat: no-repeat;
}
.coming-soon-area::before {
    background-color: #000000;
    content: '';
    position: absolute;
    top: 0px;
    right: 0px;
    bottom: 0px;
    left: 0px;
    opacity: 0.5;
}
.coming-soon-area .comingsoon-main {
    text-align: center;
    width: 45%;
    margin: 0 auto;
}
.coming-soon-area .comingsoon-main .comingsoon-logo {
    margin-bottom: 53px;
    animation: fadeInDown;
    -webkit-animation-delay: 0.5s;
    animation-delay: 0.5s;
    -webkit-animation-fill-mode: both;
    animation-fill-mode: both;
    -webkit-animation-duration: 0.5s;
    animation-duration: 0.5s;
}   
.coming-soon-area .comingsoon-main .comingsoon-text h2.title {
    color: #ffffff;
    font-size: 62px;
    font-weight: 700;
    line-height: 1;
    animation: fadeInDown;
    -webkit-animation-delay: 0.7s;
    animation-delay: 0.7s;
    -webkit-animation-fill-mode: both;
    animation-fill-mode: both;
    -webkit-animation-duration: 0.7s;
    animation-duration: 0.7s;
}
.coming-soon-area .comingsoon-main .comingsoon-text h2.title span {
    animation: blinker 1.5s infinite;
}
@keyframes blinker{
    50%{
        color: #ec6504;
    }
}
.coming-soon-area .comingsoon-main .comingsoon-text p {
    color: #ffffff;
    font-size: 16px;
    padding-top: 24px;
    margin: 0 auto;
    animation: fadeInDown;
    -webkit-animation-delay: 0.9s;
    animation-delay: 0.9s;
    -webkit-animation-fill-mode: both;
    animation-fill-mode: both;
    -webkit-animation-duration: 0.9s;
    animation-duration: 0.9s;
}
.coming-soon-area .comingsoon-main .comingsoon-timer {
    margin-top: 49px;
    animation: fadeInUp;
    -webkit-animation-delay: 0.5s;
    animation-delay: 0.5s;
    -webkit-animation-fill-mode: both;
    animation-fill-mode: both;
    -webkit-animation-duration: 0.5s;
    animation-duration: 0.5s;
}
.coming-soon-area .comingsoon-main .comingsoon-timer ul.contdown_row {
    display: flex;
    flex-wrap: wrap;
    justify-content: space-between;
}
.coming-soon-area .comingsoon-main .comingsoon-timer ul.contdown_row li.countdown_section span.countdown_timer {
    color: #ffffff;
    font-size: 40px;
    font-weight: 600;
    line-height: 1;
    display: block;
}
.coming-soon-area .comingsoon-main .comingsoon-timer ul.contdown_row li.countdown_section span.countdown_title {
    color: #ffffff;
    font-size: 18px;
    margin-top: 14px;
    font-weight: 400;
    line-height: 1;
}
.coming-soon-area .comingsoon-main .comingsoon-input {
    margin-top: 54px;
    animation: fadeInUp;
    -webkit-animation-delay: 0.7s;
    animation-delay: 0.7s;
    -webkit-animation-fill-mode: both;
    animation-fill-mode: both;
    -webkit-animation-duration: 0.7s;
    animation-duration: 0.7s;
}
.coming-soon-area .comingsoon-main .comingsoon-input .comingsoon-form form {
    position: relative;
}
.coming-soon-area .comingsoon-main .comingsoon-input .comingsoon-form form input {
    width: 100%;
    padding: 14px 15px;
    border: none;
}
.coming-soon-area .comingsoon-main .comingsoon-input .comingsoon-form form a {
    position: absolute;
    bottom: 50%;
    transform: translateY(50%);
    right: 15px;
    font-size: 16px;
    font-weight: 500;
    line-height: 1;
    border-left: 1px solid rgba(0,0,0,10%);
    padding-left: 20px;
}
.coming-soon-area .comingsoon-main .comingsoon-social {
    margin-top: 60px;
    animation: fadeInUp;
    -webkit-animation-delay: 0.9s;
    animation-delay: 0.9s;
    -webkit-animation-fill-mode: both;
    animation-fill-mode: both;
    -webkit-animation-duration: 0.9s;
    animation-duration: 0.9s;
}
.coming-soon-area .comingsoon-main .comingsoon-social ul.social-ul {
    display: flex;
    align-items: center;
    justify-content: center;
}
.coming-soon-area .comingsoon-main .comingsoon-social ul.social-ul li.social-li {
    margin-left: 30px;
}
.coming-soon-area .comingsoon-main .comingsoon-social ul.social-ul li.social-li:first-child {
    margin-left: 0px;
}
.coming-soon-area .comingsoon-main .comingsoon-social ul.social-ul li.social-li a {
    color: #ffffff;
    font-size: 22px;
    line-height: 1;
}
.coming-soon-area .comingsoon-main .comingsoon-social ul.social-ul li.social-li a:hover {
    color: #ec6504;
}
/*------------------------------------------------------- Comingsoon end -------------------------------------------------------*/