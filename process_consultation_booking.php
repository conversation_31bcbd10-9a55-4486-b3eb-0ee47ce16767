<?php
session_start();
header('Content-Type: application/json');
include('database/dbconnection.php');

$obj = new main();
$obj->connection();

// Validate required fields
$requiredFields = ['doctorId', 'patientName', 'patientEmail', 'patientPhone', 'patientAge', 'patientGender', 'preferredDate', 'preferredTime', 'healthConcerns', 'consultationFee'];

foreach ($requiredFields as $field) {
    if (!isset($_POST[$field]) || empty(trim($_POST[$field]))) {
        echo json_encode(['success' => false, 'message' => 'Missing required field: ' . $field]);
        exit;
    }
}

// Sanitize and validate input
$doctorId = intval($_POST['doctorId']);
$patientName = trim($_POST['patientName']);
$patientEmail = trim($_POST['patientEmail']);
$patientPhone = trim($_POST['patientPhone']);
$patientAge = intval($_POST['patientAge']);
$patientGender = trim($_POST['patientGender']);
$preferredDate = trim($_POST['preferredDate']);
$preferredTime = trim($_POST['preferredTime']);
$healthConcerns = trim($_POST['healthConcerns']);
$currentMedications = trim($_POST['currentMedications'] ?? '');
$consultationFee = floatval($_POST['consultationFee']);

// Validate email
if (!filter_var($patientEmail, FILTER_VALIDATE_EMAIL)) {
    echo json_encode(['success' => false, 'message' => 'Invalid email address']);
    exit;
}

// Validate phone number (basic validation)
if (!preg_match('/^[0-9]{10}$/', $patientPhone)) {
    echo json_encode(['success' => false, 'message' => 'Invalid phone number. Please enter 10 digits.']);
    exit;
}

// Validate age
if ($patientAge < 1 || $patientAge > 120) {
    echo json_encode(['success' => false, 'message' => 'Invalid age']);
    exit;
}

// Validate gender
if (!in_array($patientGender, ['Male', 'Female', 'Other'])) {
    echo json_encode(['success' => false, 'message' => 'Invalid gender']);
    exit;
}

// Validate date
$dateObj = DateTime::createFromFormat('Y-m-d', $preferredDate);
$today = new DateTime();
$today->setTime(0, 0, 0);

if (!$dateObj || $dateObj < $today) {
    echo json_encode(['success' => false, 'message' => 'Invalid consultation date']);
    exit;
}

// Validate time format
if (!preg_match('/^([01]?[0-9]|2[0-3]):[0-5][0-9]:[0-5][0-9]$/', $preferredTime)) {
    echo json_encode(['success' => false, 'message' => 'Invalid time format']);
    exit;
}

try {
    // Check if doctor exists and is active
    $doctorQuery = "SELECT DoctorId, DoctorName, ConsultationFee FROM doctors WHERE DoctorId = ? AND IsActive = 1";
    $doctorResult = $obj->MysqliSelect1($doctorQuery, array("DoctorId", "DoctorName", "ConsultationFee"), "i", array($doctorId));
    
    if (empty($doctorResult)) {
        echo json_encode(['success' => false, 'message' => 'Selected doctor is not available']);
        exit;
    }

    $doctor = $doctorResult[0];

    // Check if the time slot is still available
    $slotCheckQuery = "
        SELECT COUNT(*) as count 
        FROM consultation_bookings 
        WHERE DoctorId = ? AND ConsultationDate = ? AND ConsultationTime = ? 
        AND BookingStatus IN ('Confirmed', 'Pending')";
    
    $slotCheck = $obj->MysqliSelect1($slotCheckQuery, array("count"), "iss", array($doctorId, $preferredDate, $preferredTime));
    
    if ($slotCheck[0]['count'] > 0) {
        echo json_encode(['success' => false, 'message' => 'Selected time slot is no longer available']);
        exit;
    }

    // Generate unique booking number
    $bookingNumber = 'CON' . date('Ymd') . str_pad(mt_rand(1, 9999), 4, '0', STR_PAD_LEFT);
    
    // Check if booking number already exists
    $bookingCheckQuery = "SELECT COUNT(*) as count FROM consultation_bookings WHERE BookingNumber = ?";
    $bookingCheck = $obj->MysqliSelect1($bookingCheckQuery, array("count"), "s", array($bookingNumber));
    
    // If booking number exists, generate a new one
    while ($bookingCheck[0]['count'] > 0) {
        $bookingNumber = 'CON' . date('Ymd') . str_pad(mt_rand(1, 9999), 4, '0', STR_PAD_LEFT);
        $bookingCheck = $obj->MysqliSelect1($bookingCheckQuery, array("count"), "s", array($bookingNumber));
    }

    // Insert booking into database
    $insertQuery = "
        INSERT INTO consultation_bookings 
        (BookingNumber, DoctorId, PatientName, PatientEmail, PatientPhone, PatientAge, PatientGender, 
         HealthConcerns, CurrentMedications, PreferredDate, PreferredTime, ConsultationDate, 
         ConsultationTime, ConsultationFee, PaymentStatus, BookingStatus, ConsultationMode) 
        VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, 'Pending', 'Pending', 'Phone')";

    $insertParams = array(
        $bookingNumber, $doctorId, $patientName, $patientEmail, $patientPhone, 
        $patientAge, $patientGender, $healthConcerns, $currentMedications, 
        $preferredDate, $preferredTime, $preferredDate, $preferredTime, $consultationFee
    );

    $insertResult = $obj->MysqliInsert($insertQuery, "sissssissssssd", $insertParams);

    if ($insertResult) {
        // Get the booking ID
        $bookingId = $obj->connection->insert_id;

        // Send confirmation email (you can implement this later)
        // sendBookingConfirmationEmail($patientEmail, $bookingNumber, $doctor['DoctorName'], $preferredDate, $preferredTime);

        // Send SMS notification (you can implement this later)
        // sendBookingSMS($patientPhone, $bookingNumber, $doctor['DoctorName'], $preferredDate, $preferredTime);

        echo json_encode([
            'success' => true, 
            'message' => 'Booking successful',
            'bookingNumber' => $bookingNumber,
            'bookingId' => $bookingId,
            'doctorName' => $doctor['DoctorName'],
            'consultationDate' => $preferredDate,
            'consultationTime' => $preferredTime,
            'consultationFee' => $consultationFee,
            // For now, we'll skip payment integration and mark as paid
            'paymentUrl' => null // You can add payment gateway URL here later
        ]);

        // For demonstration, let's automatically confirm the booking
        // In production, this should happen after payment confirmation
        $updateQuery = "UPDATE consultation_bookings SET BookingStatus = 'Confirmed', PaymentStatus = 'Paid' WHERE BookingId = ?";
        $obj->MysqliUpdate($updateQuery, "i", array($bookingId));

    } else {
        echo json_encode(['success' => false, 'message' => 'Failed to create booking. Please try again.']);
    }

} catch (Exception $e) {
    error_log("Error in process_consultation_booking.php: " . $e->getMessage());
    echo json_encode(['success' => false, 'message' => 'An error occurred while processing your booking. Please try again.']);
}

// Function to send booking confirmation email (implement later)
function sendBookingConfirmationEmail($email, $bookingNumber, $doctorName, $date, $time) {
    // Email implementation here
    return true;
}

// Function to send booking SMS (implement later)
function sendBookingSMS($phone, $bookingNumber, $doctorName, $date, $time) {
    // SMS implementation here
    return true;
}
?>
