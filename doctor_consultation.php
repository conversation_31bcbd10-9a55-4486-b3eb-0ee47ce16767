<?php
session_start();
include('includes/urls.php');
include('database/dbconnection.php');
$obj = new main();
$obj->connection();

// Get consultation settings
$settings = array();
$settingsQuery = $obj->MysqliSelect1("SELECT SettingKey, SettingValue FROM consultation_settings", 
    array("SettingKey", "SettingValue"), "", array());
foreach($settingsQuery as $setting) {
    $settings[$setting['SettingKey']] = $setting['SettingValue'];
}

// Get all active doctors
$doctors = $obj->MysqliSelect1("SELECT * FROM doctors WHERE IsActive = 1 ORDER BY DoctorName", 
    array("DoctorId", "DoctorName", "Qualification", "Specialization", "Experience", "PhotoPath", "Description", "ConsultationFee"), "", array());
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <title>Book Doctor Consultation - My Nutrify</title>
    <meta name="description" content="Book online Ayurvedic doctor consultation with expert practitioners. Get personalized health advice and treatment recommendations from certified Ayurvedic doctors." />
    <meta name="keywords" content="ayurvedic doctor consultation, online doctor booking, ayurvedic treatment, health consultation, ayurvedic medicine" />
    
    <!-- favicon -->
    <link rel="shortcut icon" type="image/favicon" href="image/fevicon.png">
    <!-- bootstrap -->
    <link rel="stylesheet" type="text/css" href="css/bootstrap.min.css">
    <!-- simple-line icon -->
    <link rel="stylesheet" type="text/css" href="css/simple-line-icons.css">
    <!-- font-awesome icon -->
    <link rel="stylesheet" type="text/css" href="css/font-awesome.min.css">
    <!-- themify icon -->
    <link rel="stylesheet" type="text/css" href="css/themify-icons.css">
    <!-- ion icon -->
    <link rel="stylesheet" type="text/css" href="css/ionicons.min.css">
    <!-- owl slider -->
    <link rel="stylesheet" type="text/css" href="css/owl.carousel.min.css">
    <link rel="stylesheet" type="text/css" href="css/owl.theme.default.min.css">
    <!-- swiper -->
    <link rel="stylesheet" type="text/css" href="css/swiper.min.css">
    <!-- animation -->
    <link rel="stylesheet" type="text/css" href="css/animate.css">
    <!-- style -->
    <link rel="stylesheet" type="text/css" href="css/style.css">
    <link rel="stylesheet" type="text/css" href="css/responsive.css">
    <!-- full width override -->
    <link rel="stylesheet" type="text/css" href="css/full-width-override.css">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css" rel="stylesheet">
    <!-- SweetAlert CSS -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/sweetalert2@11/dist/sweetalert2.min.css">
    <!-- SweetAlert JS -->
    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
    
    <style>
        /* Consultation Page Styles */
        .consultation-hero {
            background: linear-gradient(135deg, #85B26F 0%, #6D945C 100%);
            color: white;
            padding: 80px 0;
            text-align: center;
        }
        
        .consultation-hero h1 {
            font-size: 3rem;
            font-weight: 700;
            margin-bottom: 1rem;
        }
        
        .consultation-hero p {
            font-size: 1.2rem;
            margin-bottom: 2rem;
            opacity: 0.9;
        }
        
        .hero-features {
            display: flex;
            justify-content: center;
            gap: 3rem;
            margin-top: 2rem;
        }
        
        .hero-feature {
            text-align: center;
        }
        
        .hero-feature i {
            font-size: 2rem;
            margin-bottom: 0.5rem;
            display: block;
        }
        
        .doctor-card {
            background: white;
            border-radius: 15px;
            box-shadow: 0 8px 25px rgba(0,0,0,0.1);
            padding: 2rem;
            margin-bottom: 2rem;
            transition: transform 0.3s ease, box-shadow 0.3s ease;
        }
        
        .doctor-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 15px 35px rgba(0,0,0,0.15);
        }
        
        .doctor-photo {
            width: 120px;
            height: 120px;
            border-radius: 50%;
            object-fit: cover;
            margin: 0 auto 1rem;
            display: block;
            border: 4px solid #85B26F;
        }
        
        .doctor-name {
            font-size: 1.5rem;
            font-weight: 700;
            color: #2A4B1E;
            margin-bottom: 0.5rem;
        }
        
        .doctor-qualification {
            color: #6C757D;
            font-size: 0.9rem;
            margin-bottom: 0.5rem;
        }
        
        .doctor-specialization {
            color: #85B26F;
            font-weight: 600;
            margin-bottom: 1rem;
        }
        
        .doctor-experience {
            background: #F8F9FA;
            padding: 0.5rem 1rem;
            border-radius: 20px;
            display: inline-block;
            font-size: 0.9rem;
            margin-bottom: 1rem;
        }
        
        .consultation-fee {
            font-size: 1.5rem;
            font-weight: 700;
            color: #28A745;
            margin-bottom: 1rem;
        }
        
        .book-consultation-btn {
            background: #85B26F;
            color: white;
            border: none;
            padding: 12px 30px;
            border-radius: 25px;
            font-weight: 600;
            transition: background 0.3s ease;
            width: 100%;
        }
        
        .book-consultation-btn:hover {
            background: #6D945C;
            color: white;
        }
        
        .consultation-process {
            background: #F8F9FA;
            padding: 60px 0;
        }
        
        .process-step {
            text-align: center;
            margin-bottom: 2rem;
        }
        
        .process-icon {
            width: 80px;
            height: 80px;
            background: #85B26F;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 1rem;
            color: white;
            font-size: 2rem;
        }
        
        .process-title {
            font-size: 1.2rem;
            font-weight: 600;
            margin-bottom: 0.5rem;
            color: #2A4B1E;
        }
        
        .faq-section {
            padding: 60px 0;
        }
        
        .faq-item {
            background: white;
            border-radius: 10px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
            margin-bottom: 1rem;
            overflow: hidden;
        }
        
        .faq-question {
            background: #85B26F;
            color: white;
            padding: 1rem 1.5rem;
            cursor: pointer;
            font-weight: 600;
            margin: 0;
        }
        
        .faq-answer {
            padding: 1.5rem;
            display: none;
        }
        
        .faq-question:hover {
            background: #6D945C;
        }
        
        @media (max-width: 768px) {
            .consultation-hero h1 {
                font-size: 2rem;
            }
            
            .hero-features {
                flex-direction: column;
                gap: 1rem;
            }
            
            .doctor-card {
                padding: 1.5rem;
            }
        }
    </style>
</head>

<body class="home-1">
    <!-- header start -->
    <?php include("components/header.php"); ?>

    <!-- Hero Section -->
    <section class="consultation-hero">
        <div class="container">
            <h1>Ayurvedic Consultation with Expert Vaidyas</h1>
            <p>Get personalized health advice and treatment recommendations from certified Ayurvedic doctors</p>
            
            <div class="hero-features">
                <div class="hero-feature">
                    <i class="fas fa-phone"></i>
                    <div>Phone Consultation</div>
                </div>
                <div class="hero-feature">
                    <i class="fas fa-rupee-sign"></i>
                    <div>₹<?php echo $settings['default_consultation_fee'] ?? '200'; ?> per session</div>
                </div>
                <div class="hero-feature">
                    <i class="fas fa-shipping-fast"></i>
                    <div>24hr Medicine Dispatch</div>
                </div>
                <div class="hero-feature">
                    <i class="fas fa-shield-alt"></i>
                    <div>100% Private & Secure</div>
                </div>
            </div>
        </div>
    </section>

    <!-- Doctors Section -->
    <section class="section-tb-padding">
        <div class="container">
            <div class="row">
                <div class="col-12 text-center mb-5">
                    <h2 class="section-title">Our Expert Ayurvedic Doctors</h2>
                    <p class="section-subtitle">Choose from our panel of experienced and certified Ayurvedic practitioners</p>
                </div>
            </div>
            
            <div class="row">
                <?php foreach($doctors as $doctor): ?>
                <div class="col-lg-4 col-md-6 mb-4">
                    <div class="doctor-card">
                        <?php if($doctor['PhotoPath']): ?>
                            <img src="cms/images/doctors/<?php echo $doctor['PhotoPath']; ?>" 
                                 alt="<?php echo htmlspecialchars($doctor['DoctorName']); ?>" 
                                 class="doctor-photo">
                        <?php else: ?>
                            <div class="doctor-photo" style="background: #85B26F; display: flex; align-items: center; justify-content: center; color: white; font-size: 2rem;">
                                <i class="fas fa-user-md"></i>
                            </div>
                        <?php endif; ?>
                        
                        <div class="doctor-name"><?php echo htmlspecialchars($doctor['DoctorName']); ?></div>
                        <div class="doctor-qualification"><?php echo htmlspecialchars($doctor['Qualification']); ?></div>
                        <div class="doctor-specialization"><?php echo htmlspecialchars($doctor['Specialization']); ?></div>
                        <div class="doctor-experience"><?php echo $doctor['Experience']; ?> years experience</div>
                        
                        <?php if($doctor['Description']): ?>
                            <p class="doctor-description"><?php echo htmlspecialchars($doctor['Description']); ?></p>
                        <?php endif; ?>
                        
                        <div class="consultation-fee">₹<?php echo number_format($doctor['ConsultationFee'], 0); ?></div>
                        
                        <button class="btn book-consultation-btn" 
                                onclick="bookConsultation(<?php echo $doctor['DoctorId']; ?>, '<?php echo htmlspecialchars($doctor['DoctorName']); ?>', <?php echo $doctor['ConsultationFee']; ?>)">
                            Book Consultation
                        </button>
                    </div>
                </div>
                <?php endforeach; ?>
            </div>
        </div>
    </section>

    <!-- Consultation Process Section -->
    <section class="consultation-process">
        <div class="container">
            <div class="row">
                <div class="col-12 text-center mb-5">
                    <h2 class="section-title">How It Works</h2>
                    <p class="section-subtitle">Simple steps to get your Ayurvedic consultation</p>
                </div>
            </div>

            <div class="row">
                <div class="col-lg-3 col-md-6">
                    <div class="process-step">
                        <div class="process-icon">
                            <i class="fas fa-calendar-check"></i>
                        </div>
                        <div class="process-title">Book Appointment</div>
                        <p>Choose your preferred doctor and time slot for consultation</p>
                    </div>
                </div>
                <div class="col-lg-3 col-md-6">
                    <div class="process-step">
                        <div class="process-icon">
                            <i class="fas fa-credit-card"></i>
                        </div>
                        <div class="process-title">Make Payment</div>
                        <p>Secure online payment of ₹<?php echo $settings['default_consultation_fee'] ?? '200'; ?> consultation fee</p>
                    </div>
                </div>
                <div class="col-lg-3 col-md-6">
                    <div class="process-step">
                        <div class="process-icon">
                            <i class="fas fa-phone-alt"></i>
                        </div>
                        <div class="process-title">Get Consultation</div>
                        <p>Doctor will call you at your scheduled time for consultation</p>
                    </div>
                </div>
                <div class="col-lg-3 col-md-6">
                    <div class="process-step">
                        <div class="process-icon">
                            <i class="fas fa-pills"></i>
                        </div>
                        <div class="process-title">Receive Treatment</div>
                        <p>Get personalized treatment plan and medicine recommendations</p>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- FAQ Section -->
    <section class="faq-section">
        <div class="container">
            <div class="row">
                <div class="col-12 text-center mb-5">
                    <h2 class="section-title">Frequently Asked Questions</h2>
                </div>
            </div>

            <div class="row">
                <div class="col-lg-8 mx-auto">
                    <div class="faq-item">
                        <h5 class="faq-question" onclick="toggleFaq(this)">
                            What are the online Ayurvedic consultation charges?
                            <i class="fas fa-chevron-down float-right"></i>
                        </h5>
                        <div class="faq-answer">
                            <p>Charges for ayurvedic consultation are ₹<?php echo $settings['default_consultation_fee'] ?? '200'; ?>/- per consultation call</p>
                        </div>
                    </div>

                    <div class="faq-item">
                        <h5 class="faq-question" onclick="toggleFaq(this)">
                            How fast is the medicine shipped?
                            <i class="fas fa-chevron-down float-right"></i>
                        </h5>
                        <div class="faq-answer">
                            <p>The Products will be dispatched within 24 hours of you placing the order. However it takes 5 – 7 days to reach your location depending on the location.</p>
                        </div>
                    </div>

                    <div class="faq-item">
                        <h5 class="faq-question" onclick="toggleFaq(this)">
                            How can I reschedule my appointment?
                            <i class="fas fa-chevron-down float-right"></i>
                        </h5>
                        <div class="faq-answer">
                            <p>You can reschedule your appointment from the confirmation email you receive after booking. Alternatively, you can contact our customer care team for assistance.</p>
                        </div>
                    </div>

                    <div class="faq-item">
                        <h5 class="faq-question" onclick="toggleFaq(this)">
                            What is the mode of consultation?
                            <i class="fas fa-chevron-down float-right"></i>
                        </h5>
                        <div class="faq-answer">
                            <p>The consultation will be online through a telephonic call. The doctor will reach out to you on your number at your appointment time for the consultation.</p>
                        </div>
                    </div>

                    <div class="faq-item">
                        <h5 class="faq-question" onclick="toggleFaq(this)">
                            Is it safe to share personal information and reports with the doctors?
                            <i class="fas fa-chevron-down float-right"></i>
                        </h5>
                        <div class="faq-answer">
                            <p>All consultations at My Nutrify are 100% Private, the medical information shared by the customers shall not be shared to others without the consent of the customer.</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <?php include("components/footer.php"); ?>

    <!-- Booking Modal -->
    <div class="modal fade" id="bookingModal" tabindex="-1" role="dialog" aria-labelledby="bookingModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-lg" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="bookingModalLabel">Book Consultation</h5>
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                        <span aria-hidden="true">&times;</span>
                    </button>
                </div>
                <div class="modal-body">
                    <form id="consultationBookingForm">
                        <input type="hidden" id="selectedDoctorId" name="doctorId">
                        <input type="hidden" id="selectedConsultationFee" name="consultationFee">

                        <div class="row">
                            <div class="col-12 mb-3">
                                <h6>Selected Doctor: <span id="selectedDoctorName" class="text-primary"></span></h6>
                                <p>Consultation Fee: ₹<span id="displayConsultationFee"></span></p>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="patientName" class="form-label">Full Name *</label>
                                <input type="text" class="form-control" id="patientName" name="patientName" required>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="patientEmail" class="form-label">Email Address *</label>
                                <input type="email" class="form-control" id="patientEmail" name="patientEmail" required>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="patientPhone" class="form-label">Phone Number *</label>
                                <input type="tel" class="form-control" id="patientPhone" name="patientPhone" required>
                            </div>
                            <div class="col-md-3 mb-3">
                                <label for="patientAge" class="form-label">Age *</label>
                                <input type="number" class="form-control" id="patientAge" name="patientAge" min="1" max="120" required>
                            </div>
                            <div class="col-md-3 mb-3">
                                <label for="patientGender" class="form-label">Gender *</label>
                                <select class="form-control" id="patientGender" name="patientGender" required>
                                    <option value="">Select Gender</option>
                                    <option value="Male">Male</option>
                                    <option value="Female">Female</option>
                                    <option value="Other">Other</option>
                                </select>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="preferredDate" class="form-label">Preferred Date *</label>
                                <input type="date" class="form-control" id="preferredDate" name="preferredDate" required>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="preferredTime" class="form-label">Preferred Time *</label>
                                <select class="form-control" id="preferredTime" name="preferredTime" required>
                                    <option value="">Select Time</option>
                                </select>
                            </div>
                        </div>

                        <div class="mb-3">
                            <label for="healthConcerns" class="form-label">Health Concerns / Symptoms *</label>
                            <textarea class="form-control" id="healthConcerns" name="healthConcerns" rows="4"
                                      placeholder="Please describe your health concerns, symptoms, or reason for consultation" required></textarea>
                        </div>

                        <div class="mb-3">
                            <label for="currentMedications" class="form-label">Current Medications (if any)</label>
                            <textarea class="form-control" id="currentMedications" name="currentMedications" rows="3"
                                      placeholder="List any medications you are currently taking"></textarea>
                        </div>

                        <div class="text-center">
                            <button type="submit" class="btn btn-primary btn-lg">
                                Proceed to Payment (₹<span id="finalConsultationFee"></span>)
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <!-- Scripts -->
    <script src="js/jquery-3.6.0.min.js"></script>
    <script src="js/bootstrap.min.js"></script>
    <script src="js/custom.js"></script>

    <script>
        // Set minimum date to today
        document.getElementById('preferredDate').min = new Date().toISOString().split('T')[0];

        // FAQ Toggle Function
        function toggleFaq(element) {
            const answer = element.nextElementSibling;
            const icon = element.querySelector('i');

            if (answer.style.display === 'block') {
                answer.style.display = 'none';
                icon.classList.remove('fa-chevron-up');
                icon.classList.add('fa-chevron-down');
            } else {
                answer.style.display = 'block';
                icon.classList.remove('fa-chevron-down');
                icon.classList.add('fa-chevron-up');
            }
        }

        // Book Consultation Function
        function bookConsultation(doctorId, doctorName, consultationFee) {
            document.getElementById('selectedDoctorId').value = doctorId;
            document.getElementById('selectedDoctorName').textContent = doctorName;
            document.getElementById('selectedConsultationFee').value = consultationFee;
            document.getElementById('displayConsultationFee').textContent = consultationFee;
            document.getElementById('finalConsultationFee').textContent = consultationFee;

            // Load available time slots for the selected doctor
            loadTimeSlots(doctorId);

            $('#bookingModal').modal('show');
        }

        // Load Time Slots Function
        function loadTimeSlots(doctorId) {
            const dateInput = document.getElementById('preferredDate');
            const timeSelect = document.getElementById('preferredTime');

            dateInput.addEventListener('change', function() {
                if (this.value) {
                    // Clear existing options
                    timeSelect.innerHTML = '<option value="">Loading...</option>';

                    // Fetch available slots via AJAX
                    fetch('get_available_slots.php', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                        },
                        body: JSON.stringify({
                            doctorId: doctorId,
                            date: this.value
                        })
                    })
                    .then(response => response.json())
                    .then(data => {
                        timeSelect.innerHTML = '<option value="">Select Time</option>';

                        if (data.success && data.slots.length > 0) {
                            data.slots.forEach(slot => {
                                const option = document.createElement('option');
                                option.value = slot.time;
                                option.textContent = slot.display_time;
                                timeSelect.appendChild(option);
                            });
                        } else {
                            timeSelect.innerHTML = '<option value="">No slots available</option>';
                        }
                    })
                    .catch(error => {
                        console.error('Error:', error);
                        timeSelect.innerHTML = '<option value="">Error loading slots</option>';
                    });
                }
            });
        }

        // Form Submission
        document.getElementById('consultationBookingForm').addEventListener('submit', function(e) {
            e.preventDefault();

            const formData = new FormData(this);

            // Show loading
            Swal.fire({
                title: 'Processing...',
                text: 'Please wait while we process your booking',
                allowOutsideClick: false,
                didOpen: () => {
                    Swal.showLoading();
                }
            });

            fetch('process_consultation_booking.php', {
                method: 'POST',
                body: formData
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    Swal.fire({
                        icon: 'success',
                        title: 'Booking Successful!',
                        text: 'Your consultation has been booked. You will receive a confirmation email shortly.',
                        confirmButtonText: 'OK'
                    }).then(() => {
                        $('#bookingModal').modal('hide');
                        this.reset();

                        // Redirect to payment if needed
                        if (data.paymentUrl) {
                            window.location.href = data.paymentUrl;
                        }
                    });
                } else {
                    Swal.fire({
                        icon: 'error',
                        title: 'Booking Failed',
                        text: data.message || 'Something went wrong. Please try again.',
                        confirmButtonText: 'OK'
                    });
                }
            })
            .catch(error => {
                console.error('Error:', error);
                Swal.fire({
                    icon: 'error',
                    title: 'Error',
                    text: 'Something went wrong. Please try again.',
                    confirmButtonText: 'OK'
                });
            });
        });
    </script>
</body>
</html>
