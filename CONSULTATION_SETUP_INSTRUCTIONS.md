# Doctor Consultation System Setup Instructions

## 🏥 Overview
This document provides step-by-step instructions to set up the Doctor Consultation Booking System for My Nutrify, similar to Krishna Ayurveda's consultation system.

## 📋 Prerequisites
- MySQL database (my_nutrify_db)
- PHP with MySQLi extension
- Existing My Nutrify website structure

## 🗄️ Database Setup

### Step 1: Create Database Tables
Run the following SQL commands in your MySQL database (`my_nutrify_db`):

```sql
-- You can copy and paste these commands from consultation_database_setup.sql
-- Or run the entire file using: mysql -u root -p my_nutrify_db < consultation_database_setup.sql

-- 1. Doctors Table
CREATE TABLE IF NOT EXISTS doctors (
    DoctorId INT AUTO_INCREMENT PRIMARY KEY,
    DoctorName VARCHAR(100) NOT NULL,
    Qualification VARCHAR(200) NOT NULL,
    Specialization VARCHAR(200) NOT NULL,
    Experience INT NOT NULL COMMENT 'Years of experience',
    PhotoPath VARCHAR(255) DEFAULT NULL,
    Description TEXT DEFAULT NULL,
    ConsultationFee DECIMAL(10,2) DEFAULT 200.00,
    PhoneNumber VARCHAR(15) NOT NULL,
    Email VARCHAR(100) DEFAULT NULL,
    IsActive TINYINT(1) DEFAULT 1,
    CreatedAt TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    UpdatedAt TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- 2. Doctor Availability Table
CREATE TABLE IF NOT EXISTS doctor_availability (
    AvailabilityId INT AUTO_INCREMENT PRIMARY KEY,
    DoctorId INT NOT NULL,
    DayOfWeek ENUM('Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday', 'Sunday') NOT NULL,
    StartTime TIME NOT NULL,
    EndTime TIME NOT NULL,
    SlotDuration INT DEFAULT 30 COMMENT 'Duration in minutes',
    IsActive TINYINT(1) DEFAULT 1,
    CreatedAt TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (DoctorId) REFERENCES doctors(DoctorId) ON DELETE CASCADE
);

-- 3. Consultation Bookings Table
CREATE TABLE IF NOT EXISTS consultation_bookings (
    BookingId INT AUTO_INCREMENT PRIMARY KEY,
    BookingNumber VARCHAR(20) UNIQUE NOT NULL,
    DoctorId INT NOT NULL,
    PatientName VARCHAR(100) NOT NULL,
    PatientEmail VARCHAR(100) NOT NULL,
    PatientPhone VARCHAR(15) NOT NULL,
    PatientAge INT NOT NULL,
    PatientGender ENUM('Male', 'Female', 'Other') NOT NULL,
    HealthConcerns TEXT NOT NULL,
    CurrentMedications TEXT DEFAULT NULL,
    PreferredDate DATE NOT NULL,
    PreferredTime TIME NOT NULL,
    ConsultationDate DATE NOT NULL,
    ConsultationTime TIME NOT NULL,
    ConsultationFee DECIMAL(10,2) NOT NULL,
    PaymentStatus ENUM('Pending', 'Paid', 'Failed', 'Refunded') DEFAULT 'Pending',
    PaymentId VARCHAR(100) DEFAULT NULL,
    BookingStatus ENUM('Pending', 'Confirmed', 'Completed', 'Cancelled', 'Rescheduled') DEFAULT 'Pending',
    ConsultationMode ENUM('Phone', 'Video', 'In-Person') DEFAULT 'Phone',
    Notes TEXT DEFAULT NULL,
    CreatedAt TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    UpdatedAt TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (DoctorId) REFERENCES doctors(DoctorId) ON DELETE RESTRICT
);

-- 4. Consultation History Table
CREATE TABLE IF NOT EXISTS consultation_history (
    HistoryId INT AUTO_INCREMENT PRIMARY KEY,
    BookingId INT NOT NULL,
    DoctorId INT NOT NULL,
    PatientName VARCHAR(100) NOT NULL,
    PatientPhone VARCHAR(15) NOT NULL,
    ConsultationDate DATE NOT NULL,
    ConsultationTime TIME NOT NULL,
    Duration INT DEFAULT NULL COMMENT 'Duration in minutes',
    Diagnosis TEXT DEFAULT NULL,
    Prescription TEXT DEFAULT NULL,
    RecommendedProducts TEXT DEFAULT NULL,
    FollowUpRequired TINYINT(1) DEFAULT 0,
    FollowUpDate DATE DEFAULT NULL,
    DoctorNotes TEXT DEFAULT NULL,
    PatientFeedback TEXT DEFAULT NULL,
    Rating INT DEFAULT NULL COMMENT 'Rating from 1-5',
    CreatedAt TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (BookingId) REFERENCES consultation_bookings(BookingId) ON DELETE CASCADE,
    FOREIGN KEY (DoctorId) REFERENCES doctors(DoctorId) ON DELETE RESTRICT
);

-- 5. Consultation Slots Table
CREATE TABLE IF NOT EXISTS consultation_slots (
    SlotId INT AUTO_INCREMENT PRIMARY KEY,
    DoctorId INT NOT NULL,
    SlotDate DATE NOT NULL,
    SlotTime TIME NOT NULL,
    Duration INT DEFAULT 30 COMMENT 'Duration in minutes',
    IsBooked TINYINT(1) DEFAULT 0,
    BookingId INT DEFAULT NULL,
    IsActive TINYINT(1) DEFAULT 1,
    CreatedAt TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (DoctorId) REFERENCES doctors(DoctorId) ON DELETE CASCADE,
    FOREIGN KEY (BookingId) REFERENCES consultation_bookings(BookingId) ON DELETE SET NULL,
    UNIQUE KEY unique_slot (DoctorId, SlotDate, SlotTime)
);

-- 6. Consultation Settings Table
CREATE TABLE IF NOT EXISTS consultation_settings (
    SettingId INT AUTO_INCREMENT PRIMARY KEY,
    SettingKey VARCHAR(50) UNIQUE NOT NULL,
    SettingValue TEXT NOT NULL,
    Description TEXT DEFAULT NULL,
    UpdatedAt TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);
```

### Step 2: Insert Default Settings and Sample Data
```sql
-- Insert default settings
INSERT INTO consultation_settings (SettingKey, SettingValue, Description) VALUES
('default_consultation_fee', '200.00', 'Default consultation fee in INR'),
('default_slot_duration', '30', 'Default consultation slot duration in minutes'),
('advance_booking_days', '30', 'How many days in advance can patients book'),
('consultation_start_time', '09:00:00', 'Default consultation start time'),
('consultation_end_time', '18:00:00', 'Default consultation end time'),
('phone_consultation_enabled', '1', 'Enable phone consultations'),
('video_consultation_enabled', '0', 'Enable video consultations'),
('booking_confirmation_sms', '1', 'Send SMS confirmation for bookings'),
('booking_confirmation_email', '1', 'Send email confirmation for bookings'),
('reminder_sms_hours', '24', 'Send reminder SMS X hours before consultation'),
('cancellation_hours', '24', 'Minimum hours before consultation to allow cancellation');

-- Insert sample doctors
INSERT INTO doctors (DoctorName, Qualification, Specialization, Experience, Description, PhoneNumber, Email) VALUES
('Dr. Rajesh Kumar', 'BAMS, MD (Ayurveda)', 'General Ayurvedic Medicine, Digestive Disorders', 15, 'Experienced Ayurvedic practitioner specializing in digestive wellness and general health consultation. Expert in traditional Ayurvedic treatments and herbal medicine.', '**********', '<EMAIL>'),
('Dr. Priya Sharma', 'BAMS, MS (Ayurveda)', 'Women\'s Health, Skin & Hair Care', 12, 'Specialist in women\'s health issues, skin care, and hair problems. Provides comprehensive Ayurvedic solutions for hormonal imbalances and beauty concerns.', '**********', '<EMAIL>'),
('Dr. Amit Patel', 'BAMS, PhD (Ayurveda)', 'Diabetes, Cardiac Wellness, Weight Management', 18, 'Senior consultant specializing in lifestyle disorders like diabetes, heart problems, and weight management through Ayurvedic principles and dietary guidance.', '**********', '<EMAIL>');

-- Insert sample availability (Monday to Saturday, 9 AM to 6 PM)
INSERT INTO doctor_availability (DoctorId, DayOfWeek, StartTime, EndTime, SlotDuration) VALUES
-- Dr. Rajesh Kumar (DoctorId: 1)
(1, 'Monday', '09:00:00', '18:00:00', 30),
(1, 'Tuesday', '09:00:00', '18:00:00', 30),
(1, 'Wednesday', '09:00:00', '18:00:00', 30),
(1, 'Thursday', '09:00:00', '18:00:00', 30),
(1, 'Friday', '09:00:00', '18:00:00', 30),
(1, 'Saturday', '09:00:00', '15:00:00', 30),
-- Dr. Priya Sharma (DoctorId: 2)
(2, 'Monday', '10:00:00', '17:00:00', 30),
(2, 'Tuesday', '10:00:00', '17:00:00', 30),
(2, 'Wednesday', '10:00:00', '17:00:00', 30),
(2, 'Thursday', '10:00:00', '17:00:00', 30),
(2, 'Friday', '10:00:00', '17:00:00', 30),
(2, 'Saturday', '10:00:00', '14:00:00', 30),
-- Dr. Amit Patel (DoctorId: 3)
(3, 'Monday', '11:00:00', '19:00:00', 30),
(3, 'Tuesday', '11:00:00', '19:00:00', 30),
(3, 'Wednesday', '11:00:00', '19:00:00', 30),
(3, 'Thursday', '11:00:00', '19:00:00', 30),
(3, 'Friday', '11:00:00', '19:00:00', 30),
(3, 'Saturday', '11:00:00', '16:00:00', 30);
```

## 🚀 Testing the System

### Step 3: Access the Consultation Page
1. Open your browser and go to: `http://localhost/nutrify/doctor_consultation.php`
2. You should see the consultation page with 3 sample doctors
3. Click "Book Consultation" on any doctor to test the booking form

### Step 4: Access the Admin Panel
1. Go to: `http://localhost/nutrify/cms/consultation_admin.php`
2. You can manage doctors, view bookings, and update consultation settings

## 📱 Features Included

✅ **Patient Booking System**
- Doctor selection with profiles
- Date and time slot booking
- Patient information form
- Health concerns and symptoms
- Real-time slot availability

✅ **Admin Management Panel**
- Dashboard with statistics
- Booking management
- Doctor management
- Settings configuration

✅ **Navigation Integration**
- "Consult by Doctor" link added to main menu
- Mobile-responsive navigation

✅ **Database Structure**
- Complete consultation system tables
- Sample doctors and availability
- Booking tracking and history

## 🔧 Customization Options

- **Consultation Fees**: Modify in `consultation_settings` table
- **Doctor Availability**: Update `doctor_availability` table
- **Add More Doctors**: Use admin panel or insert into `doctors` table
- **Styling**: Modify CSS in `doctor_consultation.php`

## 📞 Support

The system is designed to match Krishna Ayurveda's consultation model with:
- ₹200 consultation fee
- Phone consultation mode
- 24-hour medicine dispatch
- Professional doctor profiles
- Comprehensive booking system

Once the database is set up, the system will be fully functional for booking doctor consultations!
